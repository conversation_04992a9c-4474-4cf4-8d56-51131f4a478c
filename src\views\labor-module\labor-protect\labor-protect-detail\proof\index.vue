<!--
 * @Description: 证据材料--新增
 * @Version: 0.1
 * @Autor: yc
-->
<template>
  <div class="labor-protect-writ common-container">
    <apply-info ref="applyInfo" v-show="active === 0" v-model="proofList" :pageType="pageType" :formIndex="formIndex" @handleNext="handleNext" @handleAdd="handleAdd" @handleMaterials="handleMaterials"></apply-info>
         <!-- 提交材料 -->
    <submit-material ref="submitMaterial" v-show="active === 1" :active="active" :pageType="pageType" :materialId="materialId" @handleNext="handleNext" @handleSave="handleSave" @handleComfirm="handleComfirm"></submit-material>

 </div>
</template>

<script>
import ApplyInfo from "./cpns/apply-info"
import SubmitMaterial from "./cpns/submit-material"
import {commonApi} from "@/api"

export default {
  name: "labor-protect-writ",
  components: {
    ApplyInfo,
    SubmitMaterial
  },
  data() {
    return {
      active: 0,  
      formIndex: 0,
      formData: {
      },

      successSubmit: false, //是否提交成功
      signatureData: "", //签名base64

      successText: "保存成功",
      isSuccess: false,
      proofList: [],
      materialId: "",
      pageType: ""      
    }
  },
  watch: {
    active() {
      this.scrollToTop(0, 0, "auto")
    }
  },
  computed: {
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    },
    primaryKey() {
      return this.$route.query.bcz003 || ""
    }
  },
  created() {
    // 初始化表单数据
    this.getBc22List(this.primaryKey)
  },
  mounted() {   
    if (window.history && window.history.pushState) {
      // 往历史记录里面添加一条新的当前页面的url
      history.pushState(null, null, document.URL)
      // 给 popstate 绑定一个方法用来监听页面返回
      window.addEventListener("popstate", this.backFn, false) //false阻止默认事件
    } 
    // if (window.history && window.history.pushState) {
    //   if (window.history.length>1){
    //     const state = {
    //       key: Math.random() * new Date().getTime()
    //     }
    //     window.history.pushState(state, null, document.URL)
    //   }
      
    //   //给window添加一个监听事件popstate，拦截返回键，并执行方法 backFn
    //   window.addEventListener("popstate", this.backFn, false)
    // }
  },
  methods: {
    // 下一步
    handleNext(active) {
      this.active = active
      this.scrollToTop(0, 0, "auto")
    },
    // 更新表单数据
    updateFormData(data) {
      this.formData = { ...this.formData, ...data }
    },
    // 拦截返回键
    backFn() {
      if (this.active !== 0) {
        this.active = 0
        this.scrollToTop(0, 0, "auto")
      } else {
        this.$router.go(-1)   
      }
    },
    // 保存
    handleSave(type) {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定保存信息为待核实状态",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        const zjcl00 = this.$refs.submitMaterial.materialNum
        const bcz003 = this.primaryKey 
        const mapObj = {
          "info": {...this.proofList[this.formIndex], bcz003},
          "material": {...this.proofList[this.formIndex], zjcl00, bcz003}
        }
        if (!this.proofList[this.formIndex].zjmc00){
          this.$dialog.alert({
            title: "温馨提示",
            message: "请输入证据材料"+(this.formIndex+1)+"的证据名称！",  
            theme: "round-button",
            showConfirmButton: true
          })
          this.active = 0
          return
        }
        if (!this.proofList[this.formIndex].zjlx00){
          this.$dialog.alert({
            title: "温馨提示",
            message: "请选择证据材料"+(this.formIndex+1)+"的类型！",  
            theme: "round-button",
            showConfirmButton: true
          })
          this.active = 0
          return
        }
        if (!this.proofList[this.formIndex].slcl00){
          this.$dialog.alert({
            title: "温馨提示",
            message: "请输入证据材料"+(this.formIndex+1)+"的数量！",  
            theme: "round-button",
            showConfirmButton: true
          })
          this.active = 0
          return
        }
        if (!this.proofList[this.formIndex].dwcl00){
          this.$dialog.alert({
            title: "温馨提示",
            message: "请选择证据材料"+(this.formIndex+1)+"的单位！",  
            theme: "round-button",
            showConfirmButton: true
          })
          this.active = 0
          return
        }
        const params = mapObj[type]
        delete params.createTime
        delete params.updateTime
        params.serviceName = "xytBc03Web_addBc22"
        commonApi.proxyApi(params).then((res) => {
          this.$toast("保存成功！")
          this.handleNext(0)
          this.getBc22List(this.primaryKey)
        })
      }).catch(() => { })   
    },
    handleComfirm() {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定核验该材料?",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        const mapObj = {...this.proofList[this.formIndex]}
        if (!this.proofList[this.formIndex].zjclid){
          this.$dialog.alert({
            title: "温馨提示",
            message: "请完善证据材料"+(this.formIndex+1)+"！",  
            theme: "round-button",
            showConfirmButton: true
          })
          this.active = 0
          return
        }
        const params = mapObj
        delete params.createTime
        delete params.updateTime
        params.serviceName = "xytBc03Web_modifyBc22"
        commonApi.proxyApi(params).then((res) => {
          this.$toast("保存成功！")
          this.handleNext(0)
          this.getBc22List(this.primaryKey)
        })
      }).catch(() => { })   
    },
    // 查询案件信息
    getBc22List(bcz003) {      
      const params = {
        serviceName: "xytBc03Web_getBc22List",
        bcz003
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "案件信息")
        const {data} = res.map
        // for (const key in data) {
        //   data[key] === null && (data[key] = "")
        // }

        this.proofList = data        
      })
    },
    handleAdd(){
      const proofObj= {
        "cjsj00": "",
        "dwcl00": "",
        "slcl00": 0,
        "status": 0,
        "tjrxm0": "",
        "zjcl00": "",
        "zjclid": 0,
        "zjlx00": "",
        "zjmc00": ""
      }
      this.proofList.unshift(proofObj)
    },
    handleMaterials(item){
      if (this.proofList[item].status == 1) {
        this.pageType = "detail"
      } else {
        this.pageType = ""
      }
      this.$refs.submitMaterial.getArchivesPhoneData(this.proofList[item].zjcl00, this.pageType)
      this.formIndex = item
      this.active = 1
      this.scrollToTop(0, 0, "auto")
    }
  },
  destroyed(){
    //销毁监听
    window.removeEventListener("popstate", this.backFn, false)
  }
}
</script>

<style lang="less" scoped>

</style>