/*
 * @Description: 设置Cookies
 * @Version: 0.1
 * @Autor: Chenyt
 */

import Cookies from "js-cookie"

const ZhrsTokenKey = process.env.VUE_APP_ZHRS_TOKEN // 智慧人社token
const TokenKey = process.env.VUE_APP_TOKEN // 业务token
const AuthTokenKey = process.env.VUE_APP_AUTH_TOKEN // 业务token

const ls = window.sessionStorage

// 智慧人社token
export function getZhrsToken() {
  return JSON.parse(ls.getItem(ZhrsTokenKey))
}

export function setZhrsToken(token) {
  return ls.setItem(ZhrsTokenKey, JSON.stringify(token))
}

export function removeZhrsToken() {
  return Cookies.remove(ZhrsTokenKey)
}

// 业务token
export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

// 网关token
export function getAuthToken() {
  return Cookies.get(AuthTokenKey)
}

export function setAuthToken(token) {
  return Cookies.set(AuthTokenKey, token)
}

export function removeAuthToken() {
  return Cookies.remove(AuthTokenKey)
}

// 请求头 ylzins-token
export function getYlzinsToken() {
  return ls.getItem("ylzins-token")
}

export function setYlzinsToken(token) {
  return ls.setItem("ylzins-token", token)
}

// 是否加密
export function setEncryptEnabled(token) {
  return ls.setItem("encryptEnabled", token)
}

export function getEncryptEnabled() {
  return ls.getItem("encryptEnabled")
}

// 虚拟人欢迎语
export function setWelcomeText(text) {
  return ls.setItem("welcomeText", text)
}

export function getWelcomeText() {
  return ls.getItem("welcomeText")
}

// 虚拟人对话框
export function setMessages(type, list) {
  return ls.setItem(`${type}-messages`, list)
}


export function getMessages(type) {
  return ls.getItem(`${type}-messages`)
}
