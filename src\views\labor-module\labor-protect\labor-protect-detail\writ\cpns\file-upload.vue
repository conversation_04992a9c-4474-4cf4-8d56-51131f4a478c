<!--
 * @Description: 文书/笔录材料
 * @Version: 0.1
 * @Autor: yc
-->

<template>
  <div class="submit-material">

    <div class="iframe-box">
      <van-cell-group inset>
        <y-title class="topTitle" content="文书文档" fontContSize="16" titleDes="（支持上传.docx文件，且只能上传一个文件)" fontSizeDes="12"
          :colorDes="fourTextColor" mButtom="16" />
        <van-uploader v-model="fileList" :max-count="1" accept=".docx" :after-read="afterRead">
          <!-- <van-button  type="primary">上传文件</van-button> -->
        </van-uploader>
      </van-cell-group>
      <div class="btn-box flex-c-e border-bottom-wide ">
        <van-button type="primary" @click="handleUpload()">文书上传</van-button>
      </div>
    </div>

  </div>
</template>

<script>
import { four_text_color } from "@/styles/theme/theme-params.less"
export default {
  name: "file-upload",
  props: {
    writBase64Data: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      tipsList: ["上传材料后，务必点击保存或提交按钮，上传的材料才会保存。"],

      writBase64: "", //材料编号
      iframeUrl: "", //上传材料页面地址
      fourTextColor: four_text_color,
      fileList: []
    }
  },
  computed: {
    pageType() {
      return this.$route.query.pageType || ""
    }
  },
  created() {
  },
  methods: {
    // 保存 存储数据 返回
    handleUpload() {
      this.$emit("handleSave", "material")
    },
    // 上一步
    handleBack() {
      this.$emit("handleNext", 0)
    },
    afterRead(file) {
      console.log(this.fileList, "文件列表")
      console.log(file, "文件")
      // 创建FileReader对象
      const reader = new FileReader()

      // 文件读取成功后执行的回调函数
      reader.onload = (e) => {
        console.log(e.target.result, "文件内容")
        // 获取文件的Base64字符串
        const base64 = e.target.result
        this.writBase64 = base64
        // 处理Base64字符串，例如上传到服务器等
      }

      // 以DataURL的形式读取文件内容
      reader.readAsDataURL(file.file)
    }
  }
}
</script>

<style lang="less" scoped>
.topTitle {
  padding-top: 14px;
}

.iframe-box {
  height: calc(100vh);
  width: 100vw;
  background-color: #f6f6f6;
  overflow: hidden;

  .my-iframe {
    width: 200%;
    height: 200%;
    transform: scale(0.47);
    transform-origin: 0 0;
    margin-left: 12px;
  }
}

.search-field {
  /deep/.van-field__control {
    text-align: left;
  }
}

.btn-box {
  // height: 46px;
  padding: 9px 14px 9px 0px;
  box-sizing: border-box;
  background-color: #FFFFFF;

  .van-button {
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 28px;
    width: 80px;
    height: 28px;
    padding: 0;
    border-radius: 14px;
    margin-left: 12px;
  }
}

.top-div {
  height: 30px;

  .right-box {
    position: absolute;
    right: 0;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    .van-button {
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 28px;
      width: 80px;
      height: 28px;
      padding: 0;
      border-radius: 14px;
      margin-left: 12px;
    }
  }
}

.base-form {
  .right-box {
    position: absolute;
    right: 0;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    &>.point {
      width: 8px;
      height: 8px;
      border-radius: 4px;
      display: inline-block;
      margin-bottom: 1px;
    }

    &>.text {
      margin-left: 6px;
    }
  }
}

.evaluate-popup {
  width: 80% !important;
  border-radius: 8px;

  .evaluate-box {
    .evaluate-title {
      text-align: left;
      font-size: 16px;
      margin: 16px 0;
      padding-left: 16px;
      font-weight: 600;
    }

    /deep/.y-title {
      margin-bottom: 0px !important;
    }

    .evaluate-button {
      padding: 12px 0;

      .van-button--primary {
        margin-left: 8px;
      }
    }

    .online-revoke-icon {
      display: flex;
      justify-content: center;
      color: #32ae57;
      font-size: 65px;
    }

    .online-revoke-icon {
      display: flex;
      justify-content: center;
      color: #32ae57;
      font-size: 65px;
    }

    .online-revoke-text {
      font-size: 12px;
      text-align: center;
      margin-bottom: 16px;
      margin-top: 4px;
    }

    /deep/.van-radio-group {
      padding: 4px 16px;
      font-size: 14px;
    }

    /deep/.van-field__control {
      text-align: right;
    }
  }
}
</style>