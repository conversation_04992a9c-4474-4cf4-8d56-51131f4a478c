<!--
 * @Description: 工作台-头部
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="header-block flex-c-c">
    <div class="unit-card">
      <div class="unit-name xz-ellipsis-2">
        {{ baseInfo.dwmc00 }}
      </div>
      <div class="user-info">
        <span class="user-name">{{ baseInfo.aac003  }}</span>
        <span class="user-phone">{{ baseInfo.aae005}}</span>
      </div>
      <div class="job-number">
        <span class="text">工号</span>
        <span class="number">{{ baseInfo.rygh00 }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      baseInfo: {}
    }
  },
  created() {
    this.$bus.$on("getAction", this.onAction)
    this.$once("hook:beforeDestroy", () => {
      this.$bus.$off("getAction")
    })
  },
  methods: {
    onAction(data) {
      console.log(data, "data==========")
      this.baseInfo = data
      
    }
  }
}
</script>

<style lang="less" scoped>
.header-block {
  width: 100vw;
  height: 182px;
  background-image: url("~@pic/yilubao/palmar-distributor/<EMAIL>");
  background-repeat: no-repeat;
  background-size: contain;
  padding: 22px 16px 0;
  .unit-card {
    width: 100%;
    height: 160px;
    background-image: url("~@pic/yilubao/palmar-distributor/<EMAIL>");
    background-repeat: no-repeat;
    background-size: contain;  
    padding: 16px 30px 0;
    .unit-name {
      font-weight: 600;
      font-size: 18px;
      color: #19AD6E;
      line-height: 25px;
      text-align: center;
      font-style: normal;
    }  
    .user-info {
      margin-top: 20px;
      line-height: 22px;
      .user-name {
        font-weight: bold;
        font-size: 16px;
        color: #000000;
      }
      .user-phone {
        font-size: 14px;
        color: #333333;
        margin-left: 12px;
      }
    }
    .job-number {
      line-height: 18px;
      .text {
        font-size: 14px;
        color: #333333;
      }
      .number {
        font-weight: bold;
        font-size: 14px;
        color: #000000;
        margin-left: 12px;
      }
    }
  }
}
</style>