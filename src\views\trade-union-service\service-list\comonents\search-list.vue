<!--
 * @Description: 查询列表
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="search-list">
    <div v-if="list.length > 0">
      <stage-box v-for="item in list" :key="item.id" :stageInfo="item"></stage-box>
    </div>
    <y-empty v-else></y-empty>
  </div>
</template>

<script>
import StageBox from "@/components/business/stage-box"
import {commonApi} from "@api"

export default {
  name: "search-list",
  components: {
    StageBox
  },
  props: {
    searchsearchParams: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      list: [
        {
          id: 1,
          name: "暖新地图"
        },
        {
          id: 2,
          name: "暖新地图" 
        },
        {
          id: 3,
          name: "暖新地图"
        }
      ]
    }
  },
  watch: {
    searchsearchParams: {
      handler(val) {
        this.getList(val) 
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getList(data) {
      const params = {
        serviceName: "xytHwyz_findDa12MoreList",
        ...data
      }
      commonApi
        .proxyApi(params)
        .then(res => {
          console.log(res, "res666")
          this.list = res.map.data || []
        })
    }
  }
}
</script>
<style lang="less" scoped>
.search-list {
  padding: 12px 16px;
  background: #F6F6F6;
  min-height: calc(100vh - 106px);
  .stage-box {
    margin-bottom: 8px;
  }
}
</style>