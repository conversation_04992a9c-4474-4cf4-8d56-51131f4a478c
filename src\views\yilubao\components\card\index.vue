<!--
 * @Description: 卡片
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="card-box-wrapper" @click="handleClick">
    <div class="card-box">
      <div class="row">
        <div class="title mr-8">{{ title }}</div>
        <div class="tag mr-12">
          <slot name="label"></slot>
        </div>
        <div class="icon mr-4" v-if="allowEdit">编辑</div>
        <div class="right-icon" v-if="allowDelete">
          <img src="@pic/yilubao/icons/<EMAIL>" alt="">
        </div>
      </div>
      <div class="row">
        <div class="label">身份证号：</div>
        <div class="label-content">{{ idNum }}</div>
      </div>
      <slot></slot>
    </div>
</div>
</template>

<script>
export default {
  name: "card-box",
  props: {
    allowEdit: {
      type: Boolean,
      default: true
    },
    allowDelete: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ""
    },
    idNum: {
      type: String,
      default: ""
    }
  },
  methods: {
    handleClick(){
      this.$emit("showDetail", this.$attrs.info)
    }
  }
}
</script>

<style scoped lang="less">
.mr {
  &-4 {
    margin-right: 4px;
  }
  &-8 {
    margin-right: 8px;
  }
  &-12 {
    margin-right: 12px;
  }
}
.card-box-wrapper {
  background: #FFFFFF;
  padding-bottom: 20px;
  // border: 1px solid #E0E1E6;
  &:last-child {
    padding-bottom: 0;
  }
}
.card-box {
  padding: 20px 16px 8px;
  background: #FFFFFF;
  box-shadow: 0px 0px 8px 0px #E0E1E6;
  border-radius: 12px;
  .row {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    position: relative;
    top: 0;
    left: 0;
    margin-bottom: 12px;
    .right-icon {
      position: absolute;
      right: 0;
      top: 0;
      img {
        width: 16px;
        height: 16px;
        transform: translateY(-50%);
      }
    }
    .label, .label-content {
      font-size: 14px;
      color: @main_text_color;
    }
    .title {
      font-size: 16px;
      color: @main_text_color;
    }
    .tag, .icon {
      font-size: 12px;
    }
    .icon {
      color: @six_text_color;
    }
    
  }
}
</style>