<!--
 * @Description: 查看容器
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="view-container mt16">
    <div :class="['view-box', {'view-box-retract': isRetract,'view-box-retract-short': (pageSize === 'mini' && isRetract)}]">
      <!-- 投保状态 -->
      <div class="status-box" :class="'status-box' + viewStatus" v-if="status">
        {{ status }}
      </div>

      <!-- 标题 -->
      <div class="title-box" v-if="title">
        <span>{{ title }}</span>
        <span class="split-line"></span>
        <slot name="title"></slot>
      </div>

      <!-- 表单内容 -->
      <slot name="content"></slot>

      <!-- 展开收起 -->
      <div class="flexible-box flex-c-c" @click="isRetract = !isRetract">
        <span>{{ isRetract ? '展开' : '收起' }}</span>
        <span class="icon"><img :src="isRetract ? closeIconUrl : openIconUrl" alt=""></span>
      </div>
    </div>

    <!-- 其它按钮 -->
    <div class="expand-box flex-c-sb" v-if="viewStatus === '001' && showBtn">
      <van-button size="small" round @click="handleCancel">取消订单</van-button>
      <van-button size="small" round type="primary" @click="handlePay">去支付</van-button>
    </div>
  </div>

</template>

<script>

const statusInfo = {
  "0": "全部",
  "001": "待支付",
  "002_1": "保障中",
  "002_2": "已终止",
  "003": "已取消"
}

export default {
  name: "y-view-container",
  props: {
    viewStatus: { //查看状态 ==》 001待支付；002保障中；003已终止 //0全部 1待支付 2保障中 3已终止
      type: String,
      default: "0"
    },
    pageSize: {
      type: String,
      default: "small"
    },
    title: {
      type: String,
      default: ""
    },
    showBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isRetract: true, //是否收起
      closeIconUrl: require("@/assets/imgs/yilubao/icons/<EMAIL>"),
      openIconUrl: require("@/assets/imgs/yilubao/icons/<EMAIL>")
    }
  },
  computed: {
    status() {
      return statusInfo[this.viewStatus] || ""
    }
  },
  methods: {
    handlePay() {
      this.$emit("handlePay")
    },
    handleCancel() {
      this.$emit("handleCancel")
    }
  }
}
</script>

<style lang="less" scoped>
.view-container {
  background: @white_text_color;
  box-shadow: 0 0 8px 0 @white_shadow_color;
  border-radius: 12px;
  .view-box {
    padding: 20px 16px 16px;
    width: 100%;
    position: relative;
    &-retract {
      height: 276px;
      overflow: hidden;
      position: relative;
      .flexible-box {
        position: absolute;
        bottom: 0;
        width: calc(100% - 32px);
        height: 40px;
        background: @white_text_color;
      }
    }
    &-retract-short {
      height: 220px;
    }
    .title-box {
      font-size: 15px;
      font-weight: bold;
      color: @main_text_color;
      line-height: 22px;
      border-bottom: 1px solid @border_bottom_color;
      padding-bottom: 16px;
      .split-line {
        width: 1px;
        height: 14px;
        background: @split_line_color;
        margin: 0 6px;
        display: inline-block;
      }
    }
    .flexible-box {
      font-size: 12px;
      color: @seven_text_color;
      line-height: 18px;
      .icon {
        margin-left: 4px;
        display: inline-block;
        width: 8px;
        height: 4px;
        position: relative;
        & > img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }
      }
    }

    .status-box {
      width: 60px;
      height: 26px;
      position: absolute;
      top: 0;
      right: 0;
      border-top-right-radius: 12px;
      border-bottom-left-radius: 12px;
      font-weight: bold;
      font-size: 14px;
      line-height: 26px;
      text-align: center;
      &.status-box001{
        background-color:  rgba(58, 112, 229, 0.2);
        color: rgba(58, 112, 229, 1);
      }
      &.status-box002_1{
        background-color: rgba(250, 126, 0, 0.2);
        color: rgba(250, 126, 0, 1);
      }
      &.status-box002_2{
        background-color: rgba(192, 196, 204, 0.2);
        color: rgba(192, 196, 204, 1);
      }
      &.status-box003{
        background-color: rgba(192, 196, 204, 0.2);
        color: rgba(192, 196, 204, 1);
      }
        & > img {
        width: 100%;
        height: 100%;
      }
    }
    /deep/.van-cell {
      padding: 4px 0;
      line-height: 20px;
      &::after {
        display: none;
      }
      .van-cell__title {
        font-size: 14px;
        color: @third_text_color;
      }
      .van-cell__value {
        font-size: 14px;
        color: @main_text_color;
      }
    }
    /deep/.van-cell-group {
      margin-top: 12px;
      &::after {
        border: none;
      }
    }
  }

  .expand-box {
    padding: 12px 30px;
    border-top: 1px solid @border_bottom_color;
    display: flex;
    // justify-content: center;
    /deep/.van-button {
      width: 40%;
      padding: 16px;
      font-size: 16px;
      // &:first-child {
      //   margin-right: 20%;
      // }
      &--primary {
        color: @white_text_color;
        background: @ylb_color;
        border: 1px solid @ylb_color;
      }
    }
  }
}

</style>