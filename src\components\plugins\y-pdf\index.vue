<!--
 * @Description: pdf数据流（base64）由canvas展示，再转成图片，点击查看预览。可扩展成图片保存本地。目前没有pdf默认数据流
 * @Version: 0.1
 * @Autor: wjn
 * @Date: 2020-06-30 15:54:02
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-04-13 15:49:04
-->
<template>
  <div class="pdf-container" :id="eid" @click="previewImage" />
</template>

<script>
import { ImagePreview } from "@ylz/vant"
const PDFJS = require("pdfjs-dist")
PDFJS.GlobalWorkerOptions.workerSrc =
  "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.3.200/pdf.worker.min.js"

// 图片放大预览
export default {
  name: "y-pdf",
  props: {
    eid: {
      type: String,
      default: "pdfjs"
    },
    base64Pdf: {
      // pdf数据流
      type: String,
      default: ""
    }
  },
  data() {
    return {
      previewShow: false // pdf图片预览弹窗
    }
  },
  watch: {
    base64Pdf: {
      immediate: true,
      handler: function(newVal) {
        let _base64Pdf = newVal
        if (_base64Pdf) {
          const base64HeaderText = "data:application/pdf;base64,"
          const _headerText = newVal.slice(0, base64HeaderText.length)
          if (_headerText !== base64HeaderText) {
            _base64Pdf = base64HeaderText + this.base64Pdf
          }
        }
        this.$nextTick(() => {
          this.renderPdf(_base64Pdf)
        })
      }
    }
  },
  methods: {
    /**
     * @description: 预览pdf文档
     * @param {type}
     * @return: void 0
     * @author: lbw
     */
    previewImage(evt) {
      // 获取当前点击的canvas
      const id = evt.target.id
      if (!id) {
        return
      }
      const canvas = document.querySelector(`#${id}`)
      // canvas转为base64 图片
      const url = canvas.toDataURL("image/png")
      this.$emit("savePreviewImageFn", url) // 对外暴露生成的预览图片，可让原生app保存
      // 图片预览
      ImagePreview([url])
    },
    /**
     * @description: 渲染pdf文件
     * @param {pdfUrl} base64文件流
     * @return:
     * @author: hch
     */
    renderPdf(_base64Pdf) {
      const el = document.querySelector(`#${this.eid}`)
      if (!_base64Pdf) {
        el.innerHTML = ""
        return
      }
      // 当 PDF 地址为跨域时，pdf 应该以流的形式传输，否则会出现pdf损坏无法展示
      PDFJS.getDocument({
        url: _base64Pdf,
        cMapPacked: true,
        cMapUrl: "https://cdn.jsdelivr.net/npm/pdfjs-dist@2.3.200/cmaps/"
      }).promise.then(pdf => {
        // 拿到dom
        // 移除el下的子元素
        el.innerHTML = ""
        // 得到PDF的总的页数
        const totalPage = pdf.numPages
        const dpr = window.devicePixelRatio
        // 根据总的页数创建相同数量的canvas
        for (let i = 1; i <= totalPage; i++) {
          pdf.getPage(i).then((page) => {
            const viewport = page.getViewport({ scale: dpr })
            const canvas = document.createElement("canvas")
            const context = canvas.getContext("2d")
            canvas.height = viewport.height
            canvas.width = viewport.width
            const renderContext = {
              canvasContext: context,
              viewport
            }
            // 如果你只是展示pdf而不需要复制pdf内容功能，则可以这样写render // page.render(renderContext)
            page.render(renderContext)
            // 将canvas加入dom中
            canvas.id = `${this.eid}-canvas-pdf${i}`
            canvas.style.width = "100%"
            el.appendChild(canvas)
          })
        }
      })
    }
  }
}
</script>
