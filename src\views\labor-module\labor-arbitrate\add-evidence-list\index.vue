<!--
 * @Description: 添加证据清单
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div>
    <van-form
      ref="baseForm"
      class="base-form"
      label-width="140"
      :disabled="pageType === 'details'"
      @failed="onFailed"
      @submit="handleSave"
    >
      <van-field
        v-model="formData.tjr000"
        name="tjr000"
        label="提交人"
        placeholder="请输入"
        :required="true"
        :rules="formRules.tjr000"
      />

      <y-select-dict
        v-model="formData.zjmc00"
        label="证据形式"
        :required="true"
        :rules="formRules.zjmc00"
        dict-type="ZJMC00"
        :filterabled="false"
        :disabled="pageType === 'details'"
        is-link
      />

      <van-field
        v-model="formData.zjmc01"
        name="zjmc01"
        label="证据名称"
        placeholder="请输入"
        :required="true"
        :rules="formRules.zjmc01"
      />

      <van-field
        v-model="formData.ys0000"
        name="ys0000"
        label="页数"
        placeholder="请输入"
        :required="true"
        :rules="formRules.ys0000"
      />

      <van-field
        v-model="formData.ym0000"
        name="ym0000"
        label="页码"
        placeholder="请输入"
        :required="true"
        :rules="formRules.ym0000"
      />

      <y-select-dict
        v-model="formData.zjlx00"
        label="原件、复印件"
        :required="true"
        :rules="formRules.zjlx00"
        dict-type="ZJLX00"
        :filterabled="false"
        :disabled="pageType === 'details'"
        is-link
      />

      <van-field
        v-model="formData.zmdx00"
        name="zmdx00"
        label="证明对象"
        placeholder="请输入"
        :required="true"
        :rules="formRules.zmdx00"
      />

      <template v-if="formData.zjmc00 === '004'">
        <van-field
          v-model="formData.zmxm00"
          name="zmxm00"
          label="证人姓名"
          placeholder="请输入"
          :required="true"
          :rules="formRules.zmxm00"
        />

        <van-field
          v-model="formData.zrgm00"
          name="zrgm00"
          label="证人证件号码"
          placeholder="请输入"
          :required="true"
          :rules="formRules.zrgm00"
        />
      </template>

      <van-field
        class="alone-name"
        label-width="375"
        label="证据清单（可支持：图片、文档、文本）"
      />

      <div class="uploader-container uploader-container-alone">
        <van-uploader
          v-model="fileList"
          :disabled="pageType === 'details'"
          :deletable="pageType !== 'details'"
          :preview-full-image="true"
          max-count="4"
          accept="image/doc/docx/pdf"
          :after-read="afterRead"
        />
      </div>

      <div class="button-box-more" v-if="pageType !== 'details'">
        <van-button round block type="primary" native-type="submit">
          保存
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import { checkInteger } from "@/utils/check"
import { commonApi } from "@/api"
import isEmpty from "lodash/isEmpty"

export default {
  name: "add-evidence-list",

  data() {
    return {
      // 表单信息
      formData: {
        tjr000: "", //提交人
        zjmc00: "", //证据形式
        zjmc01: "", //证据名称
        ys0000: "", //页数
        ym0000: "", //页码
        zjlx00: "002", //原件、复印件
        zmdx00: "", //证明对象
        zmxm00: "", //证人姓名
        zrgm00: "", //证人证件号码

        wjxx01Base64: "", //文件材料base64
        wjxx02Base64: "",
        wjxx03Base64: "",
        wjxx04Base64: ""
      },
      formRules: {
        tjr000: [{ required: true, message: "请输入提交人" }],
        zjmc00: [{ required: true, message: "请选择证据形式" }],
        zjmc01: [{ required: true, message: "请输入证据名称" }],
        ys0000: [
          { required: true, message: "请输入页数" },
          {
            validator: checkInteger,
            message: "请输入正确的页数",
            trigger: "onBlur"
          }
        ],
        ym0000: [
          { required: true, message: "请输入页码" },
          {
            validator: checkInteger,
            message: "请输入正确的页码",
            trigger: "onBlur"
          }
        ],
        zjlx00: [{ required: true, message: "请输入原件、复印件" }],
        zmdx00: [{ required: true, message: "请输入证明对象" }],
        zmxm00: [{ required: true, message: "请输入证人姓名" }],
        zrgm00: [{ required: true, message: "请输入证人证件号码" }]

        // zrgm00: [
        //   { required: true, message: "请输入证人证件号码" },
        //   {
        //     validator: validateIdCard,
        //     message: "请输入正确身份证号码",
        //     trigger: "onBlur"
        //   }
        // ]
      },

      // 企业信息
      fileList: []
    }
  },
  computed: {
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    },
    zcy000() {
      //仲裁委员会
      return this.$route.query.zcy000
    },
    pageType() {
      return this.$route.query.pageType
    },
    bczzj0() {
      //证据清单表主键
      return this.$route.query?.bczzj0 || ""
    }
  },
  watch: {
    "formData.zjmc00": {
      handler(val) {
        if (val !== "004") {
          this.formData.zmxm00 = ""
          this.formData.zrgm00 = ""
        }
      }
    }
  },
  created() {    
    if (this.pageType === "add") {
      this.formData.tjr000 = this.userInfo.xm0000      
    } else {
      this.getBc05ZjqdById() // 查询详情
    }
  },
  methods: {
    // 查询详情
    getBc05ZjqdById() {
      const { bczzj0 } = this
      commonApi
        .proxyApi({
          serviceName: "xytBc05Zjqd_getBc05ZjqdById",
          bczzj0
        })
        .then((res) => {
          console.log("查询详情", res)
          this.formData = res.map.data

          const { wjxx01Base64, wjxx02Base64, wjxx03Base64, wjxx04Base64 } =
            this.formData
          const fileInfo = {
            wjxx01Base64,
            wjxx02Base64,
            wjxx03Base64,
            wjxx04Base64
          }
          const fileList = []
          for (const key in fileInfo) {
            if (fileInfo[key]) {
              fileList.push({ content: fileInfo[key] })
            }
          }
          this.fileList = fileList
          console.log(this.fileList, "this.fileList****")
        })
    },
    onFailed() {},
    // 保存
    handleSave() {
      if (isEmpty(this.fileList)) {
        this.$dialog.alert({
          title: "提示",
          message: "请先上传证据清单！",
          theme: "round-button"
        })
        return
      }

      console.log(this.fileList, "this.fileList00")

      this.fileList.forEach((item, index) => {
        const name = `wjxx0${index + 1}Base64`
        this.formData[name] = item.content
      })

      console.log(this.formData, "this.formData")

      this.$dialog
        .confirm({
          title: "提示",
          message: "您是否确定保存",
          showCancelButton: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        })
        .then(() => {
          const { zcy000 } = this
          commonApi
            .proxyApi({
              serviceName: "xytBc05Zjqd_saveOrUpdateBc05Zjqd",
              ...this.formData,
              zcy000
            })
            .then((res) => {
              console.log("保存", res)
              this.$dialog
                .alert({
                  title: "提示",
                  message: "保存成功！",
                  theme: "round-button"
                })
                .finally(() => {
                  this.$router.go(-1)
                })
            })
            .catch((err) => {
              console.error(err)
            })
        })
    },

    // 文件读取完成后的回调函数
    afterRead(data) {
      console.log(data, "data")
      console.log(this.fileList, "fileList")
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.alone-name {
  &::after {
    border-bottom: none !important;
  }
  .van-cell__title {
    width: 100%;
  }
  .van-cell__value {
    display: none;
  }
}
.uploader-container {
  padding: 8px 16px;
}
.button-box-more {
  background: #fff;
}
</style>
