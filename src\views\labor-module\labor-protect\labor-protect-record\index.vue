<!--
 * @Description: 人民调解--记录
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="labor-protect-record">

    <!-- 顶部tab -->
    <business-tabs :tabList="tabList" @handleChangeTabs="handleChangeTabs" @handleAdd="handleAdd"></business-tabs>

    <div class="info-container">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" :finished-text="list?.length > 0 ? '没有更多了' : ''"
          @load="onLoad">
          <div v-if="list?.length > 0">
            <info-box v-for="item in list" :key="item.cce010" title="受理状态" :moreText="getStatusName(item).text"
              :colorMore="getStatusName(item).color" customMoreText>
              <template v-if="userInfo.sfja00 == 2" #cells>
                <van-cell title="编号" :value="item.bcz003" :border="false" />
                <van-cell title="纠纷类型" :value="getDictName(aba002List, item.aba002)" :border="false" />
                <van-cell title="被申请人" :value="item.aab004" :border="false" />
                <van-cell title="申请人" :value="item.aac003" :border="false" />
                <van-cell title="调解员" :value="item.tjyName" :border="false" />
                <van-cell title="开始调解时间" :value="dayFormatFn(item.tjkssj, 'date')" :border="false" />
                <van-cell title="结束调解时间" :value="dayFormatFn(item.tjjssj, 'date')" :border="false" />
              </template>
              <template v-else #cells>
                <van-cell title="业务类型" value="新就业形态纠纷调解申请" :border="false" />
                <van-cell title="所属网络平台APP名称" :value="getDictName(platformList, item.aac149)" :border="false" />
                <van-cell class="writing-hidden" title="被投诉单位名称" :value="item.aab004" :border="false" />
                <van-cell title="案件编号" :value="item.abb013" :border="false" />
                <van-cell title="申请日期" :value="dayFormatFn(item.tjsj00, 'date')" :border="false" />
                <van-cell title="是否撤回" :value="getDictName(yesNoList, item.sfch00)" :border="false" />
              </template>

              <template #buttons>
                <template v-if="item.abb292 === '0'">
                  <van-button type="warning" @click="handleDelete(item)">删除</van-button>
                  <van-button type="warning" class="info-button" @click="handleEdit(item)">编辑</van-button>
                  <van-button type="primary" @click="handleSubmit(item)">提交</van-button>
                </template>

                <template v-else>
                  <template v-if="item.sfch00 === '0'">
                    <van-button v-if="['2', '8'].includes(item.abb292)" type="warning"
                      @click="handleToRevoke(item)">撤回</van-button>
                    <van-button v-if="item.sfja00 === '0'" type="warning" class="info-button"
                      @click="handleUpload(item)">材料上传</van-button>
                  </template>

                  <van-button v-if="item.sfja00 === '1'" type="primary" @click="handleViewCase(item)">结案查看</van-button>
                  <van-button v-if="!item.bch010" type="primary" @click="handleDetails(item)">查看详情</van-button>
                  <template v-if="item.bch010">
                    <van-button type="primary" @click="handleOpen(item)">在线调解</van-button>
                  </template>
                </template>
              </template>
            </info-box>
          </div>

          <y-empty v-else></y-empty>
        </van-list>
      </van-pull-refresh>
      <van-popup class="evaluate-popup" v-model="isShowOnlineRevoke" position="center" closeable
        :close-on-click-overlay="false">
        <div class="evaluate-box">
          <div class="evaluate-title">在线调解</div>
          <van-field v-model="ID0000" name="ID0000" label="会议邀请码" placeholder="请输入" />
          <div class="evaluate-button flex-c-c">
            <van-button plain type="info" round @click="isShowOnlineRevoke = false" native-type="button">
              关闭
            </van-button>

            <van-button @click="handleOnlineRevoke" round block type="primary">
              确定
            </van-button>
          </div>
        </div>

      </van-popup>
      <van-popup class="evaluate-popup" v-model="isShowIntoFaceConfirm" position="center" closeable
        :close-on-click-overlay="false">
        <div class="evaluate-box">
          <div class="evaluate-title">在线调解</div>
          <van-icon name="checked" class="online-revoke-icon" />
          <div class="online-revoke-text">验证成功，请进行人脸验证！</div>
          <div class="evaluate-button flex-c-c">
            <van-button plain type="info" round @click="isShowIntoFaceConfirm = false" native-type="button">
              关闭
            </van-button>

            <van-button @click="handleJumpToVerify" round block type="primary">
              确定
            </van-button>
          </div>
        </div>

      </van-popup>
    </div>
  </div>
</template>

<script>
import BusinessTabs from "@/components/business/business-tabs"
import InfoBox from "@/components/business/info-box"
import { main_color, status_one_color, status_two_color, status_three_color } from "@/styles/theme/theme-params.less"
import { SHA256 } from "crypto-js"

import { commonApi } from "@/api"
import { getDictName } from "@utils/common"

const STATUS = {
  //人民调解状态
  "0": {
    text: "待提交",
    color: status_three_color
  },
  "4": {
    text: "已结案",
    color: status_one_color
  }
}
const STATUS1 = {
  "1": {
    text: "未开始",
    color: status_three_color
  },
  "2": {
    text: "进行中",
    color: status_two_color
  },
  "3": {
    text: "已结束",
    color: status_one_color
  }
}

export default {
  name: "labor-protect-record",
  components: {
    BusinessTabs,
    InfoBox
  },
  data() {
    return {
      tabList: [
        { title: "正在受理", number: "0" },
        { title: "已结案", number: "0" },
        { title: "线上调解", number: "0" }
      ],

      // 信息列表
      demoInfo: {
        ptmc: "厦门市达达闪送分公司",
        aca111: "3",
        ccd028: "2",
        sftbch: "1",
        ccd006: "20231115"
      },
      titleObj: {
        title: "受理状态",
        platformName: "已结案"
      },
      colorMore: main_color,

      list: [],
      loading: false,
      finished: false,
      refreshing: false, //下拉刷新

      // 用户信息
      userInfo: {
        ...this.$sessionUtil.getItem("userInfo"),

        sfja00: "0", //0正在受理 1已结案
        aaa028: "2", //数据来源（0 经办 1 单位 2 个人 3 主管部门）
        source: "002", //渠道来源（001 PC端 002 移动端）

        page: 0,
        size: 3
      },

      // 字典列表
      platformList: [], //平台字典列表
      yesNoList: [], //是否同步参会字典列表
      aba002List: [], //纠纷类型字典列表
      isShowOnlineRevoke: false, //在线会议调解窗口
      isShowIntoFaceConfirm: false, //进入人脸验证窗口
      ID0000: "", //会议调解码
      bcz003: "",
      aac002: "", //身份证
      aac003: "", //姓名
      selectData: {},
      isVerifyFailed: false
    }
  },

  mounted() {
    this.getCountFn() // 获取总条数

    this.getPlatformList() //查询字典列表
    this.isVerifyFailed = this.$route.query.isVerifyFailed
    this.verifyFailed()//人脸验证提示
  },

  methods: {
    // 列表状态名称
    getStatusName(data) {
      const { sfja00, abb292, bch010 } = data
      if (sfja00 === "2") {
        return STATUS1[bch010]
      }
      return STATUS[abb292] || { text: "受理中", color: status_two_color }
    },

    // 查看字典对应名称
    getDictName,

    // 获取总条数
    getCountFn() {
      this.getCount("0") // 获取总条数 未结案
      this.getCount("1") // 获取总条数 已结案
      this.getCount("2") // 获取总条数 在线调解
    },

    getCount(sfja00) {
      const { page, size, aaa028, source } = this.userInfo
      if (sfja00 == "2") {
        const params = {
          serviceName: "xytBc03Web_findBc03ByPage",
          ...{ page, size, aaa028, source }
        }
        commonApi.proxyApi(params).then(res => {
          const { total } = res.map.data
          //2在线调解
          this.tabList[2].number = total
        })
      } else {
        const params = {
          serviceName: "xytBc01_findBc01ByPage",
          ...{ page, size, aaa028, source },
          sfja00
        }
        commonApi.proxyApi(params).then(res => {
          const { total } = res.map.data
          if (sfja00 === "0") {
            //0正在受理
            this.tabList[0].number = total
          } else {
            //1已结案
            this.tabList[1].number = total
          }
        })
      }
    },

    //查询字典列表
    getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["YES_NO", "AAC149", "ABA002"]
      }
      commonApi.proxyApi(params).then(res => {
        const { data } = res.map
        const dictInfo = {
          YES_NO: "yesNoList",
          AAC149: "platformList",
          ABA002: "aba002List"
        }
        for (const key in dictInfo) {
          this[dictInfo[key]] = data[key].map(item => {
            return { label: item.aaa103, value: item.aaa102 }
          })
        }
      })
    },

    // 切换tab
    handleChangeTabs(index) {
      console.log("切换tab", index)
      this.userInfo.sfja00 = index === 0 ? "0" : index //切换在职状态
      this.list = []
      this.onRefresh()
    },

    // 获取列表数据
    onLoad() {
      this.userInfo.page++
      this.findBc01ByPage()
    },

    // 查询列表信息
    findBc01ByPage() {
      const { page, size, sfja00, aaa028, source } = this.userInfo
      if (this.userInfo.sfja00 == 2) {
        const params = {
          serviceName: "xytBc03Web_findBc03ByPage",
          ...{ page, size, sfja00, aaa028, source }
        }
        commonApi.proxyApi(params).then(res => {
          if (this.refreshing) { // 清空列表数据
            this.list = []
            this.refreshing = false
          }

          const { rows = [], total = 0 } = res.map.data
          rows.forEach((item) => {
            item.sfja00 = "2"
          })
          this.list = [...this.list, ...rows]
          console.log(this.list, "this.list")

          this.loading = false
          if (this.list.length === total) {
            this.finished = true
          }
        }).catch(() => {
          this.loading = false
          this.finished = true
        })
      } else {
        const params = {
          serviceName: "xytBc01_findBc01ByPage",
          ...{ page, size, sfja00, aaa028, source }
        }
        commonApi
          .proxyApi(params)
          .then(res => {
            if (this.refreshing) {
              // 清空列表数据
              this.list = []
              this.refreshing = false
            }

            const { rows = [], total = 0 } = res.map.data
            this.list = [...this.list, ...rows]
            console.log(this.list, "this.list")

            this.loading = false
            if (this.list.length === total) {
              this.finished = true
            }
          }).catch(() => {
            this.loading = false
            this.finished = true
          })
      }

    },

    // 下拉刷新
    onRefresh() {
      // 清空列表数据
      this.finished = false

      // 重新加载数据
      this.loading = true
      this.userInfo.page = 0
      this.onLoad()
    },

    // 新增
    handleAdd() {
      this.$router.push({
        path: "/labor-protect-handle",
        query: { pageType: "add" }
      })
    },

    // 删除
    handleDelete(data) {
      this.$dialog
        .confirm({
          title: "提示",
          message: "您确定删除",
          showCancelButton: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        })
        .then(() => {
          const { bcz001 } = data
          const params = {
            serviceName: "xytBc01_deleteBc01ByIds",
            id: "",
            ids: [bcz001]
          }
          commonApi.proxyApi(params).then(() => {
            this.$toast("删除成功")
            this.getCountFn()
            this.refreshing = true
            this.onRefresh()
          })
          console.log(data, "删除")
        })
        .catch(() => { })
    },

    // 编辑
    handleEdit(data) {
      console.log(data, "编辑")
      const { bcz001, abz200 } = data
      this.$router.push({
        path: "/labor-protect-handle",
        query: { pageType: "edit", bcz001, abz200 }
      })
    },
    verifyFailed() {
      console.log(localStorage.getItem("isVerifyFailed"), "内容")
      if (localStorage.getItem("isVerifyFailed") == true) {
        this.$dialog.alert({
          title: "温馨提示",
          message: "人脸核验失败，请重试！",
          theme: "round-button",
          showConfirmButton: true
        })
        localStorage.setItem("isVerifyFailed", false)
        console.log(localStorage.getItem("isVerifyFailed"), "复制后内容")
      }
    },
    // 提交
    handleSubmit(data) {
      this.$dialog
        .confirm({
          title: "提示",
          message: "您确定提交",
          showCancelButton: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        })
        .then(async() => {
          console.log(data, "提交")
          const { bcz001 } = data
          try {
            const submitParams = {
              serviceName: "xytBc01_submitBc01",
              bcz001
            }
            const res = await commonApi.proxyApi(submitParams)
            console.log(res, "提交 提交 提交")
            this.$toast.success("提交成功！")
            this.refreshing = true
            this.onRefresh()
          } catch (err) {
            console.log(err, "提交err 提交 提交")
            this.$dialog.confirm({
              title: "提示",
              message: "业务数据未填写完善，请点击编辑继续填写！",
              showCancelButton: true,
              confirmButtonText: "确认"
            })
          }
        })
    },

    // 撤回
    handleToRevoke(data) {
      const { bcz001, bcz002, sfch00, aab001 } = data
      console.log(data, "value111")
      this.$router.push({
        path: "/labor-protect-revoke",
        query: { bcz001, bcz002, sfch00, aab001 }
      })
    },

    // 材料上传
    handleUpload(data) {
      const { abz200 } = data
      console.log(abz200, "abz200 handleUpload")
      this.$router.push({
        path: "/labor-protect-upload",
        query: { abz200: abz200 }
      })
      console.log(data, "提材料上传交")
    },

    // 查看详情
    handleDetails(data) {
      console.log(data, "查看详情")
      const { bcz001, abz200 } = data
      this.$router.push({
        path: "/labor-protect-handle",
        query: { pageType: "detail", bcz001, abz200 }
      })
    },

    //在线调解
    handleOpen(data) {
      this.bcz003 = data.bcz003
      this.selectData = data
      this.isShowOnlineRevoke = true
    },
    async handleOnlineRevoke() {
      try {
        const submitParams = {
          serviceName: "xytBc03Web_checkCode",
          bcz003: this.bcz003,
          id0000: this.ID0000
        }
        const res = await commonApi.proxyApi(submitParams)
        if (!res.map.data.aac002) {
          this.$dialog.alert({
            title: "温馨提示",
            message: res.msg,
            theme: "round-button",
            showConfirmButton: true
          })
          return
        }
        this.aac002 = res.map.data.aac002
        this.aac003 = res.map.data.aac003
        this.isShowIntoFaceConfirm = true
        console.log(res, "提交 提交 提交")
      } catch (err) {
        this.isShowOnlineRevoke = false
        this.refreshing = true
        this.onRefresh()
      }
    },
    handleJumpToVerify() {
      const name = this.aac002
      const id = this.aac003
      const { bcz003 } = this.selectData
      const params = {
        serviceName: "xytBc03Web_faceCheck",
        aac002: name,
        bcz003
      }
      commonApi.proxyApi(params).then((res) => {
        const dataResult = res?.msg
        console.info(dataResult, "验证二维码之后")

        if (dataResult == "1") {
          // 扫描结果为 1 则，关闭定时器
          // this.$router.push({ path: "/person/online-mediation", query: { bcz003: pK } })
          this.$router.push({ path: "/labor-protect-detail", query: { bcz003 } })
        } else {
          const formData = new FormData()
          const signStr = "aac002=" + name + "&aac003=" + id + "&returnUrl=" + window.location.href.replace("labor-protect-record", "labor-protect-verify") + "?bcz003=" + bcz003 + "&token=xmrs&ywtype=TY01"
          // const signStr= "aac002="+name+"&aac003="+id+"&returnUrl=https://app.hrss.xm.gov.cn/xmxytapp-test/#/"+"&token=xmrs&ywtype=TY01"
          // this.$router.push({ path: "/labor-protect-detail", query: { bcz003 } })
          formData.append("aac002", name)
          formData.append("aac003", id)
          formData.append("ywtype", "TY01")
          formData.append("token", "xmrs")
          formData.append("type", "04")
          formData.append("returnUrl", window.location.href.replace("labor-protect-record", "labor-protect-verify") + "?bcz003=" + bcz003)
          const encryptedData = SHA256(signStr).toString()
          formData.append("sign", encryptedData)
          const queryParams = new URLSearchParams(formData).toString()
          const url = `https://app.hrss.xm.gov.cn/SBServer/face/wxindex?${queryParams}`
          console.log(url, "人脸识别跳转路径")
          window.location.href = url
        }
      })

    },
    firstStr(str) {
      const index = str.lastIndexOf("/")
      str = str.substring(0, index + 1)
      return str
    },
    // 结案查看
    handleViewCase(data) {
      console.log(data, "查看详情")
      const { bcz001 } = data
      this.$router.push({ path: "/labor-protect-case", query: { bcz001 } })
    }
  }
}
</script>

<style lang="less" scoped>
.info-container {
  background: @background_gray_color;
  min-height: calc(100vh - 44px);

  /deep/.info-box .y-title .content .more {
    position: relative;

    &::before {
      content: " ";
      width: 8px;
      height: 8px;
      border-radius: 4px;
      background: inherit;
      position: absolute;
      top: 50%;
      left: -25%;
      transform: translate(0, -50%);
    }
  }

  /deep/.handle-cell .van-cell__value {
    color: @main_color !important;
  }
}

.evaluate-popup {
  width: 80% !important;
  border-radius: 8px;

  .evaluate-box {
    .evaluate-title {
      text-align: left;
      font-size: 16px;
      margin: 16px 0;
      padding-left: 16px;
      font-weight: 600;
    }

    .evaluate-button {
      padding: 12px 0;

      .van-button--primary {
        margin-left: 8px;
      }
    }

    .online-revoke-icon {
      display: flex;
      justify-content: center;
      color: #32ae57;
      font-size: 65px;
    }

    .online-revoke-icon {
      display: flex;
      justify-content: center;
      color: #32ae57;
      font-size: 65px;
    }

    .online-revoke-text {
      font-size: 12px;
      text-align: center;
      margin-bottom: 16px;
      margin-top: 4px;
    }

    /deep/.van-radio-group {
      padding: 4px 16px;
      font-size: 14px;
    }

    /deep/.van-field__control {
      text-align: right;
    }
  }
}
</style>
