<!--
 * @Description: 技能培训补贴
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="skill-subsidies">
    <!-- 标题背景 -->
    <div class="title-box">
      <div class="title-text">补贴类型</div>
      <div class="title-tips">培训补贴/鉴定补贴</div>

      <!-- 申请进度 -->
      <div class="apply-progress">
        <y-title content="申请进度" :moreText="progressList.length > 0 ? '更多' : ''" :colorMore="colorMore" @onMoreCilck="onMoreCilck" />
        <div class="progress-list" v-if="progressList.length > 0">
          <div class="progress-item flex-c-sb" v-for="(item,index) in progressList" :key="index">
            <div class="item-left">
              <p class="skill-number">证书编号：{{item.zsbh00}}</p>
              <p class="skill-type xz-ellipsis">补贴申报类型：{{item.jzbtlx | getApplyTypeName(businessDictObj)}}</p>
            </div>
            <div :class="['item-right', 'flex-c-c', examineStatus(item.sqjd00).className]">
              <img :src="examineStatus(item.sqjd00).icon" alt="">
              <span>{{item.sqjd00}}</span>
            </div>
          </div>
        </div>

        <div v-else class="empty-box flex-c-c-c">
          <div class="empty-img">
            <img :src="require('@pic/common/<EMAIL>')" alt="">
          </div>  
          <div class="empty-tip">暂无申请记录</div>
        </div>
      </div>
    </div>

    <!-- 申请提示 -->
    <div :class="['apply-reminder', progressList.length === 1 ? 'apply-reminder-top' : '']">
      <y-title content="申请提示" />
      <div class="reminder-text">
          <p>一、申请条件</p>
          <br />
          1、对象：在厦门市公共就业管理服务机构办理实名制就业（单位）、失业（个人）登记未满60岁的城乡劳动者。本市高校（含技工院校高级工及以上等级班）的毕业学年毕业生。
          <br />
          2、证书：福建省人社部门备案的职业技能鉴定机构及认定机构颁发的专项职业能力证书和职业技能等级证书。
          <br />
          <p>二、补贴相关限制条件</p>
          <br />
          1、培训补贴不可重复、降级申领：同一职业（工种）同一等级不得重复享受；同一职业（工种）已享受高等级别职业技能培训补贴的，不再享受低等级别的补贴。
          <br />
          2、次数：培训补贴每人累计最多享受<span class="major-text">3次</span>，享受次数期间从2023年12月20日起，至2025年12月31日。
          <br />
          3、时间：证书颁发日2024年1月1日前的，6个月内申领补贴；证书颁发日2024年1月1日及之后的，12个月内申领补贴。
          <br />
          4、培训补贴上浮：纳入厦门市急需紧缺工种，证书颁发日在2024年6月1日及之后的，培训补贴在标准基础上上浮20%。
          <br />
          5、鉴定费：初次取得专项职业能力证书，证书颁发日在2024年7月1日及之后的，停止申领。
          <br />
          <p>三、补贴工作环节提示</p>
          <br />
          1、申请核实：在“保存”前请核对录入信息（包括银行机构），确保无误；培训补贴与鉴定补贴分别申报。
          <br />
          2、鉴定费、评价补贴申领：初次取得专项职业能力和职业技能等级证书，证书颁发日在2024年7月1日及之后的，鉴定费补贴停止受理，补贴申领可向发证单位或培训机构咨询。
          <br />
          3、审核、公示及发放：受理补贴申请后，经办部门7个工作日内完成审核；在次月5个工作日内完成公示，公示时间为5个工作日；公示结束无异议的，20个工作日内完成补贴发放。
          <br />
          <p>四、申报过程中遇到问题，可拨打电话0592-12333、0592-5063639咨询。</p>
      </div>
    </div>

    <!-- 补贴标准 -->
    <div class="subsidy-standards">
      <y-title content="补贴标准" />
      <div class="subsidy-list">
        <div class="subsidy-item flex-c-c" v-for="(item,index) in subsidyList" :key="index">
          <div class="item-left">
            <img :src="item.iconUrl" alt="">
          </div>
          <div class="item-right">
            <p class="certificate xz-ellipsis-2">{{ item.certificate }}</p>
            <p class="money xz-ellipsis">{{ item.money }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 发起申请 -->
    <div class="apply-box flex-c-c">
      <van-button class="apply-button" type="primary" @click="handleApply">
        <template slot="default">
          <div class="flex-c-c">
            <img class="apply-button-icon" :src="require('@/assets/imgs/skill-subsidies/<EMAIL>')" alt="">
            <span class="apply-button-text">发起申请</span>
          </div>
        </template>
      </van-button>
    </div>
    
  </div>
</template>

<script>
import {
  six_text_color
} from "@/styles/theme/theme-params.less"

import {commonApi} from "@/api"
import {commonGetPlatformList} from "@/utils/common"

export default {
  name: "skill-subsidies",
  filters: {
    getApplyTypeName(value, list) {
      const data = list?.JZBT_BTLX00?.find(item => item.value === value)
      return data?.label || ""
    }
  },
  data() {
    return {
      // 标题
      colorMore: six_text_color,

      //补贴列表
      progressList: [],

      //补贴标准列表
      subsidyList: [ 
        {
          iconUrl: require("@/assets/imgs/skill-subsidies/<EMAIL>"),
          certificate: "初级工（五级）职业技能等级证书",
          money: "补贴700元/人"
        },
        {
          iconUrl: require("@/assets/imgs/skill-subsidies/<EMAIL>"),
          certificate: "中级工（四级）职业技能等级证书",
          money: "补贴1000元/人"
        },
        {
          iconUrl: require("@/assets/imgs/skill-subsidies/<EMAIL>"),
          certificate: "高级工（三级）职业技能等级证书",
          money: "补贴1500元/人"
        },
        {
          iconUrl: require("@/assets/imgs/skill-subsidies/<EMAIL>"),
          certificate: "技师（二级）职业技能等级证书",
          money: "补贴2000元/人"
        },
        {
          iconUrl: require("@/assets/imgs/skill-subsidies/<EMAIL>"),
          certificate: "高级技师（一级）职业技能等级证书",
          money: "补贴3000元/人"
        },
        {
          iconUrl: require("@/assets/imgs/skill-subsidies/<EMAIL>"),
          certificate: "专项职业能力证书",
          money: "培训补贴500元/人，鉴定补贴130元/人"
        }  
      ],

      // 业务字典
      businessDictObj: {
        JZBT_BTLX00: [] //补贴申报类型
      },
      disabledApplyBtn: true
    }
  },
  computed: {
    userInfo() {
      const {xm0000: aac003, zjhm00: aac002} = this.$sessionUtil.getItem("userInfo")
      return {aac003, aac002}
    }    
  },
  async created() {  
    // await this.queryCe10ByAac002() // 校验是否备案 (暂时不需要校验)

    this.businessDictObj = await commonGetPlatformList(["JZBT_BTLX00"]) //获取业务所需字典

    this.queryList() //查询补贴列表    
  },
  methods: {
    // 校验是否备案
    async queryCe10ByAac002() {
      const {aac002} = this.userInfo
      const params = {
        serviceName: "xytPerson_queryCe10ByAac002",
        aac002
      }
      const res = await commonApi.proxyApi(params)
      const {msg} = res
      if (msg === "0") { //未备案
        this.disabledApplyBtn = true
        this.$dialog.alert({
          title: "提示",
          message: "该人员未备案！",
          theme: "round-button"
        })
      } else {
        this.disabledApplyBtn = false
      }
    },
    // 审核状态
    examineStatus(value) {
      const statusInfo = {
        "通过": {
          icon: require("@/assets/imgs/skill-subsidies/<EMAIL>"),
          className: "approval-status-ok"
        },
        "不通过": {
          icon: require("@/assets/imgs/skill-subsidies/<EMAIL>"),
          className: "approval-status-no"
        },
        "进行中": {
          icon: require("@/assets/imgs/skill-subsidies/<EMAIL>"),
          className: "approval-status-progress"
        }
      }

      let data = {}
      if (value.includes("通过") && !value.includes("不通过")) { //通过
        data = statusInfo["通过"]
      } else if (value.includes("不通过")) { //不通过
        data = statusInfo["不通过"]
      } else { //进行中
        data = statusInfo["进行中"]
      }

      return data
    },
    // 查看更多列表
    onMoreCilck() {
      this.$router.push("/skill-subsidies-record")
    },    
    // 查询补贴列表
    queryList() {
      const params = {
        serviceName: "xytjzbt_queryList",
        ...this.userInfo
      }
      commonApi.proxyApi(params).then(res => {
        this.progressList = res.data?.slice(0, 2) || []
      })
    },
    // 申请
    handleApply() {
      this.$router.push({path: "/skill-subsidies-handle", query: {pageType: "add"}})
    }    
  }
}
</script>
<style lang="less" scoped>
.skill-subsidies {
  background: #F6F6F6;
  padding-bottom: 92px  ;
  .title-box {
    width: 375px;
    height: 336px;
    background: url("~@/assets/imgs/skill-subsidies/<EMAIL>") no-repeat center/100% 100%;
    position: relative;
    padding-top: 34px;
    .title-text {
      margin-left: 26px;
      font-size: 30px;
      font-weight: bold;
      color: #FC8A37;
      line-height: 42px;
    }
    .title-tips {
      margin: 6px 0 0 26px;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
    }
    .apply-progress {
      position: absolute;
      top: 136px;
      width: 346px;
      background: #FFFFFF;
      box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.04);
      border-radius: 12px;
      margin: 0 14px;
      padding: 10px 14px;
      .progress-item {
        height: 74px;    
        padding-top: 16px;    
        &:not(:last-child) {
          border-bottom: 1px dashed rgba(206,206,206,.5);
        }
        .item-left {
          height: 100%;
          width: 218px;
          .skill-number {
            font-weight: bold;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: left;
          }
          .skill-type {
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            text-align: left;
            margin-top: 10px;
          }
        }
        .item-right {
          height: 28px;
          width: 100px;
          font-size: 14px;
          
          line-height: 28px;
          text-align: left;
          font-style: normal;
          
          border-radius: 14px;  
          margin-top: -14px;  
          &.approval-status {
            &-ok {
              background: #e4f9ef;
              color: #0AC673;
            }
            &-no {
              background: #F8E8EA;
              color: #BD1A2D;
            }
            &-progress {
              background: #FFF0E1;
              color: #FB6F13;
            }
          }
          & > img {
            width: 14px;
            height: 14px;
          }
          & > span {
            margin-left: 4px;
          }
        }
      }
      .empty-box {
        height: 146px;
        .empty-img {
          width: 120px;
          height: 80px;
          & > img {
            width: 100%;  
            height: 100%;  
          }
        }
        .empty-tip {
          margin-top: 34px;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          text-align: center;
        }
      }
    }
  }  
  .apply-reminder {
    width: 356px;
    border-radius: 12px;
    margin: 34px 10px 0;
    background: url("~@/assets/imgs/skill-subsidies/<EMAIL>") no-repeat center/100% 100%;
    padding: 4px 16px 16px;
    &-top {
      margin-top: -36px;
    }
    .y-title {
      background: rgba(255,255,255,0);
    }
    .reminder-text {
      width: 100%;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: left;
      & > p {
        padding: 6px 0;
        display: inline-block;
      }
    }
  }
  .subsidy-standards {
    width: 356px;
    border-radius: 12px;
    margin: 16px 10px 0;
    box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.04);
    padding: 4px 16px;
    background: #ffffff;
    
    .subsidy-list {
      .subsidy-item {
        width: 100%;
        height: 78px;
        &:not(:last-child) {
          border-bottom: 1px dashed rgba(206,206,206,.5);
        }
        .item-left {
          width: 48px;
          height: 48px;
          & > img {
            width: 100%;
            height: 100%;
          }
        }
        .item-right {
          flex: 1;
          margin-left: 12px;
          .certificate {
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: left;
          }
          .money {
            font-size: 14px;
            color: #FF3B37;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            margin-top: 8px;
          }
        }
      }
    }
  }
  .apply-box {
    width: 375px;
    height: 80px;
    background: #FFFFFF;
    box-shadow: 6px 0px 4px 0px rgba(0,0,0,0.14);
    position: fixed;
    bottom: 0;
    .apply-button {
      width: 165px;
      height: 44px;
      background: #BD1A2D;
      border-radius: 22px;
      .apply-button-icon {
        width: 26px;
        height: 26px;
      }
      .apply-button-text {
        margin-left: 14px;
      }
    }
  }
}
</style>