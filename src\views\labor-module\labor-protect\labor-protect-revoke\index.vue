<!--
 * @Description: 劳动维权--撤回申请
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <!-- 提交成功页面 -->
    <y-success-page v-if="isSuccess" text="撤回成功"></y-success-page>

    <template v-else>
      <!-- 撤回信息 -->
      <revoke-info v-show="active === 0" @handleNext="handleNext" v-model="formData"></revoke-info>

      <!-- 撤回签名 -->
      <revoke-sign  evoke-sign v-show="active === 1" @handleNext="handleNext" @handleSubmit="handleSubmit"></revoke-sign>
    </template>
  </div>
</template>

<script>
import RevokeInfo from "./cpns/revoke-info.vue"
import RevokeSign from "./cpns/revoke-sign.vue"

import {commonApi} from "@/api"

export default {
  name: "apply-info",
  components: {
    RevokeInfo,
    RevokeSign
  },
  data() {
    return {
      active: 0,
      isSuccess: false,

      formData: {
        aac003: "",
        aac004: "",
        ccg981: "",
        aac002: "",
        aae005: "",
        aab004: "",
        tjsj00: "",
        aae007: "",
        aae008: "",
        aab277: "",
        aae009: "",
        ab01aae005: "",
        ab01aae006: ""
      }
    }
  },
  computed: {
    isRevoke() {
      return this.$route.query.sfch00
    },
    primaryKey() {
      return this.$route.query.bcz001 || ""
    },
    unitNumber() {
      return this.$route.query.aab001 || ""
    }
  },
  created() {
    this.isRevoke === "0" ? this.getBc01ById() : this.getBc02ByBcz001() //是否撤回（1是，0否）
  },
  methods: {
    // 未撤回 查询案件信息
    async getBc01ById() {    
      const unitParams = {
        serviceName: "xytCommon_getAb01NewByAab001",
        aab001: this.unitNumber
      }
      const unitInfo = await commonApi.proxyApi(unitParams) //获取单位信息
      const {data: unitData} = unitInfo.map
      console.log(unitData, "unitData")

      const personParams = {
        serviceName: "xytBc01_getBc01ById",
        bcz001: this.primaryKey
      }
      const personInfo = await commonApi.proxyApi(personParams) //获取个人信息
      const {data: personData} = personInfo.map
      this.formData = {...unitData, ...personData}
    },

    // 已撤回 查询案件信息
    async getBc02ByBcz001() {      
      const params = {
        serviceName: "xytBc02_getBc02ByBcz001",
        bcz001: this.primaryKey
      }
      const res = await commonApi.proxyApi(params) //获取案件信息
      const {data} = res.map

      const unitParams = {
        serviceName: "xytCommon_getAb01NewByAab001",
        aab001: this.unitNumber
      }
      const unitInfo = await commonApi.proxyApi(unitParams) //获取单位信息
      const {data: unitData} = unitInfo.map

      this.formData = {...this.formData, ...data, ...unitData}
    },

    // 步骤
    handleNext(active) {
      this.active = active
    },

    // 提交
    handleSubmit(qmzp00Base64) {
      const params = {
        serviceName: "xytBc02_saveOrUpdateBc02",
        ...this.formData,
        qmzp00Base64 //签名
      }

      delete params.createTime
      delete params.updateTime
      commonApi.proxyApi(params).then(res => {
        console.log(res, "撤回res")
        this.isSuccess = true
      })
    }
  }
  
}
</script>

<style lang="less" scoped>

</style>