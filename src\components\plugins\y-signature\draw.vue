<!--
 * @Description: canvas画布
 * @Version: 0.1
 * @Autor: yjm
 * @LastEditors: yjm
 * @Date: 2020-10-22 15:07:06
 * @LastEditTime: 2020-11-09 15:20:52
-->
<template>
  <div id="canvasBox" :style="getHorizontalStyle">
    <div class="greet">
      <van-nav-bar
        title="绘制签名"
        left-text="返回"
        left-arrow
        @click-left="handleBack"
      >
        <template #right>
          <div class="switch">
            横屏<van-switch v-model="checked" size="16px" />
          </div>
        </template>
      </van-nav-bar>
      <div class="tip">请用手指在虚线框中绘制签名</div>
    </div>
    <canvas></canvas>
    <div class="control">
      <div class="clean" @click="clear">清屏</div>
      <div class="confirm" @click="confirm">确定</div>
    </div>
  </div>
</template>

<script>
import { NavBar } from "@ylz/vant"
import Draw from "../../../utils/draw"

export default {
  components: { "van-nav-bar": NavBar },
  name: "y-draw",
  data() {
    return {
      checked: false
    }
  },
  mounted() {
    this.$nextTick(function() {
      this.canvasBox = document.getElementById("canvasBox")
      this.initCanvas()
    })
  },

  computed: {
    degree() {
      // 屏幕整体旋转的角度, 可取 -90,90,180等值
      return this.checked ? 90 : 0
    },
    getHorizontalStyle() {
      const d = document
      const w =
        window.innerWidth || d.documentElement.clientWidth || d.body.clientWidth
      const h =
        window.innerHeight ||
        d.documentElement.clientHeight ||
        d.body.clientHeight
      let length = (h - w) / 2
      let width = w
      let height = h

      switch (this.degree) {
      case -90:
        length = -length
        // eslint-disable-next-line no-fallthrough
      case 90:
        width = h
        height = w
        break
      default:
        length = 0
      }
      if (this.canvasBox) {
        this.canvasBox.replaceChild(
          document.createElement("canvas"),
          document.querySelector("canvas")
        )
        // eslint-disable-next-line vue/no-async-in-computed-properties
        setTimeout(() => {
          this.initCanvas()
        }, 200)
      }
      return {
        transform: `rotate(${this.degree}deg) translate(${length}px,${length}px)`,
        width: `${width}px`,
        height: `${height}px`,
        transformOrigin: "center center"
      }
    }
  },
  methods: {
    initCanvas() {
      const canvas = document.querySelector("canvas")
      this.draw = new Draw(canvas, -this.degree)
    },
    handleBack() {
      this.$emit("cancel", false)
    },
    clear() {
      this.draw.clear()
    },
    confirm() {
      if (this.isCanvasBlank()) {
        return this.$toast("请签名")
      }

      this.$emit("confirm", this.draw.getPNGImage())
    },
    getPNGImage() {
      return this.draw.getPNGImage()
    },
    getDataURLtoBlob() {
      return this.draw.dataURLtoBlob(this.draw.getPNGImage())
    },
    //验证canvas画布是否为空函数
    isCanvasBlank() {
      const canvas = document.querySelector("canvas")
      var blank = document.createElement("canvas") //系统获取一个空canvas对象
      blank.width = canvas.width
      blank.height = canvas.height
      return canvas.toDataURL() == blank.toDataURL() //比较值相等则为空
    }
  }
}
</script>

<style lang="less">
#canvasBox {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.greet {
  .tip {
    font-size: 16px * @ratio;
    color: @second_text_color;
    text-align: center;
    padding: 10px * @ratio;
  }
  .switch {
    display: flex;
    align-items: center;
    /deep/ .van-switch {
      width: 42px * @ratio;
      height: 24px * @ratio;
      margin-left: 8px * @ratio;
    }
    /deep/ .van-switch__node {
      width: 20px * @ratio;
      height: 20px * @ratio;
    }
  }
}
.control {
  display: flex;
  text-align: center;
  width: 100%;
  height: 44px * @ratio;
  margin: 12px * @ratio auto 12px * @ratio;
  padding: 0 10px * @ratio;
  text-align: center;
  line-height: 44px * @ratio;
  font-size: 16px * @ratio;
  .clean {
    border-radius: 32px * @ratio 0 0 32px * @ratio;
    border: 0.5px * @ratio solid @main_color;
    color: @main_color;
    width: 50%;
  }
  .confirm {
    border-radius: 0 32px * @ratio 32px * @ratio 0;
    background: @main_color;
    color: @white_bg_color;
    width: 50%;
  }
}
.greet a {
  cursor: pointer;
}
.greet select {
  font-size: 18px;
}
canvas {
  flex: 1;
  cursor: crosshair;
  border-radius: 2px * @ratio;
  border: 0.5px * @ratio dashed @second_border_color;
}
</style>
