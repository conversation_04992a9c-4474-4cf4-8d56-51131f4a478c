<!--
 * @Description: 申请信息
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="apply-info">
    <van-form
      class="base-form"
      @failed="onFailed"
      @submit="handleNext"
      :disabled="pageType === 'detail'"
    >
      <y-title
        content="申请人基本信息"
        moreText
        :colorMore="colorMore"
        :moreType="3"
        foldName="showBaseInfo"
        @changeFold="changeFold"
      />
      <van-cell-group inset v-show="showBaseInfo" class="border-bottom-wide">
        <van-field
          v-model="formData.aac003"
          name="aac003"
          label="姓名"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aac003"
          disabled
        />

        <y-select-dict
          v-model="formData.aac004"
          :filterabled="false"
          disabled
          :rules="formRules.aac004"
          dict-type="AAC004"
          label="性别"
          :required="required"
          is-link
        />

        <y-select-dict
          v-model="formData.ccg981"
          :rules="formRules.ccg981"
          dict-type="CCG981"
          label="证件类型"
          :required="required"
          :disabled="pageType === 'detail'"
          :filterabled="false"
          is-link
        />

        <van-field
          v-model="formData.aac002"
          name="aac002"
          label="证件号码"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aac002"
          disabled
        />

        <van-field
          v-model="formData.aae005"
          name="aae005"
          label="联系电话"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aae005"
        />

        <y-select-dict
          v-model="formData.aca111"
          :rules="formRules.aca111"
          :disabled="pageType === 'detail'"
          dict-type="ACA111"
          label="人员身份类型"
          :required="true"
          is-link
        />

        <van-field
          v-model="formData.aab299Name"
          name="aab299"
          label="户籍所在地"
          placeholder="请选择"
          type="textarea"
          :rows="2"
          :required="required"
          :rules="formRules.aab299"
          @click="handleSelectRegistered('aab299')"
          :disabled="pageType === 'detail'"
          readonly
        />

        <van-field
          v-model="formData.aab300"
          name="aab300"
          label="户籍详细地址"
          type="textarea"
          :rows="2"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aab300"
        />

        <van-field
          v-model="formData.aab302Name"
          name="aab302"
          label="现居住地"
          placeholder="请选择"
          type="textarea"
          :rows="2"
          :required="required"
          :rules="formRules.aab302"
          @click="handleSelectRegistered('aab302')"
          :disabled="pageType === 'detail'"
          readonly
        />

        <van-field
          class="laber-wider"
          v-model="formData.aab303"
          name="aab303"
          label="现居住地详细地址"
          type="textarea"
          :rows="2"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aab303"
        />
      </van-cell-group>
      <div class="separate-box" v-show="!showBaseInfo"></div>

      <y-title
        content="被申请人单位信息"
        moreText
        :colorMore="colorMore"
        :moreType="3"
        foldName="showUnitInfo"
        @changeFold="changeFold"
      />
      <van-cell-group inset v-show="showUnitInfo" class="border-bottom-wide">
        <y-select-dict
          class="laber-wider"
          v-model="formData.aac149"
          :rules="formRules.aac149"
          :disabled="pageType === 'detail'"
          :required="true"
          dict-type="AAC149"
          label="所属网络平台APP名称"
          is-link
          @change="aac149ChangeFn"
        />

        <van-field
          v-if="isShowOtherApp"
          class="label-width"
          v-model="formData.aac148"
          name="aac148"
          label="其他所属网络平台APP名称"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aac148"
        />

        <van-field
          v-model="formData.aac152"
          name="aac152"
          label="平台账号"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aac152"
        />

        <y-select-dict
          v-model="formData.aac150"
          :rules="formRules.aac150"
          :disabled="pageType === 'detail'"
          dict-type="AAC150"
          label="平台类型"
          :required="true"
          is-link
        />

        <van-field
          v-if="isShowPlatformtype"
          v-model="formData.aac147"
          name="aac147"
          label="其他平台类型"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aac147"
        />

        <y-select-dict
          class="laber-wider"
          v-model="formData.zgbm00"
          :rules="formRules.zgbm00"
          dict-type="ZGBM00"
          :disabled="pageType === 'detail'"
          label="行业监管（主管）"
          :required="true"
          is-link
        />

        <y-select-dict
          v-model="formData.aac151"
          :rules="formRules.aac151"
          dict-type="AAC151"
          :disabled="pageType === 'detail'"
          label="是否设立站点"
          :required="true"
          is-link
          @change="aac151ChangeFn"
        />

        <van-field
          v-if="isShowSite"
          v-model="formData.aac153"
          name="aac153"
          label="所属站点名称"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aac153"
        />

        <van-field
          v-if="isShowSite"
          v-model="formData.aac154"
          name="aac154"
          label="所属站点地址"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aac154"
        />

        <van-field
          v-model="formData.aab004"
          name="aab004"
          label="单位名称"
          type="textarea"
          :rows="2"
          placeholder="请输入"
          :required="required"
          :readonly="true"
          :rules="formRules.aab004"
          @click="showPicker = true"
        />

        <van-field
          v-model="formData.aab009"
          name="aab009"
          label="单位联系人"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aab009"
        />

        <van-field
          v-model="formData.aae007"
          name="aae007"
          label="单位联系电话"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aae007"
        />

        <van-field
          v-model="formData.aae008"
          name="aae008"
          label="单位地址"
          type="textarea"
          :rows="2"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aae008"
        />
      </van-cell-group>
      <div class="separate-box" v-show="!showUnitInfo"></div>

      <y-title
        content="申请人请求"
        moreText
        :colorMore="colorMore"
        :moreType="3"
        foldName="showRequestInfo"
        @changeFold="changeFold"
      />
      <van-cell-group inset v-show="showRequestInfo">
        <van-field
          v-model="formData.ywlx"
          name="ywlx"
          label="业务类型"
          placeholder="请输入"
          :required="required"
          readonly
          :rules="formRules.ywlx"
        />

        <y-select-dict
          v-model="formData.aba002"
          :rules="formRules.aba002"
          dict-type="ABA002"
          :disabled="pageType === 'detail'"
          label="案件类别"
          type="textarea"
          :rows="2"
          :required="true"
          @click="pageType !== 'detail' && (showCase = true)"
          is-link
        />

        <van-field
          type="textarea"
          v-model="formData.abb286"
          name="abb286"
          label="案情描述"
          maxlength="500"
          placeholder="请输入"
          :required="required"
          :rules="formRules.abb286"
        />

        <van-field
          type="textarea"
          v-model="formData.aba003"
          name="aba003"
          label="申请人诉求"
          maxlength="500"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aba003"
        />

        <!-- <van-field
          class="laber-wider"
          type="textarea"
          v-model="formData.aba004"
          name="aba004"
          maxlength="500"
          label="其他纠纷案由描述"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aba004"
        /> -->
      </van-cell-group>

      <!-- 单位名称查询弹窗 -->
      <van-popup v-model="showPicker" position="bottom">
        <van-field
          class="search-field"
          clickable
          v-model.trim="unitName"
          right-icon="search"
          placeholder="搜索单位名称"
          @click-right-icon="handleSearchUnit"
          input-align="left"
          label-align="left"
        >
        </van-field>
        <van-picker
          show-toolbar
          :columns="unitList"
          @confirm="handleConfirmUnit"
          @cancel="showPicker = false"
        >
        </van-picker>
      </van-popup>

      <!-- 案件类别弹窗 -->
      <van-popup v-model="showCase" round position="bottom">
        <van-cascader
          v-model="caseValue"
          title="请选择案件类别"
          :options="caseList"
          :closeable="false"
          @finish="selectCaseFinish"
        />
      </van-popup>

      <!-- 户籍所在地 现居住地 -->
      <van-popup v-model="showRegisteredAddress" position="bottom">
        <van-search
          class="select-search"
          v-model="searchValue"
          placeholder="请输入搜索关键词"
        />
        <van-picker
          show-toolbar
          :columns="registeredAddressList"
          @confirm="handleConfirmAddress"
          @cancel="showRegisteredAddress = false"
        >
        </van-picker>
      </van-popup>

      <div class="button-box-more">
        <van-button
          v-if="pageType !== 'detail'"
          plain
          type="info"
          @click="handleSave"
          native-type="button"
        >
          保 存
        </van-button>
        <van-button
          plain
          type="info"
          @click="handleCancle"
          native-type="button"
        >
          {{ pageType === "detail" ? "返 回" : "取 消" }}
        </van-button>

        <van-button
          v-if="pageType !== 'add'"
          round
          block
          type="primary"
          native-type="submit"
        >
          下一步
        </van-button>
        <!-- @click="handleNext" -->
        <van-button v-else round block type="primary" native-type="submit">
          下一步
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import { five_text_color } from "@/styles/theme/theme-params.less"
import { validateIdCard, checkMobile, validContact } from "@utils/check"

import { commonApi } from "@/api"
import { removeEmptyChildren } from "@/utils/common"

export default {
  name: "apply-info",
  model: {
    prop: "formData"
  },
  props: {
    formData: {
      type: Object,
      require: true
    },
    pageType: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      // 标题
      colorMore: five_text_color,
      showBaseInfo: true, //申请人基本信息
      showUnitInfo: true, //被申请人人单位信息
      showRequestInfo: true, //申请人请求

      // 表单
      formRules: {
        aac003: [{ required: true, message: "请输入" }],
        aac004: [{ required: true, message: "请选择" }],
        ccg981: [{ required: true, message: "请选择" }],
        aac002: [
          { required: true, message: "请输入" },
          {
            validator: validateIdCard,
            message: "请输入正确身份证号码",
            trigger: "onBlur"
          }
        ],
        aae005: [
          { required: true, message: "请输入" },
          {
            validator: checkMobile,
            message: "请输入正确的手机号",
            trigger: "onBlur"
          }
        ],
        aca111: [{ required: true, message: "请选择" }],
        aab299: [{ required: true, message: "请选择" }],
        aab302: [{ required: true, message: "请选择" }],

        aac149: [{ required: true, message: "请选择" }],
        aac152: [{ required: true, message: "请输入" }],
        aac150: [{ required: true, message: "请选择" }],
        aac147: [{ required: true, message: "请输入" }],
        zgbm00: [{ required: true, message: "请选择" }],
        aac151: [{ required: true, message: "请选择" }],
        aab004: [{ required: true, message: "请输入" }],
        aab009: [{ required: true, message: "请输入" }],
        aae007: [
          { required: true, message: "请输入" },
          {
            validator: validContact,
            message: "请输入手机号或固定电话(请去除'-'符号)",
            trigger: "onBlur"
          }
        ],
        aae008: [{ required: true, message: "请输入" }],
        abb286: [{ required: true, message: "请输入" }],
        aba003: [{ required: true, message: "请输入" }],
        aba002: [{ required: true, message: "请选择" }],
        aac154: [{ required: true, message: "请输入" }],
        aac153: [{ required: true, message: "请输入" }],
        aac148: [{ required: true, message: "请输入" }],
        aab300: [{ required: true, message: "请输入" }],
        aab303: [{ required: true, message: "请输入" }],
        aba004: [{ required: true, message: "请输入" }]
      },
      required: true,
      disabledCardType: false, //是否禁用证件类型

      //案件类别
      showCase: false,
      caseValue: "",
      caseList: [], //案件类别列表

      // 单位名称
      showPicker: false, //单位名称弹窗
      unitName: "", //单位名称
      unitList: [], //单位列表

      // 户籍所在地 现居住地
      showRegisteredAddress: false,
      activeAddressField: "aab299", // 地址选择默认
      adressList: [], //所有地址
      registeredAddressList: [], //下拉显示的地址

      // 查询 现居住地
      searchValue: ""
    }
  },
  watch: {
    searchValue: {
      handler(val) {
        if (val) {
          this.filterData(val)
        }
      },
      immediate: true
    },
    "formData.aac149": {
      handler() {
        if (this.pageType !== "add") {
          return
        }
        this.formData.aac148 = ""
      }
    },
    "formData.aac151": {
      handler() {
        if (this.pageType !== "add") {
          return
        }
        this.formData.aac153 = ""
        this.formData.aac154 = ""
      }
    },
    "formData.aac150": {
      handler() {
        if (this.pageType !== "add") {
          return
        }
        this.formData.aac147 = ""
      }
    }
  },
  computed: {
    isShowOtherApp() {
      //是否显示其他所属网络平台app名称
      const { aac149 } = this.formData
      return aac149 === "007"
    },
    isShowSite() {
      //是否显示站点信息
      const { aac151 } = this.formData
      return aac151 === "002"
    },
    isShowPlatformtype() {
      //是否显示其他平台类别
      const { aac150 } = this.formData
      return aac150 === "099"
    }
  },
  async created() {
    await this.getRegisteredAddress() //查询户籍地址字典
    this.setRegisteredAddress() //设置户籍地址

    this.handleSearchUnit() // 查询单位信息

    this.getTreeAa10ByAaa100() // 查询案件类别
  },
  methods: {
    //查询户籍地址字典
    async getRegisteredAddress() {
      const params = {
        serviceName: "xytBc01_getAab299Aa10"
      }

      const res = await commonApi.proxyApi(params)

      const { data = [] } = res.map
      const list = data.map((item) => ({
        value: item.aaa102,
        text: item.aaa103
      }))
      this.registeredAddressList = JSON.parse(JSON.stringify(list))
      this.adressList = list
    },
    // 搜索地址
    filterData(val) {
      this.registeredAddressList = this.adressList.filter((item) => {
        return item["text"].includes(val)
      })
    },
    // 设置户籍地址
    setRegisteredAddress() {
      const { aab299, aab302 } = this.formData
      const aab299List = this.adressList.filter((item) => item.value == aab299)
      const aab302List = this.adressList.filter((item) => item.value == aab302)

      aab299List.length > 0
        ? this.$emit("updateFormData", { aab299Name: aab299List[0].text })
        : false
      aab302List.length > 0
        ? this.$emit("updateFormData", { aab302Name: aab302List[0].text })
        : false
    },
    // 选择地址
    handleConfirmAddress(data) {
      const { text, value } = data
      const info = {}
      info[this.activeAddressField] = value
      info[`${this.activeAddressField}Name`] = text
      this.$emit("updateFormData", { ...info })
      this.showRegisteredAddress = false
    },

    // 查询案件类别
    getTreeAa10ByAaa100() {
      const params = {
        serviceName: "xytCommon_getTreeAa10ByAaa100",
        aaa100: "ABA002"
      }
      commonApi.proxyApi(params).then((res) => {
        const { data } = res.map

        const newData = removeEmptyChildren(data)
        this.caseList = this.formatCaseList(newData)
      })
    },
    // 案件类别数据格式化
    formatCaseList(arr) {
      if (arr?.length) {
        for (const i in arr) {
          arr[i].text = arr[i].aaa103
          arr[i].value = arr[i].aaa102
          this.formatCaseList(arr[i].children)
        }
      }
      return arr
    },

    selectCaseFinish({ selectedOptions }) {
      console.log(selectedOptions, "selectedOptions")
      this.showCase = false
      this.$emit("updateFormData", { aba002: this.caseValue })
    },

    // 查询单位信息
    handleSearchUnit() {
      const params = {
        serviceName: "xytCommon_findAb01NewByName",
        aab004: this.unitName
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "res")
        const { rows } = res.map.data
        this.unitList = rows.map((item) => {
          return {
            text: item.aab004,
            value: item.aab001,
            aab009: item.aae004,
            aae007: item.aae005,
            aae008: item.aae008
          }
        })
        console.log(this.unitList, "this.unitList")
      })
    },

    // 确认选择单位
    handleConfirmUnit(data) {
      this.showPicker = false
      const { text: aab004, value: aab001, aab009, aae007, aae008 } = data
      this.$emit("updateFormData", { aab004, aab001, aab009, aae007, aae008 })
    },

    // 表单校验失败
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },

    // 折叠表单
    changeFold({ foldName, state }) {
      console.log(foldName, "foldName")
      console.log(state, "state")
      this[foldName] = state
    },

    // 保存 存储数据 返回
    handleSave() {
      this.$emit("handleSave", "info", this.materialNum)
    },

    // 取消
    handleCancle() {
      this.$router.go(-1)
    },

    // 下一步
    handleNext() {
      this.$emit("handleNext", 1)
    },
    // 户籍所在地 现居住地
    handleSelectRegistered(val) {
      if (this.pageType === "detail") {
        return
      }

      this.showRegisteredAddress = true
      this.activeAddressField = val
    },
    /**
     * @description 所属网络平台APP名称修改时重置
     *
     *
     * */
    aac149ChangeFn(){
      this.formData.aac148 = ""
    },
    /**
     * @description 是否有设站点
     *
     *
     * */
    aac151ChangeFn(){
      this.formData.aac153 = ""
      this.formData.aac154 = ""
    }
  }
}
</script>

<style lang="less" scoped>
.search-field {
  /deep/.van-field__control {
    text-align: left;
  }
  /deep/.van-cell__value .van-field__body > textarea {
    padding: unset;
  }
}
/deep/.y-select{
  .van-field__control{
    white-space: unset !important;
  }
}
</style>
