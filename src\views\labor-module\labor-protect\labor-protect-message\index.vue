<!--
 * @Description: 资讯中心
 * @Author: wujh
 * @date: 2024/1/31 16:12
 * @LastEditors: wujh
-->
<template>

  <div class="information-center-container">
    <business-tabs :tabList="tabList"  @handleAdd="handleAdd"></business-tabs>

    <div class="tabs-box">
        <van-list
            ref="vanList"
            :finished="finished"
            finished-text="没有更多了"
            :immediate-check="false"
            @load="onLoad"
        >
          <div
              class="tabs-item"
              v-for="(item, key) in infoList"
              :key="key + 'dxal'"
              @click="openPageByNew(item)"
          >
            <div class="item-left">
              <div class="item-top text-ellipsis-2 ellipsis-2">{{ item.title }}</div>
              <div class="item-bottom">
                <p class="item-tip">{{ item.source }}</p>
                <p class="item-date">{{ item.publishDate }}</p>
              </div>
            </div>
          </div>
        </van-list>
    </div>
  </div>
</template>

<script>
import {commonApi} from "@api"
import BusinessTabs from "@/components/business/business-tabs/index.vue"

export default {
  name: "information-center",
  components: { BusinessTabs},
  data(){
    return {
      tabList: [
        { title: "留言列表", number: "0" }
      ],
      pageForm: {
        pageNumber: 1,
        pageSize: 10,
        totalNumber: 0
      },
      bannerLabel: [],
      selectd: "dxal",
      news: {},
      banners: [],
      finished: false,
      catalogIdMap: {},
      infoList: []
    }
  },
  created() {
    const dxalItem = {
      label: "典型案例",
      key: "dxal"
    }

    this.bannerLabel.push(dxalItem)
    this.selectd = "dxal"
    this.onLoad()
  },
  methods: {
    handleAdd() {
      this.$router.push({
        path: "/labor-protect-message/add"
      })
    },
    // 查询典型案例列表
    async fetchTypicalCaseData(pageForm) {
      const {pageNumber: page, pageSize: size} = pageForm
      const params={
        serviceName: "xytBc08Web_findBc08ByPage",
        source: " 002",
        page,
        size
      }
      const res = await commonApi.proxyApi(params)
      const {rows=[], total} = res?.map?.data || {}
      console.log(res, "查询典型案例列表")
      const list = rows.map(item => {
        return {
          showType: "1",
          bcz008: item.bcz008,
          message: item.message,
          title: item.title,
          publishDate: item.created_at,
          source: item.name
        }
      })
      if (!this.news["dxal"]?.contentList){
        this.news["dxal"] = {
          contentList: [],
          finished: false
        }
      }
      const contentList = this.news["dxal"].contentList.concat(list)
      this.pageForm.totalNumber = Number(total)
      const finished = this.news["dxal"].contentList.length >= total
      this.news["dxal"] = {
        pageForm: { ...this.pageForm },
        contentList,
        finished
      }
    },
    openPageByNew(item){
      const { bcz008 } = item
      const query = { showType: "1", bcz008}
      this.$router.push({
        path: "/labor-protect-message/detail",
        query
      })
    },
    async onLoad(){
      this.finished = true

      console.log(this.news[this.selectd]?.pageForm, "this.news[this.selectd]?.pageForm")
      let { pageNumber = 0 } = this.news[this.selectd]?.pageForm || {}
      const page = pageNumber += 1

      if (this.selectd === "dxal") { //典型案例 单独调用
        await this.fetchTypicalCaseData({
          pageNumber: page,
          pageSize: 10
        })
      } else {
        await this.getContentListFn({
          catalogId: this.catalogIdMap[this.selectd],
          alias: this.selectd
        }, {
          pageNumber: page,
          pageSize: 10
        })
      }

      this.finished = this.news[this.selectd].finished
      this.infoList = this.news[this.selectd].contentList
    }
  }
}
</script>

<style scoped lang="less">
.information-center-container{
  .seek-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    .seek-title {
      font-size: 18px;
      font-weight: bold;
      color: @main_text_color;
      line-height: 25px;
    }
    .more {
      font-size: 14px;
      font-weight: bold;
      color: @five_text_color;
      line-height: 20px;
    }
  }
  .tabs-box {
    .van-list{
      padding: 0 16px;
    }
    ::v-deep .van-tab {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }
    ::v-deep .van-tabs__content {
      padding: 0 0 0 14px;
    }
    .tabs-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      &:not(:last-child) {
        border-bottom: 1px solid #EEEEEE;
      }
      .item-left {
        .item-top {
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
        }
        .item-bottom {
          margin-top: 20px;
          .item-tip {
            font-size: 11px;
            font-weight: 400;
            color: @main_color;
            line-height: 16px;
          }
          .item-date {
            font-size: 12px;
            font-weight: 400;
            color: @five_text_color;
            line-height: 18px;
            //margin-left: 20px;
          }
        }
      }
      .item-right {
        width: 108px;
        margin-left: 18px;
        border-radius: 4px;
        & > img {
          width: 100%;
          max-width: 180px;
          object-fit: contain;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
