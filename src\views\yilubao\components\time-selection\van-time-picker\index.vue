<!--
 * @Description: 时间段选择器
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <van-picker
    ref="timeVanPicker"
    :title="title"
    :show-toolbar="showToolbar"     
    :columns="columns"
    @confirm="onConfirm"
    @cancel="onCancel"
    @change="onChange"
    v-on="$listeners"
    :confirm-button-text="confirmButtonText"
    :cancel-button-text="cancelButtonText"
    :readonly="readonly"
    :item-height="itemHeight"
    :visible-item-count="visibleItemCount"
    :swipe-duration="swipeDuration"
  >
    <!-- <template slot="default">
      <slot name="default"></slot>
    </template>
    <template slot="title">
      <slot name="title"></slot>
    </template>
    <template slot="confirm">
      <slot name="confirm"></slot>
    </template>
    <template slot="cancel">
      <slot name="cancel"></slot>
    </template>
    <template slot="option">
      <slot name="option"></slot>
    </template>
    
    <template slot="columns-top">
      <slot name="columns-top"></slot>
    </template>
    <template slot="columns-bottom">
      <slot name="columns-bottom"></slot>
    </template> -->
  </van-picker>
</template>
  
<script>

export default {
  name: "van-time-picker",
  props: {
    showToolbar: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      default: () => ""
    },
    confirmButtonText: {
      type: String,
      default: () => "确认"
    },
    cancelButtonText: {
      type: String,
      default: () => "取消"
    },
    readonly: {
      type: Boolean,
      default: () => false
    },
    itemHeight: {
      type: Number||String,
      default: () => 44
    },
    visibleItemCount: {
      type: Number||String,
      default: () => 6
    },
    swipeDuration: {
      type: Number||String,
      default: () => 1000
    }
  },
  data() {
    return {
      list: []
    }
  },
  
  computed: {
    columns: function() {
      return this.setData()
    }
  },
  mounted() {
    this.$emit("timePickerMounted")
  },
  methods: {
    setValues(values) {
      this.$refs.timeVanPicker.setValues(values)
    },
    setData() {
      const hours = []
      const minutes = []
      for (let i = 0; i < 24; i++) {
        const iStr = i < 10 ? `0${i}` : `${i}`
        hours.push(iStr)
      } 
      for (let i = 0; i < 60; i++) {
        const iStr = i < 10 ? `0${i}` : `${i}`
        minutes.push(iStr)
      }
      this.list = [
        {
          defaultIndex: 1,
          values: hours
        },
        {
          defaultIndex: 2,
          values: minutes
        },
        {
          values: ["-"]
        },
        {
          defaultIndex: 3,
          values: hours
        },
        {
          defaultIndex: 4,
          values: minutes
        }
      ]
      return this.list
    },
    onConfirm(value, index) {
      // const [startHour, startMinutes, temp, endHour, endMinutes] = value
      // console.log(temp)
      // // if (startHour == endHour && startMinutes >= endMinutes || startHour > endHour) {
      // //   this.$toast("请选择有效时段")
      // //   return
      // // }
      this.$emit("confirm", value, index)
    },
    onChange(picker, values, index) {
      /* if (index == 0) {
        picker.setColumnValues(1, this.list[1].values.filter(item => item >= values[0]))
        picker.setColumnValues(3, this.list[3].values.filter(item => item >= values[0]))
      } */
      this.$emit("change", values, index)
    },
    onCancel() {
      this.$emit("cancel")
    }
  }
}
</script>
