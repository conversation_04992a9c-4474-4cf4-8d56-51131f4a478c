/*
 * @Description: 调用微信SDK配置文件
 * @Version: 0.1
 * @Autor: hwx
 */
import wx from 'weixin-js-sdk'
import { commonApi } from "@/api"

// 微信SDK签名配置
function setSignature({ debug = false, appId, timestamp, nonceStr, signature }) {
  console.log('微信SDK签名配置:', { debug, appId, timestamp, nonceStr, signature })
  wx.config({
    debug, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
    appId, // 必填，公众号的唯一标识
    timestamp, // 必填，生成签名的时间戳
    nonceStr, // 必填，生成签名的随机串
    signature, // 必填，签名
    jsApiList: [
      'getLocation',
      'openLocation',
      'chooseImage',
      'previewImage'
    ], // 必填，需要使用的JS接口列表
    openTagList: ['wx-open-launch-weapp'] //跳转小程序标签
  })
}

// 签名配置
export function setWxConfig() {
  const url = location.href.split('#')[0]
  return new Promise((resolve, reject) => {
    commonApi.externalApi({ url, custom: true }).then(res => {
      if (!res.resultBody) {
        reject()
      }
      const { appId, timestamp, nonceStr, signature } = res.resultBody.ws
      setSignature({ appId, timestamp: timestamp && Number(timestamp), nonceStr, signature })
      console.log('签名配置成功')
      wx.ready(() => {
        resolve(res)
      })
      wx.error((err) => {
        reject(err)
        console.log(`签名配置出错了：${JSON.stringify(err)}`)// 这个地方的好处就是wx.config配置错误，会弹出窗口哪里错误，然		后根据微信文档查询即可。
      })
    })
      .catch(err => {
        console.log('签名配置getWxConfig错误')
        reject(err)
      })
  })

}

// 微信获取定位
export function getLocation(fn) {
  return new Promise((resolve, reject) => {
    wx.ready(() => {
      console.log('微信获取定位开始')
      wx.getLocation({
        type: 'gcj02', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        success: function (res) {
          console.log('微信获取定位', res)
          fn && fn(res)
          resolve(res)
        },
        fail(err) {
          console.log('失败，微信获取定位', err)
          fn && fn(res)
          reject(err)
        }
      })
      wx.error((err) => {
        fn && fn(res)
        console.log('ready失败，微信获取定位内部', err)
        reject(err)
      })
    })
    wx.error((err) => {
      console.log('ready失败，微信获取定位外部', err)
      fn && fn(res)
      reject(err)
    })
  })
}

// 微信内置地图查看位置接口
export function openLocation(positionInfo) {
  return new Promise((resolve, reject) => {
    wx.ready(() => {
      console.log('地图查看开始')
      const { lat, lng, name, address } = positionInfo
      wx.openLocation({
        latitude: lat && Number(lat), // 纬度，浮点数，范围为90 ~ -90
        longitude: lng && Number(lng), // 经度，浮点数，范围为180 ~ -180。
        name, // 位置名
        address, // 地址详情说明
        scale: 20, // 地图缩放级别,整型值,范围从1~28。默认为最大
        infoUrl: '', // 在查看位置界面底部显示的超链接,可点击跳转
        success: function (res) {
          console.log('成功,地图查看', res)
          resolve(res)
        },
        fail(err) {
          console.log('失败，地图查看', err)
          reject(err)
        }
      })
      wx.error((err) => {
        console.log('ready失败，微信获取定位内部', err)
        reject(err)
      })
    })
    wx.error((err) => {
      console.log('ready失败，地图查看外部', err)
      reject(err)
    })
  })
}