const { NodeSSH } = require("node-ssh")
const ssh = new NodeSSH()
var path = require("path")
const versionObj = require("./version.json")
const binUtil = require("./bin-util")
const dayjs = require("dayjs")
const time = dayjs().format("YYYYMMDDHHmmss")
const configBase64Str = "************************************************************************************************************************************************************************************************************************************"

const buff = Buffer.from(configBase64Str, "base64")
const configStr = buff.toString("ascii")

const config = JSON.parse(configStr)

const serverPath = config.serverPath // 文件目标地址
const filePath = config.filePath // 文件目标地址
const fileName = config.fileName
const version = binUtil.getVersion(versionObj, "prod")

console.log("version", version)
ssh.connect({
  host: config.host,
  username: config.username,
  privateKey: config.privateKey,
  port: config.port,
  password: config.password
})
  .then(async(e) => {
    console.log("登录远程服务器成功")
    console.log("开始上传：" + filePath + `/${fileName}.tar`)
    await ssh.putFile(path.resolve(__dirname, `../${fileName}.tar`), filePath + `/${fileName}.tar`)
      .then(function(result) {
        console.log("上传成功：" + filePath + `/${fileName}.tar`)
      }).catch((err) => {
        console.error(err)
        process.exit()
      })

    // 旧的包压缩备份
    console.log(`开始备份：${filePath}/${fileName}`)
    let tarName = fileName + "_" + version
    await ssh.execCommand(`tar -cvf ${tarName}_${ time }.tar ${fileName}`, { cwd: filePath })
      .then(function(result) {
        // console.log(`旧包压缩备份为: ${filePath}/${tarName}_${ time }.tar`)
        tarName = ` ${filePath}/${tarName}_${ time }`
      }).catch((err) => {
        console.error(err)
      })
    console.log(`移动备份文件到：${filePath}/h5-history`)
    // 移除旧的包
    await ssh.execCommand(`mv ${tarName}.tar ${filePath}/h5-backups/`, { cwd: `${filePath}`})
      .then(function(result) {
        console.log(`备份成功：${filePath}/${fileName}`)
      }).catch((err) => {
        console.error(err)
      })

    // 开始移除
    await ssh.execCommand(`rm -rf ${fileName}`, { cwd: `${filePath}`})
      .then(function(result) {
        console.log(`移除成功：${filePath}/${fileName}`)
      }).catch((err) => {
        console.error(err)
      })

    console.log(`开始解压:${fileName}.tar`)
    await ssh.execCommand(`tar -xvf ${fileName}.tar`, { cwd: filePath})
      .then(function(result) {
        console.log(`解压成功:${fileName}.tar`)
      }).catch((err) => {
        console.error(err)
      })

    console.log(`删除nginx目录下的:${fileName}`)
    // await ssh.execCommand(`sudo rm -rf ${fileName}`, { cwd: serverPath})
    //   .then(function(result) {
    //     console.log(`解压成功:${fileName}.tar`)
    //   }).catch((err) => {
    //     console.error(err)
    //   })

    // console.log(`更新nginx目录下的:${fileName}`)
    // await ssh.execCommand(`sudo scp -r ${fileName} ${serverPath}`, { cwd: filePath})
    //   .then(function(result) {
    //     console.log(`更新成功`)
    //     // process.exit()
    //   }).catch((err) => {
    //     console.error(err)
    //     process.exit()
    //   })

    console.log(`删除目录下的:${fileName}.tar`)
    await ssh.execCommand(`rm -rf ${fileName}.tar`, { cwd: filePath})
      .then(function(result) {
        console.log(`删除成功:${fileName}.tar`)
        process.exit()
      }).catch((err) => {
        console.error(err)
      })
    process.exit()
  })
  .catch((err) => {
    console.error(err)
  })