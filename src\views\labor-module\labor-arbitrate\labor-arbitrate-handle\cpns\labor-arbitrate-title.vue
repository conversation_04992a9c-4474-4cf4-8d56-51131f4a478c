
<!--
 * @Description: 业务标题
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-arbitrate-title">
    <div class="title">{{title}}</div>
    <div v-if="showPackUp" :class="['pack-up' , 'flex-c-c', isOpen ? 'pack-up-open' : '']" @click="handlePackUp">
      <span>{{isOpen ? '收起' : '展开'}}</span>
      <img src="@/assets/imgs/labor-arbitrate/<EMAIL>" alt="">
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    showPackUp: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isOpen: false
    }
  },
  watch: {
    showPackUp(val) {
      this.isOpen = val
    }
  },
  methods: {
    handlePackUp() {
      this.isOpen = !this.isOpen
      this.$emit("handlePackUp", this.isOpen)
    }
  }
}
</script>
<style lang="less" scoped>
.labor-arbitrate-title {
  padding: 14px 14px 14px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    font-size: 14px;
    color: @main_text_color;
    line-height: 20px;
    position: relative;
    &::before {
      position: absolute;
      content: '*';
      top: 0;
      left: -8px;
      color: @danger_color;
    }
  }
  .pack-up {
    font-size: 14px;
    color: #999999;
    line-height: 20px;
    & > img {
      width: 26px;
      height: 26px;
      transform: rotate(0);
      transition: all 0.3s;
    }
    &-open > img {
      transform: rotate(180deg);
      transition: all 0.3s;
    }
  }
}
</style>