<!--
 * @Description: 添加预申请人
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div>
    <!-- 提示 -->
    <y-tips v-if="formData.aac001 === '002'" :showTitle="false" :tipsList="tipsList"></y-tips>

    <!-- 申请表单 -->
    <van-form ref="baseForm" class="base-form" label-width="160" :disabled="pageType === 'details'" @failed="onFailed"
      @submit="handleSave">
      <y-select-dict v-model="formData.aac001" label="预申请人类型" :rules="formRules.aac001" dict-type="AAC001"
        :filterabled="false" :required="true" :disabled="pageType === 'details'" is-link />

      <!-- 用人单位 -->
      <van-cell-group v-show="formData.aac001 === '002'" inset class="border-bottom-wide">

        <!-- 单位名称搜索 -->
        <y-search-unit v-model="formData"></y-search-unit>

        <y-select-dict v-model="formData.aae011" label="单位类型" :required="true" :rules="formRules.aae011"
          dict-type="AAE011" :filterabled="false" :disabled="pageType === 'details'" is-link />
        <van-field v-model="formData.aae010" name="aae010" label="住所" placeholder="请输入" :required="true"
          :rules="formRules.aae010" />
        <van-field v-model="formData.aae012" name="aae012" label="注册地址" placeholder="请输入" type="textarea"
          input-align="left" :autosize="{ maxHeight: 70, minHeight: 50 }" maxlength="100" :required="true"
          :rules="formRules.aae012" />
        <van-field v-model="formData.aab013" name="aab013" label="法定代表人" placeholder="请输入" :required="true"
          :rules="formRules.aab013" />
        <van-field v-model="formData.aae014" name="aae014" class="label-width" label="法人身份证" placeholder="请输入"
          :rules="formRules.aae014" />
        <van-field class="label-width" v-model="formData.aae015" name="aae015" label="统一社会信用代码" placeholder="请输入"
          :required="true" :rules="formRules.aae015" />
        <van-field v-model="formData.aae008" name="aae008" label="联系电话" placeholder="请输入" :required="true"
          :rules="formRules.aae008" />

        <van-field class="alone-name" label="营业执照" :disabled="true" />

        <div class="uploader-container-alone">
          <van-uploader v-model="permitList" :disabled="pageType === 'details'" :deletable="pageType !== 'details'"
            max-count="1" :preview-full-image="true" :after-read="afterRead('permitList', '1')" />
        </div>
      </van-cell-group>

      <!-- 自然人 -->

      <van-cell-group v-show="formData.aac001 === '001'" inset>
        <y-select-dict class="label-width" v-model="formData.aac143" label="是否为他人预申请" :required="true"
          :rules="formRules.aac143" dict-type="AAC143" :filterabled="false" :disabled="pageType === 'details'"
          is-link />
        <y-select-dict v-model="formData.ccg981" label="证件类型" :required="true" :rules="formRules.ccg981"
          dict-type="CCG981_ZC" :filterabled="false" :disabled="pageType === 'details'" is-link />

      </van-cell-group>

      <van-cell-group v-show="formData.aac001 === '001'">
        <div class="uploader-title">上传证件信息</div>
        <div class="uploader-container">
          <van-uploader v-model="IDListOne" :disabled="pageType === 'details'" :deletable="pageType !== 'details'"
            :after-read="afterRead('IDListOne', '1')" :preview-full-image="true" @oversize="handleOversize"
            accept='.jpg,.jpeg,.png' max-size="3 * 1024 * 1024" max-count="1">
            <template #default>
              <div class="uploader-box">
                <img src="@/assets/imgs/labor-arbitrate/<EMAIL>" alt="" />
                <span>点击上传证件头像面</span>
              </div>
            </template>
          </van-uploader>

          <van-uploader v-model="IDListTwo" :disabled="pageType === 'details'" :deletable="pageType !== 'details'"
            :after-read="afterRead('IDListOne', '2')" :preview-full-image="true" @oversize="handleOversize"
            accept='.jpg,.jpeg,.png' max-size="3 * 1024 * 1024" max-count="1">
            <template #default>
              <div class="uploader-box">
                <img src="@/assets/imgs/labor-arbitrate/<EMAIL>" alt="" />
                <span>点击上传证件国徽面</span>
              </div>
            </template>
          </van-uploader>
        </div>
      </van-cell-group>

      <van-cell-group v-show="formData.aac001 === '001'">
        <van-field v-model="formData.aac003" name="aac003" label="姓名" placeholder="请输入" :required="true"
          :rules="formRules.aac003" />
        <van-field v-model="formData.aac002" name="aac002" label="证件号码" placeholder="请输入" :required="true"
          :rules="formRules.aac002" />
        <y-select-dict v-model="formData.aac005" label="民族" :required="true" :rules="formRules.aac005"
          dict-type="AAC005" :disabled="pageType === 'details'" is-link />
        <y-select-dict v-model="formData.aac004" label="性别" :required="true" :rules="formRules.aac004"
          dict-type="AAC004" :filterabled="false" :disabled="pageType === 'details'" is-link />
        <van-field v-model="formData.aac006" name="aac006" label="出生日期" placeholder="请输入" :required="true"
          :rules="formRules.aac006" :readonly="true" @click="handleSelectDate('aac006')" />
        <van-field v-model="formData.aae005" name="aae005" label="联系电话" placeholder="请输入" :required="true"
          :rules="formRules.aae005" />
        <van-field v-model="formData.aae006" name="aae006" label="住址(身份证)" placeholder="请输入" type="textarea"
          input-align="left" :autosize="{ maxHeight: 70, minHeight: 50 }" maxlength="100" :required="true"
          :rules="formRules.aae006" />
        <van-field class="label-width" type="textarea" input-align="left" :autosize="{ maxHeight: 70, minHeight: 50 }"
          maxlength="100" v-model="formData.aae007" name="aae007" label="法律文书送达地址" placeholder="请输入" :required="true"
          :rules="formRules.aae007" />
      </van-cell-group>

      <div class="button-box-more" v-if="pageType !== 'details'">
        <van-button round block type="primary" native-type="submit">
          保存
        </van-button>
      </div>
    </van-form>

    <!-- 日期选择弹出层 -->
    <van-popup v-model="pickerShow" round position="bottom">
      <van-datetime-picker v-model="currentDate" type="date" title="选择日期" :min-date="minDate" :max-date="maxDate"
        @confirm="handleConfirmPicker" @cancel="pickerShow = false" />
    </van-popup>
  </div>
</template>

<script>
import isEmpty from "lodash/isEmpty"
import { commonApi } from "@/api"
import { validateIdCard, checkMobile, validContact } from "@utils/check"
import { removeBase64Header, addBase64Header } from "@/utils/fileUtil"
import { getInfoByIdCard } from "@/utils/common"

export default {
  name: "add-apply-people",
  data() {
    return {
      tipsList: [
        "*企业相关信息，可登录“国家企业信用信息公开系统”（网址：http://www.gsxt.gov.cn/index.html)进行查询，并可将查询到的页面作为企业营业执照进行上传。"
      ],

      formData: {
        aac001: "001", //预申请人类型

        // 自然人
        aac143: "000", //是否为他人预申请
        ccg981: "001", //证件类型
        aac003: "", //姓名
        aac002: "", //身份证号码
        aac005: "", //民族
        aac004: "", //性别
        aac006: "", //出生年月
        aae005: "", //联系电话
        aae006: "", //住址（身份证）
        aae007: "", //法律文书送达地址
        aac145Base64: "", //身份证头像面（上传）
        aac146Base64: "", //身份证国徽面（上传）

        // 用人单位
        aab004: "", //单位名称
        aae011: "", //单位类型
        aae010: "", //住所
        aae012: "", //注册地址
        aab013: "", //法定代表人
        aae014: "", //法人身份证
        aae015: "", //统一社会信用代码
        aae008: "", //联系电话
        aae016Base64: "" //营业执照
      },
      formRules: {},
      formRules1: {
        aac001: [{ required: true, message: "请选择" }],

        // 自然人
        aac143: [{ required: true, message: "请选择" }],
        ccg981: [{ required: true, message: "请选择" }],
        aac003: [{ required: true, message: "请输入" }],
        aac002: [
          { required: true, message: "请输入" },
          {
            validator: validateIdCard,
            message: "请输入正确身份证号码",
            trigger: "onBlur"
          }
        ],
        aac005: [{ required: true, message: "请选择" }],
        aac004: [{ required: true, message: "请选择" }],
        aac006: [{ required: true, message: "请选择" }],
        aae005: [
          { required: true, message: "请输入" },
          {
            validator: checkMobile,
            message: "请输入正确的手机号",
            trigger: "onBlur"
          }
        ],
        aae006: [{ required: true, message: "请输入" }],
        aae007: [{ required: true, message: "请输入" }]
      },
      formRules2: {
        aac001: [{ required: true, message: "请选择" }],

        // 用人单位
        aab004: [{ required: true, message: "请点击搜索单位名称" }],
        aae011: [{ required: true, message: "请选择" }],
        aae010: [{ required: true, message: "请输入" }],
        aae012: [{ required: true, message: "请输入" }],
        aab013: [{ required: true, message: "请输入" }],
        aae015: [{ required: true, message: "请输入" }],
        aae008: [
          { required: true, message: "请输入" },
          {
            validator: validContact,
            message: "请输入手机号或固定电话(请去除'-'符号)",
            trigger: "onBlur"
          }
        ]
      },

      // 日期选择
      pickerShow: false,
      currentDate: new Date(),
      pickerType: "",
      minDate: new Date(new Date().getFullYear() - 100, 0, 1),
      maxDate: new Date(),

      // 上传证件信息
      IDListOne: [],
      IDListTwo: [],

      // 营业执照
      permitList: []
    }
  },
  watch: {
    "formData.aac001": {
      handler(val) {
        if (this.pageType === "add") {
          this.initData()
          this.formData.aac001 = val
          this.formRules = val === "001" ? this.formRules1 : this.formRules2
        }
      },
      immediate: true
    },
    "formData.ccg981": {
      handler(val) {
        if (this.formData.aac001 === "002") {
          return
        }

        const requireIdCard = { required: true, message: "请输入" }
        const checkIdCard = {
          validator: validateIdCard,
          message: "请输入正确身份证号码",
          trigger: "onBlur"
        }

        this.formRules.aac002 = val === "001" ? [requireIdCard, checkIdCard] : [requireIdCard]
      },
      immediate: true
    }
  },
  computed: {
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    },
    zcy000() {
      //仲裁委员会
      return this.$route.query.zcy000
    },
    pageType() {
      return this.$route.query.pageType
    },
    bczsqr() {
      //申请人信息表主键
      return this.$route.query?.bczsqr || ""
    }
  },
  created() {
    // 检查 URL 查询参数中是否有 formData
    const formDataStr = this.$route.query.formData
    if (formDataStr) {
      try {
        const parsedFormData = JSON.parse(formDataStr)
        // 将解析后的数据合并到表单数据中
        this.formData = { ...this.formData, ...parsedFormData }
      } catch (error) {
        console.error("解析 formData 失败:", error)
      }
    }

    if (this.pageType === "add") {
      this.initFormData()
    } else {
      this.getBc05SqrById() // 查询详情
    }
  },
  methods: {
    // 初始化基本信息
    initFormData() {
      const { xm0000: aac003, zjhm00: aac002 } = this.userInfo
      const { sex: aac004, birthdate } = getInfoByIdCard(aac002)
      console.log(birthdate, "birthdate")
      const birthdateValue = this.dayFormatFn(birthdate, "date")

      this.formData = { ...this.formData, aac003, aac002, aac004, aac006: birthdateValue }
    },
    initData() {
      Object.assign(this.$data, this.$options.data())
    },

    // 查询详情
    getBc05SqrById() {
      const { bczsqr } = this
      commonApi
        .proxyApi({
          serviceName: "xytBc05Sqr_getBc05SqrById",
          bczsqr
        })
        .then((res) => {
          console.log("查询详情", res)
          const { data } = res.map
          for (const key in data) {
            if (data[key]) {
              this.formData[key] = data[key]
            }
          }
          console.log(this.formData, "this.formData")
          const { aac145Base64, aac146Base64, aae016Base64 } = this.formData
          this.IDListOne = aac145Base64
            ? [
              {
                content: addBase64Header(aac145Base64, "image/png")
              }
            ]
            : []
          this.IDListTwo = aac146Base64
            ? [
              {
                content: addBase64Header(aac146Base64, "image/png")
              }
            ]
            : []
          this.permitList = aae016Base64
            ? [
              {
                content: addBase64Header(aae016Base64, "image/png")
              }
            ] : []
        })
    },

    // 表单校验失败
    onFailed() {
      this.$toast("请完善表单信息！")
    },
    // 选择日期
    handleSelectDate(type) {
      if (this.pageType === "details") {
        return
      }
      this.pickerShow = true
      this.pickerType = type
    },
    handleConfirmPicker(val) {
      this.formData[this.pickerType] = this.dayFormatFn(val, "date")
      this.pickerShow = false
      // this.currentDate = new Date()
    },
    // 保存
    handleSave() {
      const { aac001 } = this.formData
      if (
        aac001 === "001" &&
        (isEmpty(this.IDListOne) || isEmpty(this.IDListTwo))
      ) {
        this.$dialog.alert({
          title: "提示",
          message: "请先上传证件信息！",
          theme: "round-button"
        })
        return
      }

      if (aac001 === "002" && isEmpty(this.permitList)) {
        this.$dialog.alert({
          title: "提示",
          message: "请先上传营业执照！",
          theme: "round-button"
        })
        return
      }

      this.formData.aac145Base64 =
        aac001 === "001" ? removeBase64Header(this.IDListOne[0].content) : "" //身份证信息
      this.formData.aac146Base64 =
        aac001 === "001" ? removeBase64Header(this.IDListTwo[0].content) : ""

      this.formData.aae016Base64 =
        aac001 === "002" ? removeBase64Header(this.permitList[0].content) : "" //营业执照

      this.$dialog
        .confirm({
          title: "提示",
          message: "您是否确定保存",
          showCancelButton: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        })
        .then(() => {
          const { zcy000 } = this
          commonApi
            .proxyApi({
              serviceName: "xytBc05Sqr_saveOrUpdateBc05Sqr",
              ...this.formData,
              aac002: this.formData.aac002.toUpperCase(),
              zcy000
            })
            .then((res) => {
              console.log("保存", res)
              this.$dialog
                .alert({
                  title: "提示",
                  message: "保存成功！",
                  theme: "round-button"
                })
                .finally(() => {
                  this.$router.go(-1)
                })
            })
            .catch((err) => {
              console.error(err)
            })
        })
    },

    // 文件读取完成后的回调函数
    afterRead(data) {
      console.log(this[data], data)
    },
    // 限制上传文件大小
    handleOversize() {
      this.$dialog.alert({
        title: "提示",
        message: "请上传小于3M的图片！",
        theme: "round-button"
      })
    }
  }
}
</script>
<style lang="less" scoped>
.uploader-title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 18px;
  padding: 16px 0 0 16px;
}

.uploader-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: 20px;

  /deep/.van-uploader {
    .van-uploader__input-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;

      .uploader-box {
        margin: 20px 0 0;
        border-radius: 8px;
        border: 1px dashed #cecece;
        width: 180px;
        height: 112px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;

        &>img {
          width: 98px;
          height: 64px;
        }

        &>span {
          font-size: 14px;
          color: #303133;
          line-height: 16px;
        }
      }
    }

    .van-uploader__preview {
      margin: 20px 0 0;
      border-radius: 8px;
      border: 1px dashed #cecece;
      width: 180px;
      height: 112px;
      display: flex;
      justify-content: center;
      align-items: center;

      &>img {
        width: 100%;
      }
    }
  }
}

.uploader-container-alone {
  padding: 8px 16px;
}

/deep/.alone-name {
  &::after {
    border-bottom: none !important;
  }

  .van-cell__value {
    display: none;
  }
}
</style>
