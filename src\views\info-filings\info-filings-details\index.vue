<!--
 * @Description: 详情页面
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="page-container info-filings-details">
    <div class="page-box">
      <y-title content="人员信息" fontContSize="16" fontWeight="bold" />
      <person-info :showBtn="false" v-model="formData" businessType="details"></person-info>
    </div>
    
    <div class="page-box">
      <y-title content="信息备案" fontContSize="16" fontWeight="bold" />
      <info-record :showBtn="false" v-model="formData" businessType="details"></info-record>
    </div>   

    <div class="button-box">
        <van-button @click="$router.go(-1)" plain type="info">
          关 闭
        </van-button>
      </div>
  </div>
</template>

<script>
import PersonInfo from "../cpns/person-info.vue"
import InfoRecord from "../cpns/info-record.vue"

import {commonApi} from "@api"
export default {
  name: "info-filings-details",
  components: {
    PersonInfo,
    InfoRecord
  },
  data() {
    return {
      formData: {
        // 人员信息
        aac003: "", //姓名
        aac002: "", //证件号码
        aac004: "", //性别
        aac006: "", //出生日期
        aae005: "", //联系电话
        aac011: "", //文化程度
        ccc010: "", //毕业学校
        aac183: "", //毕业专业
        ccd027: "", //毕业时间
        aab299: "", //户籍所在地
        ccd032: "", //现居住地

        // 信息备案
        ptbm00: "", //平台名称
        aab001: "", //录用企业名称
        aca111: "", //岗位
        ccd028: "", //签约方式
        ccd006: "", //签约起始时间
        sftbch: "" //是否同步参会
      }
    }
  },
  created() {
    const {cce010} = this.$route.query
    this.getCe10ById(cce010)
  },
  methods: {
    getCe10ById(cce010) {
      const params = {
        serviceName: "xytPerson_getCe10ById",
        cce010
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "getCe10ById")
        this.formData = res.map.data
      })
    }
  }
}
</script>

<style lang="less" scoped>
.button-box {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>