<!--
 * @Description: 新就业形态工种机构培训情况
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="skill-job-organ-details">
    <div class="header-box"></div>
    <div class="organ-container">
      <div class="organ-box">
        <p class="organ-name">{{organInfo.aab004}}</p>
        <p>
          <span class="label">培训类别：</span>
          <span class="value">{{organInfo.pxlb00}}</span>
        </p>
        <p>
          <span class="label">培训规模：</span>
          <span class="value">{{organInfo.npxgm0}}</span>
        </p>
        <p>
          <span class="label">单位地址：</span>
          <span class="value">{{organInfo.aae006}}</span>
        </p>
        <p>
          <span class="label">联系电话：</span>
          <span class="value">{{organInfo.lxdh00}}</span>
        </p>

        <div class="navigation-box" @click="handleNavigate">
          <img src="@pic/life-service/<EMAIL>" alt="" />
          <span>到这去</span>
        </div>
      </div>

      <div class="tabs-box flex-c-c">
        <van-tabs v-model="activeName">
          <van-tab name="introducte" title="单位简介" />
          <van-tab name="jobs" title="培训工种" />
        </van-tabs>
      </div>
      
      <div v-show="activeName === 'introducte'" class="introducte-box">
        {{ organInfo.dwjj00 }}
      </div>

      <div v-show="activeName === 'jobs'" class="jobs-box">
        <template v-if="jobsList.length > 0">
          <div class="jobs-header"></div>
          <div class="jobs-list">
            <van-cell-group class="show-cell-group" v-for="(item,index) in jobsList" :key="index">
              <van-cell class="long-cell-value" title="工种名称" :value="item.aca112" />
              <van-cell title="是否初级资格" :value="item.sfcjzg" />
              <van-cell title="是否中级资格" :value="item.sfzjzg" />
              <van-cell title="是否高级资格" :value="item.sfgjzg" />
              <van-cell title="是否技师" :value="item.sfjs" />
              <van-cell title="是否高级技师" :value="item.sfgjjs" />
              <van-field
                disabled
                v-if="!showTextarea(item.jnpxqk)"
                class="van-field-textarea"
                v-model="item.jnpxqk"
                name="abb015"
                label="近年培训情况"
                type="textarea"
                >
              </van-field>

              <van-cell v-else title="近年培训情况" :value="item.jnpxqk" />

            </van-cell-group>
          </div>
        </template>
        
        <y-empty v-else tips="暂无培训工种"></y-empty>
      </div>
      
    </div>

  </div>
</template>

<script>
import { commonApi } from "@/api"
import {setWxConfig, openLocation} from "@/utils/wechat"

export default {
  name: "skill-job-organ-details",
  data() {
    return {
      activeName: "introducte",
      organInfo: {}, // 机构信息
      jobsList: {} // 培训工种
    }
  },
  created() {
    setWxConfig() // 微信SDK签名配置

    const {pxjgid} = this.$route.query
    this.getXytgzjg(pxjgid) // 机构信息
    this.getXytdwgz(pxjgid) // 培训工种
  },
  methods: { 
    showTextarea(jnpxqk) {
      return jnpxqk && jnpxqk === "无"
    },   
    // 机构信息
    getXytgzjg(pxjgid){
      commonApi.proxyApi({
        serviceName: "xytJnpx_getXytgzjg",
        pxjgid
      }).then((res) => {
        console.log(res, "res 机构信息")
        this.organInfo = res.map?.data?.[0] || {}
      })
    },
    // 培训工种
    getXytdwgz(pxjgid){
      commonApi.proxyApi({
        serviceName: "xytJnpx_getXytdwgz",
        pxjgid
      }).then((res) => {
        console.log(res, "res 培训工种")
        this.jobsList = res.map?.data || []
      })
    },
    // 到这去
    handleNavigate() {      
      const {wd0000: lat, jd0000: lng, pxlb00: address, aae006: name} = this.organInfo     
      openLocation({lat, lng, address, name}) //微信查看地图SDK
    }    
  }
}
</script>
<style lang="less" scoped>
.skill-job-organ-details {
  .header-box {
    width: 100vw;
    height: 180px;
    background-image: url("~@/assets/imgs/skill-job-organ-query/header-bg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .organ-container {
    padding: 16px;
    background: @second_border_color;
    min-height: calc(100vh - 180px);
    .organ-box {
      width: 100%;
      background: @white_text_color;
      border-radius: 12px 12px 8px 8px;
      margin-top: -36px;
      padding: 16px;
      position: relative;
      & > p {
        font-size: 14px;
        line-height: 20px;
        color: @main_text_color;
        margin: 6px 0;
        .label {
          color: @five_text_color;
        }
      }
      .organ-name {
        font-weight: 600;
        font-size: 16px;
        color: @main_text_color;
        line-height: 22px;
      }
      .navigation-box {
        position: absolute;
        right: 10px;
        top: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-left: 12px;
        & > img {
          width: 32px;
          height: 32px;
        }
        & > span {
          margin-top: 4px;
          font-size: 10px;
          color: #999999;
          line-height: 14px;
          text-align: center;
        }
      }
    }
    /deep/.van-tabs {
      width: 60%;
      margin: 20px 0 12px;
      .van-tabs__nav {
        background: none;
      }
    }
    
    .introducte-box {
      width: 100%;
      border-radius: 8px;
      background: @white_text_color;
      font-size: 14px;
      color: @main_text_color;
      line-height: 20px;
      padding: 16px;
    }
    .jobs-box {
      .jobs-header {
        width: 100%;
        height: 8px;
        border-radius: 4px 4px 0 0;
        background-image: url("~@/assets/imgs/skill-job-organ-query/jobs-header.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .jobs-list {
        .show-cell-group {
          margin-bottom: 16px;
        }
      }      
    }
    /deep/.long-cell-value .van-cell__value {
      line-height: 22px;
      width: 70%;
    }
  }
}

</style>