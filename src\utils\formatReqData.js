/*
 * @Description: 对请求数据做转化
 * @Version: 0.1
 * @Autor: hwx
 */


import dayjs from "dayjs"
import { getAuthToken } from "@/utils/cookie"
import store from "@/store"
import { encrypt, generateSign } from "@/utils/sm-util";

export function formatReqData(data) {
  const { custom, serviceName } = data
  const formatWhiteName = ['getAuth']
  if (custom || formatWhiteName.includes(serviceName)) {
    return JSON.stringify(data)
  }

  let result = "" //拼接字符串
  if (serviceName == "xytCommon_tyscGetDataByInterface"){
    for (const key in data.data) {
      result = result + "&" + key + "=" + JSON.stringify(data.data[key])
    }
  }else{
    for (const key in data) {
      result = result + "&" + key + "=" + JSON.stringify(data[key])
    }
  }


  const timestamp = dayjs().format("YYYY-MM-DD HH:mm:ss")
  const openEnc = process.env.VUE_APP_OPENENC
  const params = {
    appId: process.env.VUE_APP_APPID,
    timestamp, //取时间戳  纳秒级别s
    version: store.getters.version || 'V1.0.0', //版本号
    openEnc, // 加密开关 1不加密0加密
    traceNo: "8aa0611b376b4403ab58ab422f9e21ac",
    ip: "*************",
    mac: "",

    serviceName, // 接口名
    token: serviceName === 'login' ? data.token : getAuthToken(), //网关token
  }

  const encData = { ...data }
  if (openEnc === '0') { //加密
    params.encData = encrypt(encData.data)
    params.signData = generateSign(result)
  } else { //不加密
    params.data = encData
    if (serviceName == "xytCommon_tyscGetDataByInterface"){
      params.data = encData.data
    }
  }

  return JSON.stringify(params)
}
