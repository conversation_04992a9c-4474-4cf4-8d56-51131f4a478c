<!--
 * @Description: 查看盒子
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="content-box">
    <template v-if="formList.length > 0">
      <y-view-container viewStatus="9999" pageSize="mini" v-for="(item, index) in formList" :key="index">
        <template slot="content">
          <van-cell-group>
            <van-cell title="保单号" :value="item.policyNo" />
            <van-cell title="投保人" :value="item.dac003" />
            <van-cell title="被保人姓名" :value="item.aac003" />
            <van-cell title="出险时间" :value="item.accidentTime" />
            <van-cell title="报案时间" :value="item.reportTime" />
            <van-cell title="立案时间" :value="item.registerTime" />
            <van-cell title="结案时间" :value="item.closingTime" />
            <van-cell title="出险地点" :value="item.cxdd00" />
            <van-cell title="报案人" :value="item.reportName" />
            <van-cell title="实际总估损" :value="item.sjgs00" />
            <van-cell title="已决赔款" :value="item.yjpk00" />
            <van-cell title="保单业务员" :value="item.da05aac003" />
            <van-cell title="理赔经办人" :value="item.lpjbry" />
            <van-cell title="事故经过描述" :value="item.sgjgms" />
          </van-cell-group>
        </template>
      </y-view-container>
    </template>

    <y-empty v-else></y-empty>
  </div>
</template>

<script>
export default {
  props: {
    formList: {
      type: Array,
      default: () => ([])
    }
  }
}
</script>

<style lang="less" scoped>
.content-box {
  /deep/.money-cell .van-cell__value > span {
     color: @maney_color;
  }
}
/deep/.view-container .view-box .van-cell .van-cell__title{
  flex: none !important;
  width: 40%;
}

</style>