<!--
 * @Description: 证据清单
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="evidence-list">
    <add-shared title="证据清单（建议提交证据作为有利证据）" add-title="证据清单" :showPackUp="list.length > 0" :showAddBtn="pageType !== 'details'" @handleAdd="handleAdd">
      <template slot="content">
        <info-box v-for="(item,index) in list" :key="index" :title="'证据清单' + (index + 1)">
          <template #cells>
              <van-cell title="提交人" :value="item.tjr000" :border="false" />
              <van-cell title="证据形式" :value="getDictName(evidenceList, item.zjmc00)" :border="false" />      
              <van-cell title="证据名称" :value="item.zjmc01" :border="false" />
          </template>
          <template #buttons>
            <template v-if="pageType !== 'details'">
              <van-button type="primary" @click="handleEdit(item.bczzj0)">修改</van-button>
              <van-button type="warning" @click="handleDelete(item.bczzj0)">删除</van-button>
            </template>
               
            <van-button v-else type="warning" class="info-button" @click="handleDetails(item.bczzj0)">查看详情</van-button> 
          </template>
        </info-box>
      </template>
    </add-shared>
  </div>
</template>

<script>
import InfoBox from "@/components/business/info-box"
import AddShared from "./add-shared"

import isEmpty from "lodash/isEmpty"
import {commonApi} from "@/api"
import {getDictName} from "@utils/common"

export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    propList: {
      type: Array,
      default: () => []
    }
  },
  components: {
    InfoBox,
    AddShared 
  },
  data() {
    return {
      list: [],

      evidenceList: [] //证据形式
    }
  },
  computed: {
    pageType(){
      return this.$route.query.pageType
    }
  },
  watch: {
    propList(val) {
      if (this.pageType === "details") { //详情页面
        this.list = val
      }
    }
  },
  created() {
    const {ZJMC00} = this.$sessionUtil.getItem("codeList") || {}
    if (isEmpty(ZJMC00)) {
      this.getPlatformList() //查询字典列表
    } else {
      this.evidenceList = ZJMC00
    } 
    
    console.log(this.pageType, "this.pageType")
    if (this.pageType !== "details") { //新增|编辑页面 
      this.findBc05ZjqdByPage() //查询申请列表   
    }
  },
  methods: {
    //查询字典列表
    getDictName,
    getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["ZJMC00"]
      }
      commonApi.proxyApi(params).then((res) => {
        const {data} = res.map
        const dictInfo = {
          "ZJMC00": "evidenceList"
        }
        for (const key in dictInfo) {
          this[dictInfo[key]] = data[key].map((item) => {
            return {label: item.aaa103, value: item.aaa102}
          })
        }
      })
    },
    // 查询申请列表
    findBc05ZjqdByPage() {
      commonApi.proxyApi({
        serviceName: "xytBc05Zjqd_findBc05ZjqdByPage"
      }).then((res) => {
        console.log(res, "res111")
        this.list = res.map?.data?.rows || []   
      }).catch((err) => {
        console.error(err)
      })
    },
    // 添加
    handleAdd() {
      const zcy000 = this.$attrs.organId
      this.$router.push({path: "/add-evidence-list", query: {pageType: "add", zcy000 }})
    },
    // 修改
    handleEdit(bczzj0) {
      this.$router.push({path: "/add-evidence-list", query: {pageType: "edit", bczzj0 }})
    },
    // 删除
    handleDelete(id) {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定删除",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        commonApi.proxyApi({
          serviceName: "xytBc05Zjqd_deleteBc05ZjqdByIds",
          ids: [id]
        }).then((res) => {
          console.log("已删除！", res)
          this.$toast("已删除！")
          this.findBc05ZjqdByPage()     
        }).catch((err) => {
          console.error(err)
        })
      })  
    },
    // 查看详情
    handleDetails(bczzj0) {
      this.$router.push({path: "/add-evidence-list", query: {pageType: "details", bczzj0 }})
    }
  }
}
</script>
<style lang="less" scoped>
</style>