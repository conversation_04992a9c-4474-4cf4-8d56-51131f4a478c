<!--
 * @Description: 提交材料
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="submit-material">

    <!-- 标题 -->
    <y-title content="相关材料附件" />

    <!-- 案情信息 -->
    <van-form ref="materialForm" class="base-form" :disabled="pageType === 'detail'" @failed="onFailed" @submit="handleSubmit">
      <van-cell-group inset class="border-bottom-wide">
        <van-field
          class="van-field-textarea"
          v-model="formData.abb286"
          name="abb286"
          label="案情描述"
          placeholder="请输入理由，字数300字以内"
          required
          type="textarea"
          :rules="formRules.abb286"
        ></van-field>

        <template>
          <van-field
            class="van-field-textarea"
            v-model="formData.aba003"
            name="aba003"
            :label="businessType === 'juBao' ? '您的举报内容' : '您的诉求'"
            placeholder="固定模版，不可自填，请在右边下拉选择相应"
            required
            disabled
            type="textarea"
            :rules="formRules.aba003"
            >
            <template slot="right-icon" v-if="pageType !== 'detail'">
              <div class="textarea-select" @click="handleSelectAppeal">
                <span>选择</span>
                <img :src="require('@/assets/imgs/labor-protect/<EMAIL>')" alt="">
              </div>
            </template> 
          </van-field>
        </template>

        <van-field
          v-if="businessType === 'xztj'"
          class="van-field-textarea"
          v-model="formData.aba002"
          name="aba002"
          label="纠纷案由"
          placeholder="固定模版，不可自填，请在右边下拉选择相应"
          required
          disabled
          type="textarea"
          :rules="formRules.aba002"
          >
          <template slot="right-icon" v-if="pageType !== 'detail'">
            <div class="textarea-select" @click="handleSelectReason">
              <span>选择</span>
              <img :src="require('@/assets/imgs/labor-protect/<EMAIL>')" alt="">
            </div>
          </template>
        </van-field>        
      </van-cell-group>    

      <!-- 材料 -->
      <div class="iframe-box" v-show="active === 1">
        <iframe ref="iframe" class="my-iframe" :src="iframeUrl" frameborder="0"></iframe>
      </div>   
      
      <!-- 操作按钮 -->
      <div class="button-box-more">
        <van-button plain type="info" @click="handleBack" native-type="button">
          上一步
        </van-button>

        <!-- @click="handleSubmit" -->
        <van-button v-if="pageType !== 'detail'" round block type="primary" native-type="submit">
          提 交
        </van-button>
        <van-button v-else plain type="info" @click="handleClose" native-type="button">
          关 闭
        </van-button>
      </div>
    </van-form>

    <!-- 诉求弹窗 -->
    <appeal-popup ref="appealPopup" :isShowAppealPopup.sync="isShowAppealPopup" :businessType="businessType" @submitAppeal="submitAppeal"></appeal-popup>

    <!-- 纠纷案由 举报内容 弹窗 -->
    <reason-popup :isShowReasonPopup.sync="isShowReasonPopup" @handleConfirmReason="handleConfirmReason"></reason-popup>
    
  </div>
</template>

<script>
import {commonApi} from "@/api"
import AppealPopup from "./components/appeal-popup"
import ReasonPopup from "./components/reason-popup"

export default {
  name: "submit-material",
  components: {
    AppealPopup,
    ReasonPopup
  },
  props: {
    active: {
      type: Number,
      default: 0
    },
    businessType: {
      type: String,
      default: "xztj" //业务类型 默认行政调解
    },
    materialId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      tipsList: ["文案需要确认下！！！", "1、个人不能重复对在同一家入职企业进行从业人员备案。", " 2、新增备案不能对姓名，身份证号码，性别，日期进行修改。"],

      // 表单信息
      formData: {
        dzd999: "",
        abb286: "",
        aba003: "",
        aba002: ""
      },
      formRules: {
        abb286: [{ required: true, message: "请输入" }],
        aba003: [{ required: true, message: "请选择" }],
        aba002: [{ required: true, message: "请选择" }]
      },

      materialNum: "", //材料编号
      iframeUrl: "", //上传材料页面地址

      // 选择弹窗信息
      isShowAppealPopup: false, //诉求弹窗
      isShowReasonPopup: false //纠纷案由 举报内容 弹窗     
    }
  },
  watch: {
    active(val) {
      if (this.pageType !== "detail" && val === 1 && !this.materialNum) { 
        //新增和编辑外网材料需要每次显示都重新获取  查看详情在调用查看接口的时候获取
        this.getArchivesPhoneData()
      }
    },
    businessType: { //业务类型切换 诉求 案由重置
      handler() {
        this.formData.aba001 = ""
        this.formData.aba002 = ""
        this.formData.aba003 = ""
      },
      immediate: true
    }
  },
  computed: {
    pageType() {
      return this.$route.query.pageType || ""
    }  
  },
  methods: {
    //获取外网材料电子档案
    getArchivesPhoneData(dzd999="") {    
      const nwFlag = process.env.VUE_APP_NW_FLAG === "true"
      const params = {
        serviceName: "xytCommon_getArchivesPhoneData",
        aaa121: "JC0001", //业务编号
        dza001: "C4",
        modifyFlag: this.pageType !== "detail", //修改标识
        dzd999, //业务受理ID
        nwFlag //内网标识
      }
      commonApi.proxyApi(params).then((res) => {
        const { url, dzd999 } = res.map.data
        this.materialNum = dzd999
        console.log(dzd999, "dzd999")
        console.log(this.materialNum, "this.materialNum")
        console.log(url, "url")
        this.iframeUrl = url        
      })
    },
    // 表单校验失败
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },
    // 保存 存储数据 返回
    handleSave() {
      this.$emit("handleSave", "material")
    },
    // 上一步
    handleBack() {      
      this.$emit("handleNext", 0)
    },
    // 提交
    handleSubmit() {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定提交",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        this.formData.dzd999 = this.materialNum
        this.$emit("handleSubmit", this.formData)
      }).catch(() => {})      
    },

    // 打开诉求弹窗
    handleSelectAppeal() {
      this.isShowAppealPopup = true
    },  
    // 选择诉求
    submitAppeal(appealInfo) {
      const {resultIds, resultText} = appealInfo
      if (this.businessType !== "xztj") {
        this.formData.aba001 = resultIds 
        this.formData.aba002 = resultText 
      }      
      this.formData.aba003 = resultText
      this.isShowAppealPopup = false
    },

    // 打开 纠纷案由 举报内容 弹窗
    handleSelectReason() {
      this.isShowReasonPopup = true
    },
    // 选择纠纷案由
    handleConfirmReason(data) {
      console.log(data, "选择纠纷案由")
      const {resultIds, resultText} = data
      this.formData.aba001 = resultIds 
      this.formData.aba002 = resultText 

      console.log(this.formData, "选择纠纷案由 formData")

      this.isShowReasonPopup = false
    },

    // 关闭
    handleClose() {
      window.history.go(-2)
    }
  }
}
</script>

<style lang="less" scoped>
.submit-material {
  .y-title {
    border-bottom: none !important;
  }
  .submit-material-tips {
    margin-bottom: 24px;
  }
  .van-field-textarea {
    position: relative;
    padding: 8px;
    .textarea-select {
      position: absolute;
      top: -40px;
      right: 8px;
      color: @main_color;

      display: flex;
      justify-content: flex-end;
      align-items: center;
      & > img {
        width: 16px;
        height: 8px;
        margin-left: 4px;
      }
    }
    /deep/.van-cell__value .van-field__body > textarea {
      padding: 8px;
    }
  }
  .van-cell--required::before {
    left: 0;
  }
  .iframe-box {
    height: calc(100vh - 384px);
    width: 100vw;
    overflow: hidden;
    .my-iframe {
      width: 200%;
      height: 200%;
      transform: scale(0.47);
      transform-origin: 0 0;
      margin-left: 12px;
    }
  }
  .business-popup {
    width: 92%;
    border-radius: 8px;
    max-height: calc(100vh - 80px);
    .business-popup-button {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 12px 0;
      .confirm-button {
        margin-left: 16px;
      }
    }
    
    .checkbox-case {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .checkbox-line {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-left: 8px;
        color: @main_text_color;
        .van-cell {
          width: 20px;
          border-bottom: 1.5px solid @main_color;
          padding: 0;  
          margin: 0 4px;
          line-height: 18px;
          &.money-field {
            width: 40px;
          }
        }
      }
      &-other{
        height: 20px;
      }
    }
    /deep/ .van-checkbox, .van-radio {
      height: auto;
      margin: 14px 0;
    }

    .wages-form {
      /deep/.van-field__error-message {
        display: none;
      }
    }

    .check-message {
      font-size: 14px;
      color: @main_color;
      text-align: center;
    }
  }
}

</style>