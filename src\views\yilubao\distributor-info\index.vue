<!--
 * @Description: 分销员信息
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    
  </div>
</template>

<script>
export default {
  name: "XmPlatformRiderEquityMobileIndex",

  data() {
    return {
      
    }
  },

  mounted() {
    const message = "二维码分销功能正在开发中，敬请期待！"
    this.$dialog.alert({
      title: "温馨提示",
      message,  
      theme: "round-button",
      className: "ylb-dialog-alert",
      showConfirmButton: false,
      width: "78%"
    })
  },

  methods: {
    
  }
}
</script>

<style lang="scss" scoped>

</style>