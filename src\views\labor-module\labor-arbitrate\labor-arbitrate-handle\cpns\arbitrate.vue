<!--
 * @Description: 仲裁诉求
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="arbitrate">
    <add-shared title="仲裁诉求" add-title="仲裁诉求" :showPackUp="list.length > 0" :showAddBtn="pageType !== 'details'" @handleAdd="handleAdd">
      <template slot="content">
        <info-box v-for="(item,index) in list" :key="index" :title="'仲裁诉求' + (index + 1)">
          <template #cells>
              <van-cell title="项目类型" :value="getDictName(abb012List, item.abb012)" :border="false" />    
              <van-cell title="具体事项" :value="getDictName(abb013List, item.abb013)" :border="false" />
              <van-cell title="事项类型" :value="getDictName(abb014List, item.abb014)" :border="false" />  
          </template>
          <template #buttons>
            <template v-if="pageType !== 'details'">
              <van-button type="primary" @click="handleEdit(item.bczms0)">修改</van-button>
              <van-button type="warning" @click="handleDelete(item.bczms0)">删除</van-button>
            </template>

            <van-button v-else type="warning" class="info-button" @click="handleDetails(item.bczms0)">查看详情</van-button> 
          </template>
        </info-box>
      </template>
    </add-shared>
  </div>
</template>

<script>
import InfoBox from "@/components/business/info-box"
import AddShared from "./add-shared"

import isEmpty from "lodash/isEmpty"
import {commonApi} from "@/api"
import {getDictName} from "@utils/common"

export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    propList: {
      type: Array,
      default: () => []
    }
  },
  components: {
    InfoBox,
    AddShared 
  },
  data() {
    return {
      list: [],

      abb012List: [], //项目类型
      abb013List: [], //项目类型
      abb014List: [] //项目类型
    }
  },
  computed: {
    pageType(){
      return this.$route.query.pageType
    }
  },
  watch: {
    propList(val) {
      if (this.pageType === "details") { //详情页面
        this.list = val
      }
    }
  },
  created() {
    const {ABB012, ABB013, ABB014} = this.$sessionUtil.getItem("codeList") || {}
    if (isEmpty(ABB012) || isEmpty(ABB013) || isEmpty(ABB014)) {
      this.getPlatformList() //查询字典列表
    } else {
      this.abb012List = ABB012
      this.abb013List = ABB013
      this.abb014List = ABB014
    }

    console.log(this.pageType, "this.pageType")
    if (this.pageType !== "details") { //新增|编辑页面 
      this.findBc05QqmsByPage() //查询申请列表   
    }   
  },
  methods: {
    //查询字典列表
    getDictName,
    getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["ABB012", "ABB013", "ABB014"]
      }
      commonApi.proxyApi(params).then((res) => {
        const {data} = res.map
        const dictInfo = {
          "ABB012": "abb012List",
          "ABB013": "abb013List",
          "ABB014": "abb014List"
        }
        for (const key in dictInfo) {
          this[dictInfo[key]] = data[key].map((item) => {
            return {label: item.aaa103, value: item.aaa102}
          })
        }
      })
    },
    // 查询申请列表
    findBc05QqmsByPage() {
      commonApi.proxyApi({
        serviceName: "xytBc05Qqms_findBc05QqmsByPage"
      }).then((res) => {
        console.log(res, "res111")
        this.list = res.map?.data?.rows || []   
      }).catch((err) => {
        console.error(err)
      })
    },
    // 添加
    handleAdd() {
      const zcy000 = this.$attrs.organId
      this.$router.push({path: "/add-arbitrate", query: {pageType: "add", zcy000}})
    },
    // 修改
    handleEdit(bczms0) {
      this.$router.push({path: "/add-arbitrate", query: {pageType: "edit", bczms0 }})
    },
    // 删除
    handleDelete(id) {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定删除",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        commonApi.proxyApi({
          serviceName: "xytBc05Qqms_deleteBc05QqmsByIds",
          ids: [id]
        }).then((res) => {
          console.log("已删除！", res)
          this.$toast("已删除！")
          this.findBc05QqmsByPage()     
        }).catch((err) => {
          console.error(err)
        })
      })  
    },
    // 查看详情
    handleDetails(bczms0) {
      this.$router.push({path: "/add-arbitrate", query: {pageType: "details", bczms0 }})
    }
  }
}
</script>
<style lang="less" scoped>
</style>