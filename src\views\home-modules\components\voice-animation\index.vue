<template>
  <div class="voice-animation-container">
    <span v-for="n in 5" :key="'1-' + n" class="voice-dot"></span>
    <span 
      v-for="n in totalDots" 
      :key="'2-' + n" 
      :class="['voice-dot', { 'voice-dot-active': randomActiveDots.includes(n) }]" 
      :style="{ animationDelay: randomActiveDots.includes(n) ? `${n * 0.2}s` : '0s' }"
    ></span>
    <span v-for="n in 5" :key="'3-' + n" class="voice-dot"></span>
  </div>
</template>

<script>
export default {
  data() {
    return {
      randomActiveDots: [],
      totalDots: 10
    }
  },
  methods: {
    generateRandomDots() {
      const activeCount = Math.floor(Math.random() * 4) + 6 // 动画个数
      const dots = Array.from({length: this.totalDots}, (_, i) => i + 1)
      
      // 随机打乱数组并取前activeCount个
      for (let i = dots.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1))
        ;[dots[i], dots[j]] = [dots[j], dots[i]]
      }
      
      this.randomActiveDots = dots.slice(0, activeCount)
    }
  },
  mounted() {
    // 初始化随机点
    this.generateRandomDots()
    
    // 每2秒重新随机选择动画span
    this.animationTimer = setInterval(() => {
      this.generateRandomDots()
    }, 2000)
  },
  beforeDestroy() {
    if (this.animationTimer) {
      clearInterval(this.animationTimer)
    }
  }
}
</script>

<style lang="less" scoped>
.voice-animation-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;

  .voice-dot, .voice-dot-active {
    display: inline-block;
    width: 3px;
    height: 5px;
    background-color: white;
    margin: 0 1px;
    border-radius: 3px;
  }

  .voice-dot-active {
    animation: pulse 1s infinite;
  }

  @keyframes pulse {
    0% { height: 5px; }
    25% { height: 10px; }
    50% { height: 30px; }
    75% { height: 10px; }
    100% { height: 5px; }
  }
}
</style>