<?xml version="1.0" encoding="UTF-8"?>
<svg width="17px" height="16px" viewBox="0 0 17 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组</title>
    <g id="app-首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="补充图标" transform="translate(-387.000000, -1049.000000)" fill-rule="nonzero">
            <g id="编组-8备份" transform="translate(368.000000, 1039.000000)">
                <g id="编组" transform="translate(19.000000, 10.000000)">
                    <path d="M7.9997866,0 C3.5908133,0 0,3.59090909 0,8 C0,12.4090909 3.5908133,16 7.9997866,16 C12.4087599,16 15.9995732,12.4090909 15.9995732,8 C16.0450265,3.59090909 12.4542132,0 7.9997866,0 Z" id="路径" fill="#CECECE"></path>
                    <g transform="translate(2.000000, 0.000000)" fill="#FFFFFF" id="形状">
                        <path d="M6,8 C7.08333333,8 8.16666667,8.17031251 9.25,8.51062499 C10.2845604,8.83554362 11,9.89599181 11,11.104375 L11,11.75 C11,12.4403559 10.5025386,13 9.88888889,13 L2.11111111,13 C1.49746139,13 1,12.4403559 1,11.75 L1,11.1040625 C1,9.89567931 1.71543955,8.83523113 2.75,8.5103125 C3.83333333,8.170625 4.91666667,8 6,8 Z M5.99860139,9 C5.67351916,9 5.50566847,9.11514623 5.49504935,9.34578447 L5.00622705,11.9073558 C4.9849318,12.0185957 5.01897314,12.1333097 5.097346,12.2144124 L5.75470408,12.8956078 C5.8191764,12.9623571 5.90761765,13 5.99997161,13 C6.09232556,13 6.18076681,12.9623571 6.24523913,12.8956078 L6.90293976,12.2144124 C6.98118922,12.1332435 7.01510004,12.0185381 6.99371615,11.9073558 L6.50386621,9.34578447 C6.49221943,9.11514624 6.32368364,9 5.99860139,9 L5.99860139,9 Z M6,2 C7.6569,2 9,3.3431 9,5 C9,6.6569 7.6569,8 6,8 C4.3431,8 3,6.6569 3,5 C3,3.3431 4.3431,2 6,2 Z"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>