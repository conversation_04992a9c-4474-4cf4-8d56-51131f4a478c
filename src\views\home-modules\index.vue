<!--
 * @Description:
 * @Author: wujh
 * @date: 2024/5/6 16:22
 * @LastEditors: 吕志伟 <EMAIL>
-->
<template>
  <div style="height: 100px">
    <home v-show="active === 'home'" />
    <person-center v-show="active === 'person'" />
    <!-- 底部tab -->
    <van-tabbar v-model="active" @change="handleChange">
      <van-tabbar-item v-for="(item , key) in iconList" :key="key" :name="item.text">
        <span>{{ item.name }}</span>
        <template #icon="props">
          <img :src="props.active ? item.active : item.inactive" />
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import home from "@pic/home/<USER>/home.png"
import homeActive from "@pic/home/<USER>/home-active.png"
import person from "@pic/home/<USER>/person.png"
import personActive from "@pic/home/<USER>/person-active.png"
import Home from "@/views/home-modules/components/home"
import PersonCenter from "@/views/home-modules/components/person-center"
import iconParkComments from "@pic/home/<USER>/iconPark-comments.svg"
import iconParkAndroid from "@pic/home/<USER>/iconPark-android.svg"
import { ZHRS_LOGIN_URL } from "@/assets/data/url-config"

export default {
  name: "new-home",
  components: { PersonCenter, Home },
  data(){
    return {
      active: "home",
      iconList: [
        {
          name: "服务大厅",
          text: "home",
          inactive: home,
          active: homeActive
        },
        {
          name: "边聊边办",
          text: "dialogue",
          inactive: iconParkComments,
          active: iconParkComments
        },
        {
          name: "数字人",
          text: "digital-human",
          inactive: iconParkAndroid,
          active: iconParkAndroid
        },
        {
          name: "个人中心",
          text: "person",
          inactive: person,
          active: personActive
        }
      ]
    }
  },
  activated() {
    if (this.active === "dialogue" || this.active === "digital-human") {
      this.active = "home"
    }
  },
  methods: {
    handleChange(activeVal) {
      console.log("handleChange", activeVal)
      this.scrollToTop(0, 0, "auto") //滚动到顶部
      if (activeVal === "dialogue") {
        window.location.href = ZHRS_LOGIN_URL + "dialogue"
      } else if (activeVal === "digital-human") {
        this.$router.push("/digital-human")
      }

      // const components = ["InsureAgainst", "InsureView", "ClaimsCheck"]
      // this.componentName = components[activeVal]
    },
    handleClickTab() {
      console.log("handleClickTab")
      if (this.active === "person") {
        this.$toast("功能建设中,敬请期待！")
        return
      }
    }
  }
}
</script>

<style lang="less" scoped>
.page-home {
  min-height: 100vh;
  background-image: url("~@pic/home/<USER>/<EMAIL>");
  background-repeat: no-repeat;
  background-size: contain;
  padding: 20px 16px 80px;
  .title{
    & > img {
      width: 100%;
    }
  }
  .menu{
    display: flex;
    flex-flow: row;
    flex-wrap: wrap;
    width: 100%;
    min-height: 180px;
    background-color: #fff;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    .menu-item{
      display: flex;
      flex-flow: column;
      min-width: 25%;
      justify-content: center;
      align-items: center;
      padding-top: 6px;
      & > img{
        width: 44px;
        height: 44px;
        object-fit: contain;
      }
      & > p{
        height: 20px;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        font-style: normal;
        padding-top: 8px;
      }
    }
  }

  .banner-box {
    width: 100%;
    padding: 16px 0 0;
    .van-swipe-item{
      text-align: center;
    }
    .banner-img {
      max-width: 344px;
      max-height: 110px;
      object-fit: contain;
    }
  }
  .seek-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    .seek-title {
      font-size: 18px;
      font-weight: bold;
      color: @main_text_color;
      line-height: 25px;
    }
    .more {
      font-size: 14px;
      font-weight: bold;
      color: @five_text_color;
      line-height: 20px;
    }
  }
  .tabs-box {
    ::v-deep .van-tab {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }
    ::v-deep .van-tabs__content {
      padding: 0 0 0 14px;
    }
    .tabs-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      &:not(:last-child) {
        border-bottom: 1px solid #EEEEEE;
      }
      .item-left {
        .item-top {
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
        }
        .item-bottom {
          margin-top: 20px;
          .item-tip {
            font-size: 11px;
            font-weight: 400;
            color: @main_color;
            line-height: 16px;
          }
          .item-date {
            font-size: 12px;
            font-weight: 400;
            color: @five_text_color;
            line-height: 18px;
            //margin-left: 20px;
          }
        }
      }
      .item-right {
        width: 108px;
        margin-left: 18px;
        border-radius: 4px;
        & > img {
          width: 100%;
          max-width: 180px;
          object-fit: contain;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
