<!--
 * @Description: 保险期限组件
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="time-picker" id="timePicker">
    <!-- 选择时间方式 -->
    <div class="time-option">
      <van-tabs v-model="selectionType" @change="clearDate">
        <van-tab v-for="(item, key) in selectionList" :title="item.title" :name="item.name" :key="key"></van-tab>
      </van-tabs>
    </div>
    
    <!-- 保险天数快捷操作 -->
    <div class="title">套餐选择</div>
    <div class="shortcuts" :class="{'shortcuts-disabled': selectionType === 'time'}">
      <div class="short-label" :class="sortcutClass(item)" v-for="(item, index) in shortcuts" :key="index" @click="handleClick(item)">
        <template v-if="item.value !== CUSTOM_DAYS">
          {{ item.label }}天
        </template>
        <template v-if="item.value === CUSTOM_DAYS && selectionType === 'time'">
          其它
        </template>
        <van-field
          v-if="item.value === CUSTOM_DAYS && selectionType === 'menu'"
          v-model="customDay"
          name="customDay"
          input-align="left"
          placeholder="请输入"
          :border="false"
        />
      </div>
    </div>
    
    <!-- 时间选择区域 -->
    <van-field
      class="flex-column"
      v-model="time"
      name="time"
      label="保险期限"
      placeholder="请选择"
      v-bind="$attrs"
      v-on="$listeners"
      :rules="rules.time"
      :label-width="90"
      :border="false"
    >  
      <template #input>
        <div class="custom-content">
          <div class="time-area" @click="showPopup('start')">
            <div class="label">起始时间</div>
            <div class="content">
              <span >{{formatStartDate}}</span>
              <span class="place">0时起</span>
            </div>
          </div>
          <div class="time-area" :class="{'no-border': formatEndDate, 'endtime-disabled': selectionType === 'menu'}" @click="showPopup('end')">
            <div class="label">截止时间</div>
            <div class="content">
              <span>{{formatEndDate}}</span>
              <span class="place">24时止</span>
            </div>
          </div>
        </div>
      </template>
    </van-field>
    <!-- 时间区间统计 -->
    <div class="day-total" v-if="formatEndDate&&intervalDay">
      共{{ intervalDay }}天
    </div>
    <van-popup 
        v-model="startDatePopupVisible"
        position="bottom"
      >
      <van-datetime-picker v-model="startDate"
        type="date"
        v-bind="$attrs"
        @confirm="checkStartDate"
        @cancel="closeStart" />
      </van-popup>
    <van-popup 
      v-model="endDatePopupVisible"
      position="bottom"
    >
      <van-datetime-picker v-model="endDate"
        type="date"
        v-bind="$attrs"
        :min-date="startDate"
        @confirm="checkEndtDate"
        @cancel="closeEnd" 
      />
    </van-popup>
  </div>
</template>

<script>
import {dayFormat} from "@/utils/dayjs"
import dayjs from "dayjs"
const totalDay = (val) => {
  const reg = /^\d{1,3}$/g
  if (val && reg.test(val) && val <= 365) {
    return true
  }

  return false
}
const CUSTOM_DAYS = "004"
const shortcuts = Object.freeze(
  [
    {
      label: "365",
      value: "001"
    }, 
    {
      label: "90",
      value: "002"
    },
    {
      label: "30",
      value: "003"
    },
    {
      label: "其它",
      value: "004"
    }
  ]
)
export default {
  model: {
    prop: "formData"
  },
  props: {
    formData: {
      type: Object,
      require: true
    },
    rules: {
      type: Object,
      default: () => ({})
    }
  },
  name: "time-picker",
  data() {
    return {  
      CUSTOM_DAYS,
      active: "", // 选中的快捷操作
      time: "", // 仅供校验使用
      formatStartDate: "",
      formatEndDate: "",
      startDate: new Date(),
      endDate: new Date(),
      startDatePopupVisible: false,
      endDatePopupVisible: false,
      // 投保套餐 001-365天；002-90天；003-30天；004-其他天数
      customDay: "", // 自定义天数
      shortcuts,
      intervalDay: 0, // 间隔天数
      days: 0, // 自定义天数
      pageHeight: "",
      selectionType: "", // 套餐类型
      selectionList: [
        {
          title: "套餐选择",
          name: "menu"
        },
        {
          title: "时间选择",
          name: "time"
        }
      ]
    }
  },
  computed: {
    sortcutClass() {
      return (item) => {
        return this.selectionType === "menu" && item.value === this.active ? "short-label-active" : ""
      }
    } 
  },
  watch: {
    "formData": {
      handler(newVal) {
        this.init()
      },
      immediate: true
    },
    formatStartDate: {
      handler(newVal) {
        if (newVal && this.active) {
          const item = this.shortcuts.find(item => item.value === this.active)
          const addDay = (item.value === "004" ? this.customDay : +item.label) || 1
          
          this.formatEndDate = dayjs(this.formatStartDate).add(addDay - 1, "day").format("YYYY-MM-DD")
          this.dayTotal()
        }
      }
    },
    customDay(val) {
      if (!val) {
        return
      }

      this.active = "004"
      if (!totalDay(val)) {
        this.$toast("请输入合法天数(1-365)")
        this.formatEndDate = ""
        this.customDay = ""
        return
      }

      this.formatStartDate ? "" : this.formatStartDate = dayjs().add(1, "day").format("YYYY-MM-DD") //开始时间默认明天
      this.formatEndDate = dayjs(this.formatStartDate).add(+val-1, "day").format("YYYY-MM-DD")
      if (dayjs(this.formatEndDate).isAfter(dayjs(this.$attrs.maxDate))) {
        this.$toast("投保截止时间为：20250913（含当日）")
        this.formatEndDate = ""
        this.active = ""
        this.customDay = ""
      }
      this.dayTotal()
    }
  },
  
  methods: {
    init() {
      const { bxqsrq, bxjzrq, bxzq00, selectionType, tbtc00 } = this.formData
      if (!bxqsrq || !bxjzrq) {
        return
      }

      this.$nextTick(() => {
        if (this.formatStartDate) {
          return
        }
        this.formatStartDate = bxqsrq
        this.formatEndDate = bxjzrq
        this.intervalDay = bxzq00
        this.active = tbtc00
        this.time = bxzq00 
        this.startDate = new Date(bxqsrq)
        this.endDate = new Date(bxjzrq)
        // 套餐类型
        this.selectionType = selectionType
      })
    },
    /**
     * @description: 快捷click事件
     * @param {*}
     * @return {*}
     * @author: T
     */    
    handleClick(item) {
      // 用户自己选择时间
      if (this.selectionType === "time") {
        this.active = ""

        return
      }

      const { label, value } = item
      if (value === "004") { //其它
        return
      }

      this.customDay = ""
      this.active = value
      if (!this.formatStartDate) {
        this.formatStartDate = dayjs().add(1, "day").format("YYYY-MM-DD")
        this.formatEndDate = dayjs(this.formatStartDate).add(+label - 1, "day").format("YYYY-MM-DD")
      } else {
        this.formatEndDate = dayjs(this.formatStartDate).add(+label - 1, "day").format("YYYY-MM-DD")
      }

      if (dayjs(this.formatEndDate).isAfter(dayjs(this.$attrs.maxDate))) {
        this.$toast("投保截止时间为：20250913（含当日）")
        this.formatEndDate = ""
        this.active = ""
        
      }
      this.dayTotal()
    },
    // 清空日期
    clearDate() {
      this.formatStartDate = ""
      this.formatEndDate = ""
      this.intervalDay = ""
      this.active = ""
    },
    // 选择起始日期
    showPopup(type) {
      if (type === "start") {
        this.startDatePopupVisible = true
        return
      } 

      if (type === "end" && this.formatStartDate == "") {
        this.$toast("请先选择开始日期")
        return
      }
  
      this.endDatePopupVisible = true
    },
    // 确认起始日期
    checkStartDate() {
      // this.active = ""
      // 不能投保当天
      if (dayjs(this.startDate).isAfter(dayjs())) {
        this.formatStartDate = dayFormat(this.startDate, "YYYY-MM-DD")
      } else {
        this.$toast("起保日期不能早于当前时间延后一天")
      }
    
      // if (dayjs(this.formatStartDate).isAfter(dayjs(this.formatEndDate))) {
      //   this.endDate = ""
      //   this.formatEndDate = ""
      //   this.active = ""
      // }

      this.dayTotal()
      this.startDatePopupVisible = false
    },    
    checkEndtDate() {
      // TODO 
      if (this.customDay && this.customDay !== this.intervalDay) {
        this.customDay = ""
        this.active = ""
      }
      if (dayjs(this.endDate).isBefore(dayjs(this.formatStartDate))) {
        this.$toast("终保日期不能早于起保日期")
      } else {
        this.formatEndDate = dayFormat(this.endDate, "YYYY-MM-DD")
        // this.$emit("update:formData", {...this.formData, bxqsrq: this.formatStartDate, bxjzrq: this.formatEndDate})
      }
      this.dayTotal()
      this.endDatePopupVisible = false
      this.active = ""
    },
    closeStart() {
      this.startDatePopupVisible = false
    },
    closeEnd() {
      this.endDatePopupVisible = false
    },
    /**
     * @description: 计算投保天数
     * @param {*}
     * @return {*}
     * @author: T
     */    
    dayTotal() {
      const { formatStartDate, formatEndDate } = this
      if (!formatStartDate || !formatEndDate) {
        this.intervalDay = 0
        // this.active = "004"
      } else {
        const startDate = dayjs(formatStartDate)
        const endDate = dayjs(formatEndDate)
        // 计算两个日期之间的天数差
        this.intervalDay = +endDate.diff(startDate, "day") + 1
      }
      this.time = this.intervalDay
      const shortcutsLables = []
      const shortcutsValues = []
      this.shortcuts.forEach(item => {
        const { label, value } = item
        shortcutsLables.push(label)
        shortcutsValues.push(value)
      })
      const index = shortcutsLables.indexOf(`${this.intervalDay}`)
      // let active = this.active
      this.$nextTick(() => {
        if (index > -1) {
          // 默认选中
          // this.active = shortcutsValues[index]
        } else {
          this.active = "004"
        }
      
        this.$emit("update:formData", { ...this.formData, bxqsrq: formatStartDate, bxjzrq: formatEndDate, tbtc00: this.active, bxzq00: this.intervalDay, selectionType: this.selectionType })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.endtime-disabled {
  opacity: 0.5;
}
.clearpadding {
  // padding: 0!important;
}
.time-picker {
  padding: 0 16px 16px;
  background-color: #fff;
  .time-option {
    padding: 0 20%;
  }
}
.custom-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  background: #fff;
  font-size: 14px;
  .time-area {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex: 1;
    text-align: center;
    color: #333333;
    text-align: left;
    word-wrap: break-word;
    padding: 9px 0;
    position: relative;
    top: 0;
    left: 0;
    &::before {
      content: '*';
      position: absolute;
      left: -8px;
      top: 8px;
      color: @danger_color;
    }
    &::after {
      content: ' ';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background: #cecece;
      transform: scaleY(0.5);
    }
    .label {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
    }
    .content {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #666666;
      position: relative;
      top: 0;
      left: 0;
      padding-right: 30px;
      &::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 10px;
        width: 11px;
        height: 11px;
        background: url("~@pic/yilubao/icons/<EMAIL>") no-repeat;
        background-size: cover;
      }
      .place {
        color: #C0C4CC;
        text-align: right;
        width: 56px;
        display: inline-block;
      }

      // line-height: 20px;
    }
  }
  .time-area.no-border {
    &::after {
      height: 0;
    }
  }
}
.day-total {
  font-size: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #2CD8B8;
  line-height: 20px;
  background-color: #DCF8F3;
  position: relative;
  top: 0;
  left: -16px;
  padding: 7px 0;
  width: 100vw;
  text-align: center;
  margin-bottom: 15px;
}
.title {
  font-size: 14px;
  padding-left: 16px;
}
.shortcuts {
  display: flex;
  flex-direction: row;
  font-size: 16px;
  color: @main_text_color;
  margin-top: 15px;
  padding-left: 16px;
  &-disabled {
    .short-label {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #C0C4CC;
      background-color: #EEEEEE;
    }
  }
  .short-label {
    flex: 1;
    justify-content: center;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 3px 8px;
    border-radius: 20px;
    margin-right: 14px;
    border: 1px solid @border_color; 
    cursor: pointer;
    /deep/.van-field {
      background-color: transparent;
      padding: 0;
      width: 40px;
      height: 20px;
      line-height: 20px;
      .van-field__body {
        // width: 60px; 
        .input {
          color: #fff;
        }
      }
      .van-field__control {
        color: #eee;
      }
    }
    &-active {
      background-color: @ylb_color;
      border-color: @ylb_color;
      color: @white_text_color;
      transition: 0.5s;
      /deep/.van-field__control {
        &::placeholder  {
          color: #fff;
        }
        color: #fff;
      }
    }
    input {
      width: 60px;
    }
  }
  
}
</style>