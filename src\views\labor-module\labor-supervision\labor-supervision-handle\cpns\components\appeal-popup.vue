<!--
 * @Description: 诉求弹窗
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <van-popup class="business-popup" v-model="showAppeal" :close-on-click-overlay="false">
      <van-collapse v-model="appealActiveNames">
        <van-checkbox-group v-model="wagesResult">
          <van-collapse-item title="工资类" name="1">
              <van-form ref="wagesOneForm" class="wages-form checkbox-case">
                <van-checkbox name="wagesOne"></van-checkbox> 
                <div class="checkbox-line">
                  <span>请求公司支付</span>                  
                  <van-field v-model="wagesMonthOne" :rules="mounthRules" label="" placeholder="" />
                  <span>个月(天工资)共</span>
                  <van-field class="money-field" v-model="wagesMoneyOne" label="" placeholder="" :rules="moneyRules" />
                  <span>元</span>
                </div>
              </van-form>    
              
              <van-form ref="wagesTwoForm" class="wages-form checkbox-case">
                <van-checkbox name="wagesTwo"></van-checkbox> 
                <div class="checkbox-line">
                  <span>请求公司支付</span>
                  <van-field v-model="wagesMonthTwo" :rules="mounthRules" label="" placeholder="" />
                  <span>个月(天)加班费共</span>
                  <van-field class="money-field" v-model="wagesMoneyTwo" :rules="moneyRules" label="" placeholder="" />
                  <span>元</span>
                </div>
              </van-form>
              
              <van-form ref="wagesThreeForm" class="wages-form checkbox-case">
                <van-checkbox name="wagesThree"></van-checkbox>
                <div class="checkbox-line">
                  <span>请求公司支付</span>
                  <van-field v-model="wagesMonthThree" :rules="mounthRules" label="" placeholder="" />
                  <span>个月经济补偿金</span>
                  <van-field class="money-field" v-model="wagesMoneyThree" :rules="moneyRules" label="" placeholder="" />
                  <span>元</span>
                </div>
              </van-form>
              
              <van-form ref="wagesFourForm" class="wages-form checkbox-case">
                <van-checkbox name="wagesFour"></van-checkbox>
                <div class="checkbox-line">
                  <span>请求公司退还押金</span>
                  <van-field class="money-field" v-model="wagesMoneyFour" :rules="moneyRules" label="" placeholder="" />
                  <span>元</span>
                </div>
              </van-form>
          </van-collapse-item>
        </van-checkbox-group> 

        <van-checkbox-group v-model="otherResult">
          <van-collapse-item title="办理类" name="2">
            <van-checkbox name='{"text": "补签劳动合同", "id": "gz_6"}'>补签劳动合同</van-checkbox>

            <van-form ref="peopleNumberForm" class="wages-form checkbox-case checkbox-case-other">
              <van-checkbox name="peopleNumberName"></van-checkbox>
              <div class="checkbox-line">
                <span>清退童工</span>
                <van-field class="money-field" v-model="peopleNumber" :rules="peopleNumberRules" label="" placeholder="" />
                <span>人</span>
              </div>
            </van-form>            
            <van-checkbox name='{"text": "妇女职工权益", "id": "gz_8"}'>妇女职工权益</van-checkbox>            
            <van-checkbox name='{"text": "办理退工手续", "id": "gz_9"}'>办理退工手续</van-checkbox>            
            <van-checkbox name='{"text": "按规定安排职工休年休假", "id": "gz_10"}'>按规定安排职工休年休假</van-checkbox>
            <van-checkbox name='{"text": "其他", "id": "gz_11"}'>其他</van-checkbox>            
            <van-checkbox name='{"text": "非劳动部门管辖", "id": "gz_12"}'>非劳动部门管辖</van-checkbox>            
            <van-checkbox name='{"text": "确认劳动关系", "id": "gz_13"}'>确认劳动关系</van-checkbox>            
          </van-collapse-item>
          <van-collapse-item title="证件类型" name="3">
            <van-checkbox name='{"text": "请求公司退还证件", "id": "gz_14"}'>请求公司退还证件</van-checkbox>            
          </van-collapse-item>
        </van-checkbox-group>

        <div class="check-message" v-if="isCheckFail">
            {{ checkFailMessage }}
        </div>

        <div class="business-popup-button">          
          <van-button round plain type="info" @click="showAppeal=false">关 闭</van-button>    
          <van-button class="confirm-button" round block type="primary" @click="handleConfirmAppeal">确 定</van-button>
        </div>
      </van-collapse>      
    </van-popup>
  </div>
</template>

<script>
import { checkInteger, checkIntegerPointReg } from "@utils/check"
export default {
  props: {
    isShowAppealPopup: {
      type: Boolean,
      default: false
    },
    businessType: {
      type: String,
      default: "xztj" //业务类型 默认行政调解
    }
  },
  data() {
    return {
      // 选择弹窗信息
      appealActiveNames: ["1"],
      
      wagesResult: [], //工资类选择
      otherResult: [], //其他类选择    

      wagesMonthOne: "", //月份
      wagesMoneyOne: "", //元
      
      wagesMonthTwo: "", //月份
      wagesMoneyTwo: "", //元

      wagesMonthThree: "", //月份
      wagesMoneyThree: "", //元

      wagesMoneyFour: "", //元
      
      peopleNumber: "", //童工人数

      // 表单校验
      isCheckFail: false, //校验失败
      checkFailMessage: "表单验证不通过", //校验失败文案
      peopleNumberRules: [
        { required: true, message: "请输入童工人数" },
        {
          validator: checkInteger,
          message: "请输入正确的人数",
          trigger: "onBlur"
        }
      ],
      mounthRules: [
        { required: true, message: "请输入月数" },
        {
          validator: checkIntegerPointReg,
          message: "请输入正确的月数",
          trigger: "onBlur"
        }
      ],
      moneyRules: [
        { required: true, message: "请输入金额" },
        {
          validator: checkIntegerPointReg,
          message: "请输入正确的金额",
          trigger: "onBlur"
        }
      ]
    }
  },
  computed: {
    showAppeal: {
      get() {
        return this.isShowAppealPopup
      },
      set(val) {
        this.$emit("update:isShowAppealPopup", val)
      }
    },
    // 工资类
    wagesOneName() {
      const {wagesMonthOne, wagesMoneyOne} = this
      const data = {
        text: `请求公司支付${wagesMonthOne}个月(天工资)共${wagesMoneyOne}元`,
        id: "gz_1"
      }
      return JSON.stringify(data)
    },
    wagesTwoName() {
      const {wagesMonthTwo, wagesMoneyTwo} = this
      const data = {
        text: `请求公司支付${wagesMonthTwo}个月(天)加班费共${wagesMoneyTwo}元`,
        id: "gz_2"
      }
      return JSON.stringify(data)
    },
    wagesThreeName() {
      const {wagesMonthThree, wagesMoneyThree} = this
      const data = {
        text: `请求公司支付${wagesMonthThree}个月经济补偿金${wagesMoneyThree}元`,
        id: "gz_3"
      }
      return JSON.stringify(data)
    },
    wagesFourName() {
      const data = {
        text: `请求公司退还押金${this.wagesMoneyFour}元`,
        id: "gz_4"
      }
      return JSON.stringify(data)
    },
    // 清退童工 人数
    peopleNumberName() {      
      const data = {
        text: `清退童工${this.peopleNumber}人`,
        id: "gz_7"
      }
      return JSON.stringify(data)
    }
  },
  watch: {
    businessType: { //业务类型切换 诉求 案由重置
      handler() {
        this.resetData()
      },
      immediate: true
    }
  },
  methods: {
    // 点击确定
    handleConfirmAppeal() {
      const wagesResultList = []
      const checkList = [] //需要校验的表单
      this.wagesResult.length > 0 && this.wagesResult.forEach(item => {
        const formName = `${item}Form`
        const checkName = `${item}Name`
        checkList.push(this.checkForm(formName))
        wagesResultList.push(this[checkName]) 
      })

      if (this.otherResult.includes("peopleNumberName")) { // 校验清退童工       
        checkList.push(this.checkForm("peopleNumberForm"))     
      }

      Promise.all(checkList).then(() => {
        console.log("all校验成功")
        this.isCheckFail = false
        this.checkFailMessage = ""

        const allList = [...wagesResultList, ...this.otherResult]
        this.submitAppeal(allList)
      }).catch((error) => {
        console.log(error, "all校验失败")
        
        const {message=""} = error?.[0] || {}
        if (message) {
          this.isCheckFail = true
          this.checkFailMessage = message
          return
        }

        this.$toast("请输入完整月份或金额")
      })
    },   
    // 表单校验 
    checkForm(formName) {
      console.log(formName, "formName")
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate().then((valid) => {
          resolve()
        }).catch((error) => {
          reject(error)
        })
      })      
    },
    // 确定提交
    submitAppeal(allList) {
      let resultIds = ""
      let resultText = ""      
      if (allList.length > 0) { //清退童工特殊处理
        const index = allList.indexOf("peopleNumberName")
        if (index > -1) {
          allList.splice(index, 1) // 删除'peopleNumberName'
          allList.splice(index, 0, this.peopleNumberName)
        }
      }    
      
      const resList = allList.map(item => JSON.parse(item))
      resList.forEach((item, index) => {
        resultText += `${index + 1}、${item.text}；`
        resultIds += `${item.id},`
      })

      const appealInfo = {
        resultIds: resultIds.slice(0, -1),
        resultText: resultText.slice(0, -1)
      }

      this.$emit("submitAppeal", appealInfo)
    },
    // 重置数据
    resetData() {      
      Object.assign(this.$data, this.$options.data())
    }
  }
}
</script>
<style lang="less" scoped>
.business-popup {
  width: 96%;
  border-radius: 8px;
  max-height: calc(100vh - 80px);
  .business-popup-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 0;
    .confirm-button {
      margin-left: 16px;
    }
  }
  
  .checkbox-case {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .checkbox-line {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-left: 8px;
      color: @main_text_color;
      .van-cell {
        width: 20px;
        border-bottom: 1.5px solid @main_color;
        padding: 0;  
        margin: 0 4px;
        line-height: 18px;
        &.money-field {
          width: 40px;
        }
      }
    }
    &-other{
      height: 20px;
    }
  }
  /deep/ .van-checkbox, .van-radio {
    height: auto;
    margin: 14px 0;
  }

  .wages-form {
    /deep/.van-field__error-message {
      display: none;
    }
  }

  .check-message {
    font-size: 14px;
    color: @main_color;
    text-align: center;
  }
}
</style>