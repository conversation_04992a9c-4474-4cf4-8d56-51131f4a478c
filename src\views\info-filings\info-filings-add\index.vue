<!--
 * @Description: 信息备案--新增
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="info-filings-add">
    
    <!-- 重要提示 -->
    <y-tips :tipsList="tipsList" v-if="active === 0"></y-tips>
    
    <!-- 步骤条 -->
    <y-society-steps class="top-header" :handleList="handleList" :active="active"></y-society-steps>

    <!-- 步骤 -->
    <person-info v-show="active === 0" @handleNext="handleNext" v-model="formData"></person-info>

    <info-record v-if="active === 1" @handleNext="handleNext" v-model="formData" @handleSubmit="handleSubmit"></info-record>

    <success-filed v-show="active === 2" @handleNext="handleNext" :active="active"></success-filed>
    
  </div>
</template>

<script>
import PersonInfo from "../cpns/person-info"
import InfoRecord from "../cpns/info-record"
import SuccessFiled from "../cpns/success-filed"

import {getInfoByIdCard} from "@/utils/common"

export default {
  name: "info-filings-add",
  components: {
    PersonInfo,
    InfoRecord,
    SuccessFiled
  },  
  data() {
    return {
      active: 0, 
      select: "",
      handleList: [
        "人员信息",
        "信息备案",
        "备案成功"
      ],

      tipsList: [
        "个人不能重复对正在入职同一家入职企业进行从业人员备案",
        "新增备案不能对姓名、身份证号码、性别、出生日期进行修改"
      ],

      formData: {
        // 人员信息
        aac003: "", //姓名
        aac002: "", //证件号码        
        aac004: "", //性别
        aac006: "", //出生日期
        aae005: "", //联系电话
        aac011: "", //文化程度
        ccc010: "", //毕业学校
        aac183: "", //毕业专业
        ccd027: "", //毕业时间
        aab299: "", //户籍所在地
        ccd032: "", //现居住地

        // 信息备案
        aab001: "", //企业编号
        ptbm00: "", //平台编码
        ptbm00Text: "", //平台编码文本
        aca111: "", //岗位
        ccd028: "", //签约方式
        ccd006: "", //签约起始时间
        sftbch: "", //是否同步参会
        yglx00: "001", //用工类型
        yglx00Text: "", //用工类型文本
        // 其余参数
        ccg981: "001", //证件类型        
        aab019: "1", //企业类型 1 平台企业 2 合作企业

        aaa028: "2", //数据来源（0 经办 1 单位 2 个人 3 主管部门）
        source: "002" //渠道来源（001 PC端 002 移动端）
  
      }
    }
  },
  watch: {
    active(val) {
      this.scrollToTop(0, 0, "auto")
    }
  },
  computed: {
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    }
  },
  created() {      
    this.initFormData()
  },
  mounted() {    
    if (window.history && window.history.pushState) {
      if (window.history.length>1){
        const state = {
          key: Math.random() * new Date().getTime()
        }
        window.history.pushState(state, null, document.URL)
      }
      
      //给window添加一个监听事件popstate，拦截返回键，并执行方法 backFn
      window.addEventListener("popstate", this.backFn, false)
    }
  },
  methods: {
    initFormData() {
      const {xm0000: aac003, zjhm00: aac002, mobile: aae005} = this.userInfo
      const {sex: aac004, birthdate: aac006, area: aab299} = getInfoByIdCard(aac002)
      this.formData = {...this.formData, aac003, aac002, aae005, aac004, aac006, aab299} 
    },
    // 拦截返回键
    backFn() {
      // 第一步或提交成功状态 返回上一页面 
      if (this.active != 1) {        
        this.$router.go(-1)
        return
      }

      // 未完成提交 改变active状态
      this.active--
      const state = {
        key: Math.random() * new Date().getTime()
      }
      window.history.pushState(state, null, document.URL)
    },

    // 下一步
    handleNext(active) {
      this.active = active
    },
    
    // 提交
    handleSubmit() {
      // 调用提交接口
      this.active = 2
    }
  },
  destroyed(){
    //销毁监听
    window.removeEventListener("popstate", this.backFn, false)
  }
}
</script>

<style lang='less' scoped>
</style>

