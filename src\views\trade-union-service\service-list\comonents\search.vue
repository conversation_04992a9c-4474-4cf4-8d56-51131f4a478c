<!--
 * @Description: 搜索
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="search-box flex-c-c">
    <van-field
      v-model="input"
      input-align="left"
      placeholder="搜索感兴趣的服务"
      :border="false"
      clearable
      left-icon="search"
    />
    <span class="search-btn" @click="handleSearch">搜索</span>
  </div>
</template>

<script>
export default {
  name: "trade-union-service-search",

  data() {
    return {
      input: ""
    }
  },

  mounted() {
    
  },

  methods: {
    handleSearch() {
      this.$emit("search", this.input)
    }
  }
}
</script>

<style lang="less" scoped>
.search-box {
  width: 100%;
  height: 28px;
  background: #ffffff;
  border-radius: 18px;
  z-index: 99999;
  padding: 0 14px;
  .search-icon {
    width: 20px;
    height: 20px;
  }
  .van-field {
    flex: 1;
    padding: 0 10px;
    border-radius: 14px;
    background: #F5F7FA;
    /deep/.van-icon-clear {
      font-size: 14px;
    }
    /deep/.van-field__control::-webkit-input-placeholder {
      font-size: 14px;
    }
    /deep/.van-field__left-icon {
      width: 20px;
      height: 20px;
      color: #999999;
    }
  }
  .search-btn {
    font-size: 14px;
    color: #bd1a2d;
    display: inline-block;
    height: 100%;
    line-height: 28px;
    margin-left: 8px;
    font-weight: bold;
  }
}
</style>