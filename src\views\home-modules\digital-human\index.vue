<!--
 * @Description: 数字人
 * @Author: hwx
 * @date: 2025/5/28 16:22
-->
<template>
  <div class="avatar">
    <div class="_titleBar_hmcad_61 _title_18qjg_237">厦门人社智慧小新</div>
    <div id="wrapper" class="digital-human-wrapper"></div>

    <div class="overlay-content">
      <!-- 按钮组 -->
      <div v-if="false" class="button-group">
        <!--        <van-button class="action-button" @click="start()" type="primary">开始对话</van-button>-->
        <van-button class="action-button" @click="interrupt()" type="primary">打断</van-button>
        <van-button class="action-button" @click="startRecord()" v-if="recorderbutton == false"
          type="primary">开启录音</van-button>
        <van-button class="action-button" @click="stop()" type="primary">关闭连接</van-button>
        <van-button class="action-button" @click="destroy()" type="primary">销毁SDK</van-button>
        <van-button class="action-button" @click="writeText()" type="primary">文本驱动</van-button>
      </div>
      <!-- 按钮组2 -->

      <div class="button-group2">
        <div class="clickable clickable_mute_btn" @click="changePlayStateFn">
          <template v-if="isPlay">
            <svg t="1717315840299" class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" p-id="1481"
              xmlns:xlink="http://www.w3.org/1999/xlink" width="64" height="64">
              <path
                d="M469.824 150.4C511.232 114.112 576 143.488 576 198.464v626.944c0 55.04-64.768 84.352-106.176 48.192L275.968 704 160 704a64 64 0 0 1-64-64V384a64 64 0 0 1 64-64h115.968zM512 198.464l-193.92 169.6A64 64 0 0 1 276.032 384H160v256h115.968a64 64 0 0 1 42.176 15.808L512 825.472V198.528z m202.688 11.712a320.064 320.064 0 0 1 13.44 598.4l-13.44 5.12-21.376-60.352a256.064 256.064 0 0 0 12.544-478.08l-12.544-4.8 21.376-60.288z m-26.688 163.2a159.936 159.936 0 0 1 9.6 271.168l-9.6 5.952-32-55.36a95.936 95.936 0 0 0 7.744-161.344L656 428.8l32-55.36z"
                fill="#ffffff" p-id="1482"></path>
            </svg>
          </template>
          <template v-else>
            <svg t="1717313287188" class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" p-id="2454"
              xmlns:xlink="http://www.w3.org/1999/xlink" width="64" height="64">
              <path
                d="M184.576 139.52a32 32 0 1 0-49.152 40.96l640 768a32 32 0 0 0 49.152-40.96L268.288 240l-0.192-0.256L184.576 139.52zM448 195.648v155.968L512 428.16V160a32 32 0 0 0-32-32c-65.28 0-121.6 31.616-163.84 66.112l41.024 49.088c27.072-22.016 57.92-40.064 90.816-47.552z m381.568 611.776c6.976 7.488 12.544 15.744 16.64 24.576C916.672 756.672 960 639.36 960 510.464c0-141.568-52.224-269.12-135.296-342.4a32 32 0 0 0-42.304 47.936C848.64 274.56 896 382.912 896 510.4c0 111.36-36.032 208-89.216 269.76l22.784 27.264zM448 650.304l64 76.48V864a32 32 0 0 1-32 32c-79.872 0-146.176-48-189.888-90.496a521.92 521.92 0 0 1-61.44-72.32H134.4c-36.48 0-70.4-28.032-70.4-67.584V355.776c0-39.616 33.92-67.584 70.4-67.584h10.432l53.568 64h-64a7.936 7.936 0 0 0-5.44 1.92C128 355.008 128 355.584 128 355.776v309.76c0 0.192 0 0.832 0.96 1.728 1.024 0.96 2.88 1.92 5.44 1.92h111.36a32 32 0 0 1 27.264 15.232l0.128 0.192 0.64 1.088 3.072 4.608c17.024 24.96 36.416 48.192 57.984 69.376 31.872 31.104 70.848 58.816 113.152 68.544v-177.92z m220.672-35.072l43.776 52.288c29.44-46.528 44.992-100.48 44.864-155.52a289.536 289.536 0 0 0-96-215.872 32 32 0 0 0-42.624 47.744A225.536 225.536 0 0 1 693.312 512c0 36.608-8.576 71.808-24.64 103.232z"
                p-id="2455" fill="#ffffff"></path>
            </svg>
          </template>
        </div>
        <div class="clickable clickable_break" @click="interrupt">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1.6 10.0001C1.6 5.36081 5.36081 1.6 9.99999 1.6C14.6392 1.6 18.4 5.36081 18.4 10.0001C18.4 14.6392 14.6392 18.4 9.99999 18.4C5.3608 18.4 1.6 14.6392 1.6 10.0001Z"
              stroke="#ffffff" stroke-width="1.2"></path>
            <rect x="6.5" y="6.5" width="7" height="7" rx="1.5" fill="#ffffff"></rect>
          </svg>
        </div>
        <div class="clickable clickable_recorder" @click="changeRecordFn()">
          <template v-if="recorderbutton">
            <svg width="68" height="89" viewBox="0 0 68 89" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M35.0233 62.2162H33.03C21.4626 62.2162 12.0537 52.797 12.0537 41.2296V20.9763C12.0537 9.40886 21.4626 0 33.03 0H35.0233C46.5908 0 55.9996 9.40886 55.9996 20.9763V41.2399C55.9996 52.8074 46.5908 62.2162 35.0233 62.2162ZM60.76 43.0558V42.4981C60.76 40.5048 62.3815 38.8833 64.3852 38.8833C66.3785 38.8833 68 40.5048 68 42.4981V43.0558C68 60.1281 54.5012 74.102 37.6148 74.8766V81.6105H49.4611C51.7643 81.6105 53.582 83.8 52.9417 86.2065C52.5079 87.797 50.969 88.8401 49.3269 88.8401H18.5286C16.2254 88.8401 14.4077 86.6506 15.048 84.2441C15.4818 82.6536 17.0207 81.6105 18.6628 81.6105H30.3852V74.8766C13.4988 74.102 0 60.1281 0 43.0558V42.4981C0 40.5048 1.62151 38.8833 3.61482 38.8833C5.60814 38.8833 7.22965 40.5048 7.22965 42.4981V43.0558C7.22965 56.6579 18.2497 67.6779 31.8518 67.6779H36.1379C49.74 67.6779 60.76 56.6579 60.76 43.0558ZM21.534 23.2337C21.534 21.6689 22.8026 20.4004 24.3674 20.4004H44.7673C46.3322 20.4004 47.6007 21.6689 47.6007 23.2337C47.6007 24.7985 46.3322 26.0671 44.7674 26.0671H24.3674C22.8026 26.0671 21.534 24.7985 21.534 23.2337ZM24.3674 30.6001C22.8026 30.6001 21.534 31.8686 21.534 33.4334C21.534 34.9983 22.8026 36.2668 24.3674 36.2668H44.7674C46.3322 36.2668 47.6007 34.9983 47.6007 33.4334C47.6007 31.8686 46.3322 30.6001 44.7673 30.6001H24.3674Z"
                fill="#fff"></path>
            </svg>
          </template>
          <template v-else>
            <svg width="76" height="89" viewBox="0 0 76 98" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M52.9068 10.0229C49.2136 4.01454 42.5788 0 35.0229 0H33.0295C21.4621 0 12.0532 9.40886 12.0532 20.9763V41.2296C12.0532 46.9899 14.3865 52.2175 18.1576 56.0145L33.0781 36.2668H24.367C22.8022 36.2668 21.5337 34.9983 21.5337 33.4334C21.5337 31.8686 22.8022 30.6001 24.367 30.6001H37.3596L40.7846 26.0671H24.367C22.8022 26.0671 21.5337 24.7985 21.5337 23.2337C21.5337 21.6689 22.8022 20.4004 24.367 20.4004H44.767C44.8643 20.4004 44.9604 20.4053 45.0551 20.4149L52.9068 10.0229ZM43.2912 26.0671H44.767C46.3318 26.0671 47.6004 24.7985 47.6004 23.2337C47.6004 22.5023 47.3232 21.8356 46.8682 21.3329L53.9569 11.9507C55.2658 14.6858 55.9992 17.7469 55.9992 20.9763V22.5185L47.5926 33.6448C47.5977 33.575 47.6004 33.5045 47.6004 33.4334C47.6004 31.8686 46.3318 30.6001 44.767 30.6001H39.8663L43.2912 26.0671ZM35.5848 36.2668L19.6393 57.3711C21.7076 59.0917 24.1106 60.4229 26.7347 61.2508L45.7415 36.0947C45.4377 36.206 45.1094 36.2668 44.767 36.2668H35.5848ZM23.0901 66.0746C20.5411 65.1043 18.1952 63.723 16.1333 62.0115L11.7812 67.7715C13.8545 69.4582 16.1433 70.8899 18.5992 72.0183L23.0901 66.0746ZM20.5014 72.8184L25.0941 66.7398C27.2411 67.3508 29.5079 67.6779 31.8513 67.6779H36.1374C49.7395 67.6779 60.7595 56.6579 60.7595 43.0558V42.4981C60.7595 40.5048 62.381 38.8833 64.3847 38.8833C66.378 38.8833 67.9995 40.5048 67.9995 42.4981V43.0558C67.9995 60.1281 54.5007 74.102 37.6143 74.8766V81.6105H49.4606C51.7638 81.6105 53.5815 83.8 52.9412 86.2065C52.5074 87.797 50.9685 88.8401 49.3264 88.8401H18.5281C16.2249 88.8401 14.4072 86.6506 15.0475 84.2441C15.4813 82.6536 17.0202 81.6105 18.6623 81.6105H30.3847V74.8766C26.9184 74.7176 23.5948 74.0024 20.5014 72.8184ZM10.2665 66.4586L14.6418 60.6677C10.0678 56.1976 7.22916 49.9593 7.22916 43.0558V42.4981C7.22916 40.5048 5.60765 38.8833 3.61434 38.8833C1.62102 38.8833 -0.000488281 40.5048 -0.000488281 42.4981V43.0558C-0.000488281 52.2995 3.95684 60.6349 10.2665 66.4586ZM28.8311 61.7938L55.9992 25.8361V41.2399C55.9992 52.8074 46.5903 62.2162 35.0229 62.2162H33.0295C31.5921 62.2162 30.188 62.0708 28.8311 61.7938Z"
                fill="white"></path>
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M74.4111 0.808511C76.1737 2.14025 76.5229 4.64871 75.1912 6.41131L7.19121 96.4113C5.85946 98.1739 3.351 98.5232 1.5884 97.1915C-0.174196 95.8597 -0.523475 93.3512 0.808267 91.5887L68.8083 1.58865C70.14 -0.173952 72.6485 -0.523231 74.4111 0.808511Z"
                fill="white"></path>
            </svg>
          </template>
        </div>
      </div>

      <!-- 中间文案部分 -->
      <div class="text-display-box ask-box">
        <p class="typed-text">{{ asrText }}</p>
      </div>
      <div ref="htmlContainer" class="text-display-box answer-box" @wheel="stopAutoScroll" @touchmove="stopAutoScroll"
        @touchend="mouseup">
        <p class="typed-text" v-html="nlpText"></p>
      </div>
    </div>
  </div>
</template>

<script>
import digitalHumanMixin from "./mixins"
export default {
  name: "avatarComponent",
  mixins: [digitalHumanMixin],
  data() {
    return {
      textarea: "如何线上办理劳动维权",
      isPlay: true,
      isRecord: false,
      mouseTimer: null,
      isAutoScrolling: true // 新增自动滚动状态标志
    }
  },

  beforeDestroy() {
    this.destroy()
  },
  watch: {
    nlpText() {
      cancelAnimationFrame(animateScroll)
      let animateScroll
      if (!this.isAutoScrolling) {
        console.log("cancelAnimationFrame")
        cancelAnimationFrame(animateScroll)
        return
      }

      this.$nextTick(() => {
        const container = this.$refs.htmlContainer
        const textLength = this.nlpText.length

        if (textLength >= 200 && this.isAutoScrolling) {
          const start = container.scrollTop
          const end = container.scrollHeight
          const duration = textLength / 22 * 6000
          const startTime = performance.now()

          animateScroll = (timestamp) => {
            if (!this.isAutoScrolling) { return }

            const elapsed = timestamp - startTime
            const progress = Math.min(elapsed / duration, 1)
            const easeProgress = easeOutQuad(progress)
            container.scrollTop = start + (end - start) * easeProgress

            if (progress < 1) {
              requestAnimationFrame(animateScroll)
            }
          }

          const easeOutQuad = (t) => {
            return t * (2 - t)
          }

          requestAnimationFrame(animateScroll)
        }
      })
    }
  },
  mounted() {
  },
  methods: {
    stopAutoScroll() {
      clearTimeout(this.mouseTimer)
      this.isAutoScrolling = false
    },
    mouseup() {
      clearTimeout(this.mouseTimer)
      this.isAutoScrolling = false
      this.mouseTimer = setTimeout(() => {
        this.isAutoScrolling = true
      }, 2000)
    },
    starInit() {
      this.$nextTick(() => {
        this.nlpText = "欢迎使用厦门人社智慧小新 ，我是你的专属 AI 智能助手，有什么问题可以随时问我～"
      })
    },
    nlpCallBackFn(nlpData) {
      console.log("nlpCallBackFn", nlpData)
      const text = nlpData.content
      // const regex = /https:\/\/[^\s]+/g
      const regex = /\[\[(.*?)]]/g
      // const replacedText = text.replace(regex, (match) => {
      //   return `<a class="answer-link" href="${match}">一键去办理</a>`
      // })
      const replacedText = text.replace(regex, (match) => {
        const matchReplace = match.replace("[[", "").replace("]]", "")
        return `<a class="answer-link" href="${matchReplace}">一键去办理</a>`
      })
      console.log("replacedText", replacedText)
      this.nlpText = replacedText
    },
    changePlayStateFn() {
      console.log(this.player)
      if (this.isPlay) {
        this.player.muted = true
      } else {
        this.player.resume()
      }
      this.isPlay = !this.isPlay
    },
    changeRecordFn() {
      if (!this.recorderbutton) {
        this.startRecord()
      } else {
        this.stopRecord()
      }
    }
  }
}
</script>
<style lang="less" scoped>
.avatar {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #333;

  /* 示例背景色，可根据图片调整 */
  ._title_18qjg_237 {
    position: absolute;
    width: 100%;
    height: 30px;
    background: transparent url("~@/assets/imgs/szr/bg-title-Umaue-lQ.png") no-repeat center / auto 100%;
    font-size: 12px;
    font-weight: 700;
    line-height: 27px;
    z-index: 2;
    letter-spacing: 2px;
    text-align: center;
    color: #b2d7ff;
    white-space: nowrap;
  }
}

.digital-human-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* 确保数字人内容能正确显示 */
  z-index: 1;
  background: transparent url("~@pic/home/<USER>/digital-human-bg.png") no-repeat center / 100% 100%;
}

.overlay-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  /* 将内容对齐到底部 */
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
  z-index: 2;
  /* 确保浮动在数字人上方 */
}

.text-display-box {
  width: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  /* 半透明背景 */
  color: #fff;
  padding: 15px;
  border-radius: 10px;
  font-size: 12px;
  line-height: 1.6;
  text-align: left;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);

  &.ask-box {
    margin-bottom: 20px;
    /* 调整位置，与底部保持距离 */
    height: 54px;
  }

  &.answer-box {
    margin-bottom: 70px;
    /* 调整位置，与底部保持距离 */
    height: 188px;
    overflow-y: auto;
    /* 允许垂直滚动 */
    position: relative;
    touch-action: auto;

    /* 允许正常触摸行为 */
    a {
      position: relative;
      z-index: 1;
      cursor: pointer;
      touch-action: manipulation;
      -webkit-tap-highlight-color: transparent;
      /* 消除点击高亮 */
    }
  }
}

/* 针对 iOS 的特殊处理 */
@supports (-webkit-overflow-scrolling: touch) {
  .answer-box a {
    /* iOS 上增强点击体验 */
    outline: none;
    -webkit-user-select: none;
    user-select: none;
  }
}

//.text-display-box {
//  width: 90%;
//  background-color: rgba(58, 82, 238, 0.8); /* 半透明背景 */
//  color: #fff;
//  padding: 15px;
//  border-radius: 10px;
//  font-size: 12px;
//  line-height: 1.6;
//  text-align: left;
//  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
//  &.ask-box {
//    border-radius: 10px 10px 0 0;
//    //margin-bottom: 20px; /* 调整位置，与底部保持距离 */
//    height: 54px;
//  }
//  &.answer-box {
//    background-color: rgba(58, 82, 238, 0.6); /* 半透明背景 */
//    border-radius: 0 0 10px 10px;
//    margin-bottom: 70px; /* 调整位置，与底部保持距离 */
//    height: 188px;
//    overflow-y: auto; /* 允许垂直滚动 */
//  }
//}

/deep/.typed-text {
  white-space: pre-wrap;
  /* 保留空白符和换行符 */
  word-break: break-word;

  .answer-link {
    color: #3ca0f6;
  }
}

.button-group {
  width: 100px;
  position: absolute;
  left: 10px;
  top: 40px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;

  /* 按钮之间的间距 */
  /* margin-bottom: 20px; */
  /* 移除或调整此行 */
  .van-button {
    background-color: white;
  }
}

.button-group2 {
  position: relative;

  .clickable_mute_btn {
    position: fixed;
    top: 20px;
    right: 10px;
    width: 30px;
    height: 30px;

    &>svg {
      width: 18px;
      height: 18px;
    }
  }

  .clickable_break {
    position: fixed;
    top: 60px;
    right: 10px;
    width: 30px;
    height: 30px;

    &>svg {
      width: 18px;
      height: 18px;
    }
  }

  .clickable_recorder {
    position: fixed;
    top: 50%;
    right: 10px;
    width: 30px;
    height: 30px;

    &>svg {
      width: 14px;
      height: 18px;
    }
  }

  .clickable {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    border-radius: 50%;
    background-color: rgba(144, 157, 179);
    //box-shadow: 0 0 32px 4px #ffffff99 inset;
    box-shadow: 0 0 32px 4px #00000060;
    border: 1px solid rgb(197, 218, 255);
  }
}

.action-button {
  min-width: 100px;
  /* 按钮最小宽度 */
  height: 40px;
  border-radius: 20px;
  /* 圆角按钮 */
  font-size: 15px;
  background-color: #ff4d4f;
  /* 红色按钮背景 */
  border: none;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 移除或修改原有的Element UI相关样式 */
.container,
.aside,
.main {
  display: none;
  /* 隐藏旧的布局元素 */
}

/* 确保其他通用样式不冲突 */
* {
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  border: none;
}

.error {
  border-block-color: red;
}

.widthclass {
  width: 400px;
}

span {
  color: red;
}
</style>
