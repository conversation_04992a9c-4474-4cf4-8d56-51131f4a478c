<!--
 * @Description: 普通多选模式
 * @Version: 0.1
 * @Author: T
-->
<template>
  <div class="item-list">
    <div v-for="(item, index) in list" :key="item.DICT_KEY + index" class="item-box" @click="handleClick(item)" :class="{'item-box-active': itemActive(item)}">{{ item.DICT_VALUE }}</div>
  </div>
</template>
<script>

export default {
  props: {
    type: {
      type: String,
      default: ""
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      activeList: []
    }
  },
  computed: {
    itemActive() {
      return (info) => {
        const { DICT_KEY } = info || {}
        return DICT_KEY && this.activeList.find(item => item.DICT_KEY === DICT_KEY)
      }
    }
  },
  methods: {
    onReset() {
      this.activeList = []
    },
    handleClick(itemInfo) {
      const { DICT_KEY } = itemInfo
      const selectedIndex = this.activeList.findIndex(item => item.DICT_KEY === DICT_KEY)
      const list = this.activeList
      
      if (selectedIndex !== -1) {
        list.splice(selectedIndex, 1)
        this.activeList = [...list]
      } else {
        this.activeList = [...this.activeList, itemInfo]
      }
      this.$emit("onActiveList", this.activeList, this.type)
    }
  }
}
</script>

<style lang="less" scoped>
.item-list {
  padding: 16px;
  // min-height: 100px;
  &::after {
    content: " ";
    display: table;
    clear: both;
  }
  .item-box {
    width: calc((100% - 2*8px)/3);
    border-radius: 2px;
    float: left;
    background: #F6F6F6;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    text-align: left;
    font-style: normal;
    text-align: center;
    margin-right: 8px;
    margin-bottom: 12px;
    padding-top: 8px;
    padding-bottom: 8px;
    border: 1px solid transparent;
    user-select: none; /* 禁止文本被选中 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    &:nth-child(3n) {
      margin-right: 0;
    }
    &-active {
      border-color: @dark_blue_color;
      color: @dark_blue_color;
      background-color: #fff;
    }

  }
}
</style>