<!--
 * @Description: 申请进度
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div>
    <van-steps direction="vertical" :active="active" :active-color="activeColor">
      <van-step>
        <h3>提交完成</h3>
        <p>{{applyTime}}</p>
      </van-step>

      <!-- 1、未上报 -->
      <van-step v-if="shzt00 === '001'">
        <h3>未上报</h3>
      </van-step>

      <!-- 2、审核中 -->
      <van-step v-if="shzt00 === '002'">
        <h3>审核中</h3>
      </van-step>
      
      <!-- 3、审核不通过 -->
      <van-step v-if="shzt00 === '004'">
        <h3>审核不通过</h3>
        <p>{{examineOpinion}}</p>
      </van-step>

      <!-- 4、审批中 -->
      <template v-if="shzt00 === '003' && spzt00 === '000'">
        <van-step>
          <h3>审核通过</h3>
          <p>{{examineOpinion}}</p>
        </van-step>

        <van-step>
          <h3>审批中</h3>
        </van-step>
      </template>

      <!-- 5、审批不通过 -->
      <template v-if="shzt00 === '003' && spzt00 === '002'">
        <van-step>
          <h3>审核通过</h3>
          <p>{{examineOpinion}}</p>
        </van-step>

        <van-step>
          <h3>审批不通过</h3>
          <p>{{reviewOpinion}}</p>
        </van-step>
      </template>

      <!-- 6、审批通过 -->
      <template v-if="shzt00 === '003' && spzt00 === '001'">
        <van-step>
          <h3>审核通过</h3>
          <p>{{examineOpinion}}</p>
        </van-step>

        <van-step>
          <h3>审批通过</h3>
          <p>{{reviewOpinion}}</p>
        </van-step>
      </template>
      
      <!-- 未开始项 -->
      <van-step v-if="shzt00 === '001'">
        <h3>审核</h3>
      </van-step>
      <van-step v-if="['001','002'].includes(shzt00)">
        <h3>审批</h3>
      </van-step>
    </van-steps>
  </div>
</template>

<script>
export default {
  props: {
    baseFormData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      activeColor: "#52C41A"
    }
  },
  computed: {
    active() {
      const {shzt00} = this.baseFormData
      return ["001", "002", "004"].includes(shzt00) ? 1 : 2
    },
    shzt00() {
      return this.baseFormData.shzt00
    },
    spzt00() {
      return this.baseFormData.spzt00
    },
    applyTime() { //申请时间
      const {createtime} = this.baseFormData      
      return `申请时间：${createtime}`
    },
    examineOpinion() { //初审意见
      const {csyj00} = this.baseFormData      
      return `初审意见：${csyj00 || "——"}`
    },
    reviewOpinion() { //复审意见
      const {fsyj00} = this.baseFormData      
      return `复审意见：${fsyj00 || "——"}`
    }
  }
}
</script>
<style lang="less" scoped>
.van-steps {
  margin-bottom: 20px;
  /deep/ .van-hairline::after {
    border: none;
    
  }
  /deep/ .van-step__title {
    & > p {
      margin-top: 4px;
      color: #999999;
    }
    &--active > p {
        color: #52C41A;
    }
  }
  
}
</style>