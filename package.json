{"name": "mobile-template", "version": "1.3.0", "private": false, "scripts": {"serve": "vue-cli-service serve --mode proxy", "lint": "eslint --fix --ext .js,.vue, src/", "build:prod": "vue-cli-service build --mode prod", "build:test": "vue-cli-service build --mode test", "new:comp": "node ./scripts/generateComponent", "new:view": "node ./scripts/generateView", "serve:proxy": "vue-cli-service serve --mode proxy", "svgo": "svgo -f src/assets/svg --config=src/assets/svg/svgo.yml", "tar": "node ./bin/tar.js", "upload:prod": "node ./bin/upload-script-pord.js", "build:test:tar:upload": "npm run build:test && npm run tar && npm run upload:prod"}, "dependencies": {"@ylz/vant": "^2.13.3-beta", "axios": "^0.21.1", "core-js": "^3.12.1", "crypto-js": "^4.2.0", "dayjs": "^1.10.4", "img-loader": "^4.0.0", "js-cookie": "^2.2.1", "jsonp": "^0.2.1", "markdown-it": "^14.1.0", "mockjs": "^1.1.0", "pdfjs-dist": "2.3.200", "postcss-pxtorem": "^5.1.1", "qrcodejs2": "^0.0.2", "vant": "2.12.23", "vconsole": "^3.5.2", "vconsole-webpack-plugin": "^1.5.2", "vue": "^2.6.12", "vue-awesome-swiper": "^3.1.3", "vue-drag-resize": "^1.5.4", "vue-jsonp": "^2.0.0", "vue-markdown": "^2.2.4", "vue-router": "^3.5.1", "vuex": "^3.6.2", "vuex-persistedstate": "^3.2.0", "weixin-js-sdk": "1.6.0"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-service": "^4.1.0", "@ylz/vue-cli-plugin-template-y-pdf": "^2.0.5", "archiver": "^4.0.1", "babel-eslint": "^10.0.3", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.0", "babel-plugin-transform-runtime": "^6.23.0", "chalk": "^3.0.0", "compression-webpack-plugin": "^3.1.0", "eslint": "^6.8.0", "eslint-plugin-vue": "^6.2.2", "fs-extra": "^9.1.0", "husky": "^4.3.8", "less": "^3.11.1", "less-loader": "^5.0.0", "lint-staged": "^10.5.4", "node-ssh": "^12.0.4", "shelljs": "^0.8.4", "style-resources-loader": "^1.3.3", "svgo": "^1.3.2", "vue-template-compiler": "^2.6.12", "webpack-theme-color-replacer": "^1.3.20"}, "browserslist": ["> 1%", "last 2 versions"], "config": {}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}}