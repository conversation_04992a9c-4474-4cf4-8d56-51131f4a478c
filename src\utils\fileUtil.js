/**
 * @description 文件工具类
 *
 * */


/**
 * 文件转base64
 *
 * @export
 *
 */
export function fileToBase64Fn(data, isNeedHeader = false) {
  return new Promise((resolve, reject) => {
    const blob = new Blob([data], { type: "image/jpg" }) // 必须指定type类型
    const reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onload = () => {
      if (isNeedHeader) {
        resolve(reader.result)
      } else {
        const result = reader.result.split("data:image/jpg;base64,")
        resolve(result[1])
      }
    }
    reader.onerror = (error) => reject(error)
  })
}

/**
 * 将base64转为URL
 * @param code base64编码 去掉前面的【data....base64,】这一段
 * @param mimeType
 *
 * */
export function base64ToBlob(code, mimeType = "application/pdf") {
  const raw = window.atob(code)
  const rawLength = raw.length
  const uInt8Array = new Uint8Array(rawLength)
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i)
  }
  return URL.createObjectURL(new Blob([uInt8Array], { type: mimeType }))
}

export function downloadByBase64(fileStream, option = {}) {
  const link = document.createElement("a")
  const mimeType = fileStream.match(/data:(\S*);base64/)[1]
  link.href = base64ToBlob(fileStream.split(",")[1], mimeType)
  link.download = option.fileType ? `${option.fileName}.${option.fileType}` : `${option.fileName}`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export function removeBase64Header(base64String) {
  const parts = base64String.split(',');
  return parts[1] ? parts[1] : '';
}

export function addBase64Header(base64String, mimeType = "image/png") {
  return `data:${mimeType};base64,${base64String}`
}
