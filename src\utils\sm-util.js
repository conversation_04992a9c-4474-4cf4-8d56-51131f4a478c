
const sm2 = require("@/utils/sm-crypto").sm2
const sm4 = require("@/utils/sm-crypto").sm4

export const privateKey = process.env.VUE_APP_PRIVATEKEY //keypair.privateKey // 私钥
export const SM4_KEY = process.env.VUE_APP_SM4KEY
export const encryptions = JSON.parse(process.env.VUE_APP_ENCRYPTIONS)

/**
 * sm4 加密
 * @param {string|object} data 要进行加密的数据
 */
 export function encrypt(data) {
  console.log("encrypt", data)
  return sm4.encrypt(JSON.stringify(data), encryptions).toUpperCase();
}

/**
 * sm4 解密
 * @param {string} data 要进行解密的数据
 */
export function decrypt(data) {
  return sm4.decrypt(data, encryptions)
}


/**
 * 签名加密
 * @param {string} params 要进行解密的数据
 */
export function generateSign(data, reqNo, timestamp, options) {
  const sortData = sort_ascii(data.data)
  const params = `appId=${process.env.VUE_APP_APPID}&data=${sortData}&reqNo=${reqNo}&serviceName=${data.serviceName}&timestamp=${timestamp}&key=${SM4_KEY}`
  return sm2.doSignature(params, privateKey, options).toUpperCase()
}


/**按ascii码从小到大排序
  * @description:
  * @param {type}
  * @return:
  * @author: wjn
 */
 export function sort_ascii(obj) {
  var arr = new Array();
  var num = 0;
  for (var i in obj) {
    arr[num] = i;
    num++;
  }
  var sortArr = arr.sort();
  var sortObj = {};
  for (var i in sortArr) {
    sortObj[sortArr[i]] = obj[sortArr[i]];
  }
  let str = "{" //自定义排序字符串
  for (const i in sortArr) {
    if (typeof (obj[sortArr[i]]) === 'object') {
      let newArr = []
      obj[sortArr[i]].forEach(item => {
        if (typeof (item) === "string") {
          newArr.push(item)
        } else {
          newArr.push(sort_ascii2(item))
        }
      });
      str += '"' + sortArr[i] + '":' + JSON.stringify(newArr) + ','
    } else {
      str += '"' + sortArr[i] + '":"' + obj[sortArr[i]] + '",'
    }

    sortObj[sortArr[i]] = obj[sortArr[i]];
  }
  //去除两侧字符串
  const char = ","
  str = str.replace(new RegExp("^\\" + char + "+|\\" + char + "+$", "g"), "")
  str += "}"
  return str
  //return sortObj;
}


export function sort_ascii2(obj) {
  var arr = new Array();
  var num = 0;
  for (var i in obj) {
    arr[num] = i;
    num++;
  }
  var sortArr = arr.sort()
  var sortObj = {}
  for (var i in sortArr) {
    if (typeof obj[sortArr[i]] === "number") {
      sortObj[sortArr[i]] = obj[sortArr[i]].toString()
    } else if (
      Object.prototype.toString.call(obj[sortArr[i]]) === "[object Array]"
    ) {
      sortObj[sortArr[i]] = obj[sortArr[i]].map(item => {
        return sort_ascii2(item)
      })
    } else if (
      Object.prototype.toString.call(obj[sortArr[i]]) === "[object Object]"
    ) {
      sortObj[sortArr[i]] = sort_ascii2(obj[sortArr[i]])
    } else if (typeof obj[sortArr[i]] === "boolean") {
      sortObj[sortArr[i]] = JSON.stringify(obj[sortArr[i]])
    } else {
      sortObj[sortArr[i]] = obj[sortArr[i]]
    }
  }
  return sortObj
}
