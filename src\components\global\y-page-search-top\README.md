<!--
 * @Description: 顶部页面路由搜索
 * @Version: 1.0.0
 * @Autor: xyDideo
-->

# y-title 标题组件

### 介绍

适用于项目头部搜索页面，返回路由与页面名称

### 引入

```js
import Vue from "vue";
import { Overlay } from "@ylz/vant";
Vue.use(Overlay);
```

## 代码演示

```html
<template>
  <y-page-search-top class="mb-10" :lists="menuLists" />
</template>
```

```js
export default {
  computed: {
    menuLists() {
      // 过滤没用路由
      return this.$router.options.routes.filter(
        (x) => x.path !== "/" && x.path !== "*"
      )
    }
  }
  created() {},
};
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| ---- | ---- | ---- | ------ |


### Event

| 事件名      | 说明           | 回调参数 |
| ----------- | -------------- | -------- |
