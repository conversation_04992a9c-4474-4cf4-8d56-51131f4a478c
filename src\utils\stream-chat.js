/*
 * @Description: 带超时控制的流式请求函数，兼容流式和普通JSON错误返回
 * @Author: 吕志伟 <EMAIL>
 * @Date: 2025-06-10 15:41:52
 * @LastEditors: AI重写
 * @LastEditTime: 2025-06-19
 */
/**
 * 带超时控制的流式请求函数，兼容流式和普通JSON错误返回
 * @param {string} url 请求地址
 * @param {Object} body 请求体
 * @param {Function} onMessage 收到每条 chunk 数据回调
 * @param {Function} onDone 所有数据接收完成回调
 * @param {Function} onError 出错或超时回调
 * @param {number} timeout 超时时间（ms）
 */

export async function streamChatRequest(url, body, onMessage, onDone, onError, tempAssistantIndex, headers) {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(body)
    });

    // 1. 先判断 content-type
    const contentType = response.headers.get('content-type') || '';
    if (contentType.includes('application/json')) {
      // 普通JSON，直接parse
      const data = await response.json();
      if (data.errorCode !== 0) {
        onError?.({ message: data.message, index: tempAssistantIndex });
        return;
      }
      // 正常数据（如需处理可加）
      onDone?.();
      return;
    }

    // 2. 流式处理
    if (!response.ok || !response.body) {
      onError?.({ message: "服务异常，请稍后重试", index: tempAssistantIndex });
      return;
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        onDone?.();
        break;
      }
      buffer += decoder.decode(value, { stream: true });
      // 按行分割
      const lines = buffer.split('\n');
      buffer = lines.pop(); // 最后一行可能不完整，留到下次
      for (const line of lines) {
        let dataStr = line.trim();
        if (dataStr.startsWith('data:')) {
          dataStr = dataStr.slice(5).trim();
        }
        if (!dataStr || dataStr === '[DONE]') continue;
        try {
          const parseData = JSON.parse(dataStr);
          let content = parseData.choices?.[0]?.delta?.content || '';
          onMessage(content, tempAssistantIndex);
        } catch (e) {
          // 不是完整JSON，拼回buffer，等下次
          buffer = dataStr + '\n' + (buffer || '');
          break;
        }
      }
    }
  } catch (error) {
    onError?.({ message: "服务异常，请稍后重试", index: tempAssistantIndex });
  }
}