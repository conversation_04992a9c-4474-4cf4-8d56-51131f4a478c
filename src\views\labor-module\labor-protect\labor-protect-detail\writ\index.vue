<!--
 * @Description: 文书/笔录--新增
 * @Version: 0.1
 * @Autor: yc
-->
<template>
  <div class="labor-protect-writ common-container">

    <apply-info ref="applyInfo" v-show="active === 0" v-model="writList" :pageType="pageType" :wordsList="wordsList"
      @handleNext="handleNext" @handleSave="handleSave" @handleAdd="handleAdd" @openSign="openSign"
      @handleMaterials="handleMaterials" @handlePrint="handlePrint" @handleUpload="handleUpload"></apply-info>
    <!-- 提交材料 -->
    <submit-material ref="submitMaterial" v-show="active === 1" :active="active" :pageType="pageType"
      :materialId="materialId" @handleNext="handleNext" @handleSave="handleSave"></submit-material>

    <sign-confirm ref="signConfirm" v-show="active === 2" :pageType="pageType" :signatureData="signatureData"
      @handleNext="handleNext" @handleSubmit="handleSubmit" @handleSave="handleSave"
      @handleRouterBack="handleRouterBack"></sign-confirm>

    <file-download ref="fileDownload" v-show="active === 3" :viewType="pageType" :writBase64Data="writBase64Data"
      @handleSave="handleSave" @handleNext="handleNext"></file-download>

    <writ-info ref="writInfo" v-show="active === 4" :pageType="pageType" @handleSave="handleSaveWrit"
      @handleNext="handleNext"></writ-info>

    <!-- <file-upload ref="fileUpload" v-show="active === 4" :pageType="pageType" :writBase64Data="writBase64Data"
      @handleSave="handleSave" @handleUpload="handleUpload" @handleNext="handleNext"></file-upload> -->
  </div>
</template>

<script>
import ApplyInfo from "./cpns/apply-info"
import SubmitMaterial from "./cpns/submit-material"
import fileDownload from "./cpns/file-download"
import SignConfirm from "./cpns/sign-confirm"
// import fileUpload from "./cpns/file-upload"
import WritInfo from "./cpns/writ-info"
import { commonApi } from "@/api"

export default {
  name: "labor-protect-writ",
  components: {
    ApplyInfo,
    SubmitMaterial,
    SignConfirm,
    // fileUpload,
    fileDownload,
    WritInfo
  },
  data() {
    return {
      active: 0,
      writList: [],
      wordsList: [],
      materialId: "",
      signatureData: "",
      formIndex: 0,
      openType: "",
      writBase64Data: "",
      pageType: ""
    }
  },
  watch: {
    active() {
      this.scrollToTop(0, 0, "auto")
    }
  },
  computed: {
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    },
    primaryKey() {
      return this.$route.query.bcz003 || ""
    }
  },
  created() {
    // 初始化表单数据
    this.getBc23List(this.primaryKey)
  },
  mounted() {
    if (window.history && window.history.pushState) {
      // 往历史记录里面添加一条新的当前页面的url
      history.pushState(null, null, document.URL)
      // 给 popstate 绑定一个方法用来监听页面返回
      window.addEventListener("popstate", this.backFn, false) //false阻止默认事件
    }

    // if (window.history && window.history.pushState) {
    //   if (window.history.length>1){
    //     const state = {
    //       key: Math.random() * new Date().getTime()
    //     }
    //     window.history.pushState(state, null, document.URL)
    //   }

    //   //给window添加一个监听事件popstate，拦截返回键，并执行方法 backFn
    //   window.addEventListener("popstate", this.backFn, false)
    // }
  },
  methods: {
    // 下一步
    handleNext(active) {
      this.active = active
      this.scrollToTop(0, 0, "auto")
    },
    async handleSaveWrit(item) {
      const params = item
      params.serviceName = "xytBc03Web_addBc23Ws"
      params.bcz003 = this.primaryKey
      const res = await commonApi.proxyApi(params)
      if (!res.map.data) {
        this.$toast("保存失败！")
        return
      } else {
        params.wsck00 = res.map.data
        this.handleNext(0)
        this.getBc23List(this.primaryKey)
      }
    },
    // 保存
    async handleSave(type) {
      if (type == "sign") {
        this.$dialog.confirm({
          title: "提示",
          message: "您确定保存信息并上传签名",
          showCancelButton: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        }).then(() => {
          const picBase64 = this.$refs.signConfirm.signature
          const params = {}
          params.serviceName = "xytBc03Web_modifyBc23Confirm"
          params.wsck00 = this.writList[this.formIndex].wsck00
          params.picBase64 = picBase64
          commonApi.proxyApi(params).then((res) => {
            this.$toast("保存成功！")
            this.handleNext(0)
            this.getBc23List(this.primaryKey)
          })
        }).catch(() => { })
      } else {
        const zjcl00 = this.$refs.submitMaterial.materialNum
        const base64 = this.$refs.fileUpload.writBase64
        let mapObj = {}
        if (this.openType == "writ") {
          if (!base64) {
            this.$toast("文件未上传")
            return
          }
          mapObj = {
            "info": { ...this.writList[this.formIndex] },
            "material": { ...this.writList[this.formIndex], base64 }
          }
        } else {
          mapObj = {
            "info": { ...this.wordsList[this.formIndex] },
            "material": { ...this.wordsList[this.formIndex], zjcl00 }
          }
        }
        let params = mapObj[type]
        delete params.createTime
        delete params.updateTime
        if (this.openType == "writ") {
          if (type == "material") {
            if (!params.wsck00) {
              params.serviceName = "xytBc03Web_addBc23Ws"
              params.zjcl00 = "writ"
              const res = await commonApi.proxyApi(params)
              if (!res.map.data) {
                this.$toast("保存失败！")
                return
              } else {
                params.wsck00 = res.map.data
              }
            }

            params = {}
            params.serviceName = "xytBc03Web_modifyUploadZjcl"
            params.wsck00 = this.writList[this.formIndex].wsck00
            params.base64 = base64
          } else {
            params.serviceName = "xytBc03Web_addBc23Ws"
          }
        } else if (this.openType == "words") {
          if (params.wsck00) {
            this.$toast("已提交，请勿重复提交！")
          }
          params.serviceName = "xytBc03Web_addBc23Bl"
        }
        commonApi.proxyApi(params).then((res) => {
          this.$toast("保存成功！")
          this.handleNext(0)
          this.getBc23List(this.primaryKey)
        })
      }
    },
    handleAdd(item) {
      const writOrWordsObj = {
        "cjsj00": "",
        "dwcl00": "",
        "slcl00": 0,
        "status": 0,
        "tjrxm0": "",
        "zjcl00": "",
        "zjclid": 0,
        "zjlx00": "",
        "zjmc00": "",
        "bcz003": this.primaryKey
      }
      if (item === "001") {
        this.openType = "writ"
        this.handleNext(4)
        // writOrWordsObj.wscllx = item
        // this.writList.unshift(writOrWordsObj)
      } else if (item === "002") {
        writOrWordsObj.wscllx = item
        this.wordsList.unshift(writOrWordsObj)
      }
      this.$refs.applyInfo.isShowAddWrit = false
      this.$refs.applyInfo.isLoading = false
    },
    handleSubmit() {
      this.handleNext(3)
    },
    handleRouterBack() {

    },
    async openSign(item, index) {
      this.$refs.fileDownload.iframeUrl = ""
      this.writBase64Data = ""
      if (item == "签名1" && this.writList[index].qzqr01 == "1") {
        return
      } else if (item == "签名2" && this.writList[index].qzqr02 == "1") {
        return
      } else if (item == "签名3" && this.writList[index].qzqr03 == "1") {
        return
      }
      if (!this.writList[index].wsck00) {
        this.$dialog.alert({
          title: "温馨提示",
          message: "请上传调解协议书文书材料！",
          theme: "round-button",
          showConfirmButton: true
        })
        return
      }
      this.signatureData = ""
      this.openType = "writ"
      this.formIndex = index
      await this.getBc03ById(this.writList[index].wsck00, item)
      const saveRes = await this.getBc23Base64ByDzd999(this.writList[index].wsck00)
      const data = saveRes.msg
      this.$refs.fileDownload.iframeUrl = "data:application/pdf;base64," + data
      this.writBase64Data = "data:application/pdf;base64," + data
      this.handleNext(3)
    },
    // 查询文书/笔录表
    getBc23List(bcz003) {
      const params = {
        serviceName: "xytBc03Web_getBc23List",
        bcz003
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "文书/笔录信息")
        const { data } = res.map
        const writArr = []
        const wordsArr = []
        for (const key in data) {
          data[key] === null && (data[key] = "")
          if (data[key].wscllx == "001") {
            writArr.push(data[key])
          } else if (data[key].wscllx == "002") {
            wordsArr.push(data[key])
          }
        }

        this.writList = writArr
        this.wordsList = wordsArr
      })
    },
    handleMaterials(index) {
      this.openType = "words"
      this.formIndex = index
      if (this.wordsList[index].zjcl00) {
        this.pageType = "detail"
      } else {
        this.pageType = ""
      }
      this.$refs.submitMaterial.getArchivesPhoneData(this.wordsList[index].zjcl00, this.pageType)
      this.handleNext(1)
    },
    async handlePrint(index) {
      this.$refs.fileDownload.iframeUrl = ""
      this.writBase64Data = ""
      this.openType = "writ"
      this.formIndex = index
      this.pageType = "notsign"
      const saveRes = await this.getBc23Base64ByDzd999(this.writList[index].wsck00)
      const data = saveRes.msg
      this.$refs.fileDownload.iframeUrl = "data:application/pdf;base64," + data
      this.writBase64Data = "data:application/pdf;base64," + data
      this.handleNext(3)
    },
    handleUpload(index) {
      this.openType = "writ"
      this.formIndex = index
      // this.writList[index].wsck00
      this.handleNext(4)
    },
    // 拦截返回键
    backFn() {
      if (this.active !== 0) {
        this.active = 0
        this.scrollToTop(0, 0, "auto")
      } else {
        this.$router.go(-1)
      }
    },
    // 查询文书/笔录表
    getBc03ById(wsck00, type) {
      const params = {
        serviceName: "xytBc03Web_getBc03ById",
        wsck00
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "文书/笔录信息")
        const { data } = res.map
        console.log(data, "文书")
        console.log(type, "类型")
        if (type === "文书") {
          this.$refs.fileDownload.iframeUrl = "data:application/pdf;base64," + data.qzcl05
          this.writBase64Data = "data:application/pdf;base64," + data.qzcl05
        } else if (type === "签名1") {
          this.signatureData = data.qzcl01
          this.pageType = "sign"
          this.$refs.signConfirm.titleContent = "申请人签名"
          if (data.qzcl01) {
            this.$refs.signConfirm.isShowSign = false
          } else {
            this.$refs.signConfirm.isShowSign = true
          }
        } else if (type === "签名2") {
          this.signatureData = data.qzcl02
          this.pageType = "sign"
          this.$refs.signConfirm.titleContent = "被申请人签名"
          if (data.qzcl02) {
            this.$refs.signConfirm.isShowSign = false
          } else {
            this.$refs.signConfirm.isShowSign = true
          }

        } else if (type === "签名3") {
          this.pageType = "sign"
          this.signatureData = data.qzcl03
          this.$refs.signConfirm.titleContent = "调解员签名"
          if (data.qzcl03) {
            this.$refs.signConfirm.isShowSign = false
          } else {
            this.$refs.signConfirm.isShowSign = true
          }
        }
      })
    },
    getYszjclPdfBase64(wsck00) {
      const params = {
        serviceName: "xytBc03Web_getYszjclPdfBase64",
        wsck00
      }
      return commonApi.proxyApi(params)
    },
    getBc23Base64ByDzd999(wsck00) {
      const params = {
        serviceName: "xytBc03Web_getBc23Base64ByDzd999",
        wsck00
      }
      return commonApi.proxyApi(params)
    }
  },
  destroyed() {
    //销毁监听
    window.removeEventListener("popstate", this.backFn, false)
  }
}
</script>

<style lang="less" scoped></style>