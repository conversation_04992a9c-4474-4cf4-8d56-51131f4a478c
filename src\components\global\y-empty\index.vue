<!--
 * @Description: 日期
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="y-empty" :style="{height: height + 'px'}">
    <div class="empty-img">
      <img :src="require('@pic/common/<EMAIL>')" alt="">
    </div>  
    <div class="empty-tip">{{tips}}</div>
  </div>
</template>

<script>
export default {
  name: "y-empty",
  props: {
    height: {
      type: String,
      default: "200"
    },
    tips: {
      type: String,
      default: "空空如也~"
    }
  }
}
</script>

<style lang="less" scoped>
.y-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;  
  .empty-img {
    width: 120px;
    & > img {
      width: 100%;
    }
  }
  .empty-tip {
    font-size: 14px;
    color: @six_text_color;
    line-height: 20px;
    margin-top: 16px;
  }
}
</style>