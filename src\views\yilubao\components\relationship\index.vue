<!--
 * @Description: 被保人与投保人关系
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="page-container p0">
    <van-field
      class="field-custom"
      v-model="ralathionship"
      name="aab303"
      label="被保人与投保人关系"
      placeholder="请选择"
      :required="true"
      :label-width="200"
      :border="false"
    >  
      <template #input>
        <div class="shortcuts">
          <div class="short-label" v-for="item in dictData" :key="item.aaa102" :class="sortcutClass(item)" @click="handleClick(item)">
            {{ item.aaa103 }}
          </div>
        </div>
      </template>
    </van-field>
  </div>  
</template>

<script>
export default {
  name: "relationship",
  model: {
    prop: "val",
    event: "change"
  },
  props: {
    val: {
      type: String,
      default: "001" // 默认本人
    },
    dictData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      ralathionship: this.val,
      shortcuts: ["本人", "配偶", "父母", "子女"]
    }
  },
  computed: {
    sortcutClass() {
      return (item) => {
        const { aaa102 } = item
        return aaa102 === this.ralathionship ? "short-label-active" : ""
      }
    }
  },
  watch: {
    val: {
      immediate: true,
      handler(val) {
        this.ralathionship = val
      }
    }
  },
  methods: {
    handleClick(item) {
      const { aaa102 } = item
      this.ralathionship = aaa102
      this.$emit("change", aaa102)
    }
  }

}
</script>

<style scoped lang="less">
.p0  {
  padding: 0;
}
/deep/.field-custom {
  display: flex;
  flex-direction: column;
}
.shortcuts {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  color: @main_text_color;
  width: 100%; // 没有设置此属性vivo 手机展示不正确
  .short-label {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 28px;
    padding: 4px 15px;
    border-radius: 14px;
    border: 1px solid rgba(0,0,0,0.1);
    cursor: pointer;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #909399;
    line-height: 20px;
    &:not(:last-child) {
      margin-right: 9px;
    }
    &-active {
      background-color: @ylb_color;
      border-color: @ylb_color;
      color: @white_text_color;
      transition: 0.5s;
    }
  }
}

</style>