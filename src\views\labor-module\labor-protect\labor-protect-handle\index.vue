<!--
 * @Description: 人民调解--新增
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-protect-handle common-container">
    <!-- 步骤条 -->
    <y-society-steps class="top-header" :handleList="handleList" :active="active"></y-society-steps>

    <!-- 提交成功页面 -->
    <y-success-page v-if="isSuccess" :text="successText"></y-success-page>

    <template v-else>
      <!-- 申请信息 -->
      <apply-info ref="applyInfo" v-show="active === 0" v-model="formData" :pageType="pageType" @handleNext="handleNext" @updateFormData="updateFormData" @handleSave="handleSave"></apply-info>

      <!-- 提交材料 -->
      <submit-material ref="submitMaterial" v-show="active === 1" :active="active" :pageType="pageType" :materialId="materialId" @handleNext="handleNext" @handleSave="handleSave"></submit-material>

      <!-- 签字确认 -->
      <sign-confirm ref="signConfirm" v-show="active === 2" :pageType="pageType" :signatureData="signatureData" @handleNext="handleNext" @handleSubmit="handleSubmit" @handleSave="handleSave" @handleRouterBack="handleRouterBack"></sign-confirm>
    </template>

  </div>
</template>

<script>
import ApplyInfo from "./cpns/apply-info"
import SubmitMaterial from "./cpns/submit-material"
import SignConfirm from "./cpns/sign-confirm"

import {commonApi} from "@/api"
import {getInfoByIdCard} from "@/utils/common"

export default {
  name: "labor-protect-handle",
  components: {
    ApplyInfo,
    SubmitMaterial,
    SignConfirm
  },
  data() {
    return {
      active: 0,
      handleList: [
        "申请信息",
        "提交材料",
        "签字确认"
      ],

      formData: {
        aac003: "",
        aac004: "",
        ccg981: "",
        aac002: "",
        aac005: "",
        aca111: "",
        aac149: "",
        aac152: "",
        aac150: "",
        aac147: "",
        zgbm00: "",
        aac151: "",
        aab004: "",
        aab503: "",
        aab505: "",
        abb286: "",
        aba002: "",
        ywlx: "新就业形态申请",
        aac153: "",
        aac154: "",
        aac148: "",
        aab300: "", //户籍详细地址
        aab303: "", //现居住地详细地址
        aba004: "",
        abb289: "1", //数据来源 1：微信端 2：个人网页端 3：来访 4：上级交办 5：12345
        aab299: "", //户籍所在地
        aab299Name: "", //户籍所在地 名称
        aab302: "", //现居住地
        aab302Name: "" //现居住地 名称
      },

      successSubmit: false, //是否提交成功
      signatureData: "", //签名base64

      successText: "保存成功",
      isSuccess: false
    }
  },
  watch: {
    active() {
      this.scrollToTop(0, 0, "auto")
    }
  },
  computed: {
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    },
    pageType() {
      return this.$route.query.pageType || ""
    },
    primaryKey() {
      return this.$route.query.bcz001 || ""
    },
    materialId() {
      console.log(this.formData.abz200, "this.formData.abz200")
      return this.formData.abz200 || ""
    }
  },
  created() {
    // 初始化表单数据
    this.initFormData()

    // 编辑和查看详情 查询已保存信息
    if (this.pageType === "add") {
      return
    }
    console.log(this.pageType, "this.pageType")
    this.getBc01ById(this.primaryKey)
  },
  mounted() {
    if (window.history && window.history.pushState) {
      if (window.history.length>1){
        const state = {
          key: Math.random() * new Date().getTime()
        }
        window.history.pushState(state, null, document.URL)
      }

      //给window添加一个监听事件popstate，拦截返回键，并执行方法 backFn
      window.addEventListener("popstate", this.backFn, false)
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      const {xm0000: aac003, zjhm00: aac002, mobile: aae005} = this.userInfo
      const {sex: aac004, area: aab299} = getInfoByIdCard(aac002)
      this.formData = {...this.formData, aac003, aac002, aae005, aac004, aab299}
    },

    // 更新表单数据
    updateFormData(data) {
      this.formData = { ...this.formData, ...data }
    },

    // 拦截返回键
    backFn() {
      // 未完成提交 改变active状态
      if ((this.active === 1 || this.active === 2) && !this.successSubmit) {
        this.active--
        const state = {
          key: Math.random() * new Date().getTime()
        }
        window.history.pushState(state, null, document.URL)
        return
      }

      // 第一步或提交成功状态 返回上一页面
      this.$router.go(-1)
    },

    // 下一步
    handleNext(active) {
      this.active = active
      this.scrollToTop(0, 0, "auto")
    },

    // 保存
    handleSave(type) {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定保存信息为待提交状态",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        const abz200 = this.$refs.submitMaterial.materialNum
        const qmzp00Base64 = this.$refs.signConfirm.signature
        const mapObj = {
          "info": {...this.formData},
          "material": {...this.formData, abz200},
          "sign": {...this.formData, abz200, qmzp00Base64}
        }

        const params = mapObj[type]
        delete params.createTime
        delete params.updateTime
        params.serviceName = "xytBc01_saveOrUpdateBc01"
        commonApi.proxyApi(params).then((res) => {
          this.$toast("保存成功！")
          setTimeout(() => {
            this.$router.go(-2)
          }, 300)
        })
      }).catch(() => { })
    },

    // 提交
    async handleSubmit() {
      this.successSubmit = true
      const abz200 = this.$refs.submitMaterial.materialNum
      console.log(abz200, "abz200")
      const qmzp00Base64 = this.$refs.signConfirm.signature

      const saveParams = {
        ...this.formData,
        serviceName: "xytBc01_saveOrUpdateBc01",
        abz200,
        qmzp00Base64
      }
      delete saveParams.createTime
      delete saveParams.updateTime

      const saveRes = await commonApi.proxyApi(saveParams) //保存接口
      console.log(saveRes, "saveRes")

      const {data} = saveRes.map
      const submitParams = {
        serviceName: "xytBc01_submitBc01",
        bcz001: data
      }

      const submitRes = await commonApi.proxyApi(submitParams) //提交接口
      console.log(submitRes, "submitRes")

      this.successText = "提交成功"
      this.isSuccess = true
    },

    // 详情页面返回
    handleRouterBack() {
      this.successSubmit = true
      this.$router.go(-1)
    },

    // 查询案件信息
    getBc01ById(bcz001) {
      const params = {
        serviceName: "xytBc01_getBc01ById",
        bcz001
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "案件信息")
        const {data} = res.map
        for (const key in data) {
          data[key] === null && (data[key] = "")
        }

        this.formData = {...this.formData, ...data}
        if (this.pageType === "detail") {
          this.$refs.applyInfo.formRules = {}
        }

        this.$refs.submitMaterial.getArchivesPhoneData(this.formData.abz200) //获取外网材料电子档案
        this.signatureData = this.formData.qmzp00Base64
      })
    }
  },
  destroyed(){
    //销毁监听
    window.removeEventListener("popstate", this.backFn, false)
  }
}
</script>

<style lang="less" scoped>

</style>