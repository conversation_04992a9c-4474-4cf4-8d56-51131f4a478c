<!--
 * @Description: 
 * @Author: FBZ
 * @Date: 2024-05-23 17:04:39
 * @LastEditors: FBZ
 * @LastEditTime: 2024-05-24 10:10:55
-->
<template>
  <div>
    <van-button @click="activeMeeting">发起会议</van-button>
    <van-button @click="nextStep">下一步</van-button>
  </div>
</template>

<script>
export default {
  name: "step1",
  methods: {
    activeMeeting() {
      // 唤起会议组件
      this.$store.dispatch("meeting/activeMeeting")
    },
    nextStep() {
      // 测试跳转
      this.$router.push("/meeting-demo-step2")
    }
  }
}
</script>

<style lang="less" scoped>
</style>
