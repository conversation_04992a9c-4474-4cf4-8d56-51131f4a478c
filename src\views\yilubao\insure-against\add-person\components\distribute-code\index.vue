<!--
 * @Description: 分销码信息
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="page-container">
    <van-cell-group inset >
      <van-field
        v-model="formData.aab004"
        name="aab004"
        label="保险公司"
        label-width="70"
        placeholder="请输入"
        disabled
      />
      
      <van-field
        v-model="formData.aac003"
        name="aac003"
        label="业务员"
        placeholder="请输入"
        label-width="200"
        disabled
      />
      
      <van-field
        v-model="formData.rygh00"
        name="rygh00"
        label-width="200"
        label="工号"
        placeholder="请输入"
        disabled
      />
    </van-cell-group>
  </div>
</template>

<script>
export default {
  name: "distribute-code",

  model: {
    prop: "formData"
  },
  props: {
    formData: {
      type: Object,
      require: true
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.van-field__control--custom {
  display: flex;
  justify-content: flex-end;
}
</style>