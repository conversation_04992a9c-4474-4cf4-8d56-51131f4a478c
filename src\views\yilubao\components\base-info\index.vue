<!--
 * @Description: 投保人基础信息
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="page-container">    
      <van-field
        v-model="formData.aac003"
        name="aac003"
        label="姓名"
        placeholder="请输入"
        :required="required"
        :rules="formRules.aac003"
        :disabled="disabled"
      />

      <!-- <y-select-dict v-model="formData.ccg981" label="证件类型" dict-type="CCG981" :disabled="disabled" is-link /> -->
      <y-select-dict v-model="formData.ccg981" label="证件类型" :rules="formRules.ccg981" dict-type="CCG981" :disabled="disabled" is-link />

      <van-field
        v-model="formData.aac002"
        name="aac002"
        label="证件号码"
        placeholder="请输入"
        :required="required"
        :rules="formRules.aac002"
        :disabled="disabled"
        @blur="checkIsAallowedApply"
      />

      <van-field
        v-model="formData.aae005"
        name="aae005"
        label="手机号码"
        placeholder="请输入"
        :required="required"
        :rules="formRules.aae005"
      />        
      <van-field
        v-show="needEmail"
        v-model="formData.aae006"
        name="aae006"
        label="电子邮箱"
        placeholder="请输入"
        :rules="formRules.aae006"
      />       
  </div>
</template>

<script>
import {commonApi} from "@api"

import { ylb_color } from "@/styles/theme/theme-params.less"
import {validateIdCard, checkMobile} from "@utils/check"

export default {
  name: "base-info",
  model: {
    prop: "formData"
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      require: true
    },
    needEmail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 标题
      ylb_color,
      showBaseInfo: true, //申请人基本信息
      showUnitInfo: true, //被申请人人单位信息
      showRequestInfo: true, //申请人请求

      // 表单
      formRules: {
        aac003: [{ required: true, message: "请输入" }],
        ccg981: [
          { required: true, message: "请选择"}
        ],
        aac002: [
          { required: true, message: "请输入" }
          
        ],
        aae005: [
          { required: true, message: "请输入" },
          {
            validator: checkMobile,
            message: "请输入正确的手机号",
            trigger: "onBlur"
          }
        ]
      },
      required: true

    }
  },
  watch: {
    "formData.ccg981": {
      immediate: true,
      handler(val) {
        if (val === "001") {
          const rules = JSON.parse(JSON.stringify(this.formRules.aac002))
          rules.push({
            validator: validateIdCard,
            message: "请输入正确身份证号码",
            trigger: "onBlur"
          })
          this.formRules.aac002 = rules
          return
        }

        this.formRules.aac002 = [{ required: true, message: "请输入" }]
      }
    }
  },
  created() {
  },
  methods: {
    // 校验是否允许投保 （灵活就业人员等允许）
    async checkIsAallowedApply() {
      const {ccg981, aac002} = this.formData

      let checkRes = true
      if (!ccg981 || ccg981 === "001") {
        checkRes = validateIdCard(aac002)
      }
      if (!checkRes) {        
        return
      }

      const params = {
        serviceName: "xytDa01Web_checkApplyAvailableByAac002",
        aac002
      }
      const res = await commonApi.proxyApi(params)
      const {flag} = res.map.data
      if (!flag) {
        const message = "投保人承诺此次“益鹭保”投保时间范围内，被保人需从事灵活就业相关工作！"
        this.$dialog.alert({
          title: "提示",
          message,
          theme: "round-button",
          className: "ylb-dialog-alert"
        })
      }      
    },
    onFailed() {

    },
    handleNext() {

    }
  }
}
</script>

<style lang="less" scoped>
.page-wrapper {
  padding: 0 20px;
  background-color: #fff;
}
</style>