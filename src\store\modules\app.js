export default {
  namespaced: true,
  state: {
    defaultRatio: 2,
    ratio: 2, // 缩放比例
    mode: "default",
    version: "V1.0.0" // 统一输出接口请求版本
  },

  mutations: {
    UPDATE_RATIO(state, payload) {
      state.defaultRatio = state.ratio
      state.ratio = payload
    },
    UPDATE_MODE(state, payload) {
      state.mode = payload
    }
  },

  actions: {
    updateRatio({ commit }, payload) {
      commit("UPDATE_RATIO", payload)
    },
    updateMode({ commit }, payload) {
      commit("UPDATE_MODE", payload)
    }
  }
}
