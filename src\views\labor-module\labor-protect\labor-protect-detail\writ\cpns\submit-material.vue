<!--
 * @Description: 文书/笔录材料
 * @Version: 0.1
 * @Autor: yc
-->

<template>
  <div class="submit-material">
    <!-- 重要提示 -->
    <y-tips :tipsList="tipsList"></y-tips>

    <div class="iframe-box">
      <iframe id="iframe" class="my-iframe" :src="iframeUrl" frameborder="0"></iframe>
    </div>

    <div class="button-box-more">
      <van-button  plain type="info" @click="handleBack">
        返回
      </van-button>
      <van-button v-if="pageType !== 'detail'"  round block type="primary" native-type="submit" @click="handleSave">
        保存
      </van-button>
    </div>
    
  </div>
</template>

<script>
import {commonApi} from "@/api"
export default {
  name: "submit-material",
  props: {
    active: {
      type: Number,
      default: 0
    },
    pageType: {
      type: String,
      default: ""
    },
    materialId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      tipsList: ["上传材料后，务必点击保存或提交按钮，上传的材料才会保存。"],

      materialNum: "", //材料编号
      iframeUrl: "" //上传材料页面地址
    }
  },
  computed: {
  },
  created() {
    this.getArchivesPhoneData() //获取外网材料电子档案
  },
  methods: {
    //获取外网材料电子档案
    getArchivesPhoneData(dzd999 = "", pageType) {      
      const nwFlag = process.env.VUE_APP_NW_FLAG === "true"
      const modifyFlag = pageType !== "detail"
      console.log(dzd999, "dzd999*****")
      const params = {
        serviceName: "xytCommon_getArchivesPhoneData",
        aaa121: "BCZ0008", //业务编号
        modifyFlag, //修改标识
        dzd999, //业务受理ID
        nwFlag //内网标识
      }
      commonApi.proxyApi(params).then((res) => {
        const { url, dzd999 } = res.map.data
        this.materialNum = dzd999
        console.log(dzd999, "dzd999")
        console.log(this.materialNum, "this.materialNum")
        this.iframeUrl = url
      })
    },
    // 保存 存储数据 返回
    handleSave() {
      this.$emit("handleSave", "material")
    },
    // 上一步
    handleBack() {
      this.$emit("handleNext", 0)
    }
  }
}
</script>

<style lang="less" scoped>
.iframe-box {
  height: calc(100vh - 384px);
  width: 100vw;
  overflow: hidden;
  .my-iframe {
    width: 200%;
    height: 200%;
    transform: scale(0.47);
    transform-origin: 0 0;
    margin-left: 12px;
  }
}

</style>