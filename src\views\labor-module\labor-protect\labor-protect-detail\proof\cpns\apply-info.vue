<!--
 * @Description: 证据材料
 * @Version: 0.1
 * @Autor: yc
-->

<template>
  <div class="apply-info">
    <div class="top-div">
      <span class="right-box">
        <van-button class="add-button flex-c-c" type="primary" @click="handleAdd">
          <span class="ml2">新增</span>
        </van-button>
      </span>
    </div>
    <div v-if="proofList?.length > 0">
      <van-form class="base-form" @failed="onFailed" @submit="handleNext" :disabled="pageType === 'detail'">
      <div v-for="(item, index) in proofList" :key="index">
        <y-title :content="'证据材料' + (index + 1)" moreText :colorMore="colorMore" :moreType="1">
          <template slot="right-box">
            <span class="right-box">
              <span class="point" :style="{ 'background': STATUS[item.status]['color'] }"></span>
              <span class="text" :style="{ 'color': STATUS[item.status]['color'] }">{{ STATUS[item.status]['text']
                }}</span>
            </span>
          </template>
        </y-title>
        <van-cell-group inset>

          <van-field v-model="proofList[index].zjmc00" name="zjmc00" label="证据名称" placeholder="请输入" :required="required" :disabled="proofList[index].status == '1'"
            :rules="formRules.zjcl00"  />

          <y-select-dict  v-model="proofList[index].zjlx00"  label="类型" :rules="formRules.zjlx00"  :required="required" :disabled="proofList[index].status == '1'" dict-type="ZJLX00" is-link />

          <van-field v-model="proofList[index].slcl00" name="slcl00" label="数量" placeholder="请输入" :required="required" :disabled="proofList[index].status == '1'"
            :rules="formRules.slcl00"  />

          <van-field v-model="proofList[index].dwcl00" name="dwcl00" label="单位" placeholder="请输入" :required="required" :disabled="proofList[index].status == '1'"
            :rules="formRules.dwcl00"  />
          <van-field v-if="proofList[index].cjsj00" v-model="proofList[index].cjsj00" name="cjsj00" label="生成时间"
            placeholder="请输入" :formatter="formatterTime"  disabled />

          <van-field v-if="proofList[index].tjrxm0" v-model="proofList[index].tjrxm0" name="aae005" label="提交人"
            placeholder="请输入"   disabled />
          <van-field v-if="false" disabled />
        </van-cell-group>
        <div class="btn-box flex-c-e border-bottom-wide">
          <van-button type="primary" @click="handleMaterials(index)">证据材料</van-button>
        </div>
      </div>
      
    </van-form>
    </div>
    <y-empty v-else></y-empty>
  </div>
</template>

<script>
import {
  five_text_color
} from "@/styles/theme/theme-params.less"
import { validateIdCard, checkMobile } from "@utils/check"
import { commonApi } from "@/api"

const STATUS = {
  "0": {
    text: "待核验",
    color: "#FFBF00"
  },
  "1": {
    text: "已核验",
    color: "#0AC673"
  }
}
export default {
  name: "apply-info",
  model: {
    prop: "proofList"
  },
  props: {
    proofList: {
      type: Array,
      require: true
    },
    pageType: {
      type: String,
      default: ""
    },
    formIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      STATUS,
      // 标题
      colorMore: five_text_color,
      // 表单
      formRules: {
        aac003: [{ required: true, message: "请输入" }],
        aac004: [{ required: true, message: "请选择" }],
        ccg981: [{ required: true, message: "请选择" }],
        aac002: [
          { required: true, message: "请输入" },
          {
            validator: validateIdCard,
            message: "请输入正确身份证号码",
            trigger: "onBlur"
          }
        ],
        aae005: [
          { required: true, message: "请输入" },
          {
            validator: checkMobile,
            message: "请输入正确的手机号",
            trigger: "onBlur"
          }
        ],
        aca111: [{ required: true, message: "请选择" }],
        aab299: [{ required: true, message: "请选择" }],
        aab302: [{ required: true, message: "请选择" }],

        aac149: [{ required: true, message: "请选择" }],
        aac152: [{ required: true, message: "请输入" }],
        aac150: [{ required: true, message: "请选择" }],
        aac147: [{ required: true, message: "请输入" }],
        zgbm00: [{ required: true, message: "请选择" }],
        aac151: [{ required: true, message: "请选择" }],
        aab004: [{ required: true, message: "请输入" }],
        aab009: [{ required: true, message: "请输入" }],
        aae007: [{ required: true, message: "请输入" }],
        aae008: [{ required: true, message: "请输入" }],
        abb286: [{ required: true, message: "请输入" }],
        aba003: [{ required: true, message: "请输入" }],
        aba002: [{ required: true, message: "请选择" }],
        aac154: [{ required: true, message: "请输入" }],
        aac153: [{ required: true, message: "请输入" }],
        aac148: [{ required: true, message: "请输入" }],
        aab300: [{ required: true, message: "请输入" }],
        aab303: [{ required: true, message: "请输入" }],
        aba004: [{ required: true, message: "请输入" }]
      },
      required: true,
      zjlx00List: [],
      dwcl00List: []
    }
  },
  watch: {
    searchValue: {
      handler(val) {
        if (val) {
          this.filterData(val)
        }
      },
      immediate: true
    }
  },
  computed: {
  },
  async created() {
    this.getDicData()
  },
  methods: {
    formatterTime(time) {
      return this.dayFormatFn(String(time), "YYYY-MM-DD HH:mm")
    },   
    // 下一步
    handleNext() {
      this.$emit("handleNext", 1)
    },
    // 表单校验失败
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },
    handleAdd() {
      this.$emit("handleAdd", 1)
    },
    handleMaterials(data) {
      this.$emit("handleMaterials", data)
    },
    /**
     * @description: 获取字典数据
     * @param {*}
     * @return {*}
     * @author: T
     */    
    getDicData() {
      commonApi.proxyApi({ 
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["ZJLX00"]
      }).then(res => {
        const {data} = res.map.data
        // const dictInfo = {
        //   "ZJLX00": "zjlx00List"
        // }
        this.zjlx00List = data
      })
    }
    // DwclConfirm(data) {
    //   this.showPicker = false
    //   const { text: dwcl00Text, value: dwcl00 } = data
    //   this.$set(this.proofList[this.formIndex].dwcl00, "dwcl00Text", dwcl00Text)
    //   this.$set(this.formData, "ptbm00", dwcl00)
    // },
    // ZjlxConfirm(data) {
    //   this.showPicker = false
    //   const { text: ptbm00Text, value: ptmc00 } = data
    //   this.$set(this.formData, "ptbm00Text", ptbm00Text)
    //   this.$set(this.formData, "ptbm00", ptmc00)
    // }
  }
}
</script>

<style lang="less" scoped>
.search-field {
  /deep/.van-field__control {
    text-align: left;
  }
}

.btn-box {
  // height: 46px;
  padding: 9px 14px 9px 0px;
  box-sizing: border-box;

  .van-button {
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 28px;
    width: 80px;
    height: 28px;
    padding: 0;
    border-radius: 14px;
    margin-left: 12px;
  }
}

.top-div {
  height: 30px;
  .right-box {
    position: absolute;
    right: 0;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    .van-button {
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 28px;
      width: 80px;
      height: 28px;
      padding: 0;
      border-radius: 14px;
      margin-left: 12px;
    }
  }
}

.base-form {
  .right-box {
    position: absolute;
    right: 0;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    &>.point {
      width: 8px;
      height: 8px;
      border-radius: 4px;
      display: inline-block;
      margin-bottom: 1px;
    }

    &>.text {
      margin-left: 6px;
    }
  }
}

.evaluate-popup {
  width: 80% !important;
  border-radius: 8px;

  .evaluate-box {
    .evaluate-title {
      text-align: left;
      font-size: 16px;
      margin: 16px 0;
      padding-left: 16px;
      font-weight: 600;
    }

    .evaluate-button {
      padding: 12px 0;

      .van-button--primary {
        margin-left: 8px;
      }
    }

    .online-revoke-icon {
      display: flex;
      justify-content: center;
      color: #32ae57;
      font-size: 65px;
    }

    .online-revoke-icon {
      display: flex;
      justify-content: center;
      color: #32ae57;
      font-size: 65px;
    }

    .online-revoke-text {
      font-size: 12px;
      text-align: center;
      margin-bottom: 16px;
      margin-top: 4px;
    }

    /deep/.van-radio-group {
      padding: 4px 16px;
      font-size: 14px;
    }

    /deep/.van-field__control {
      text-align: right;
    }
  }
}
</style>