<!--
 * @Description: 掌上分销
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="palmar-distributor">
    <!-- 工作台/主题/我的 -->
    <component :is="componentName" :baseInfo="baseInfo"></component>

    <!-- 底部tab -->
    <van-tabbar v-model="active" @change="handleChange">
      <van-tabbar-item v-for="(item , key) in iconList" :key="key">
        <span>{{ item.name }}</span>
        <template #icon="props">
          <img :src="props.active ? item.active : item.inactive" />
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
// pages
import WorkPlatform from "./work-platform"
import ThemePage from "./theme-page"
import MinePage from "./mine-page"

// icons
import claimsCheckActive from "@pic/yilubao/icons/<EMAIL>"
import claimsCheck from "@pic/yilubao/icons/<EMAIL>"
import insureAgainstActive from "@pic/yilubao/icons/<EMAIL>"
import insureAgainst from "@pic/yilubao/icons/<EMAIL>"
import insureViewActive from "@pic/yilubao/icons/<EMAIL>"
import insureView from "@pic/yilubao/icons/<EMAIL>"

import {commonApi} from "@/api"
export default {
  name: "palmar-distributor",
  components: {
    WorkPlatform,
    ThemePage,
    MinePage
  },
  data() {
    return {
      componentName: "WorkPlatform",
      active: 0,
      iconList: [
        {
          name: "工作台",
          inactive: insureAgainst,
          active: insureAgainstActive
        },
        {
          name: "主题",
          inactive: insureView,
          active: insureViewActive
        },
        {
          name: "我的",
          inactive: claimsCheck,
          active: claimsCheckActive
        }
      ],
      baseInfo: {}      
    }
  },
  watch: {
    active(val) {
      if (val === 0) {
        this.getDa05ByCurrentUser()
      }
    }
  },
  async created() {     
    await this.getDa05ByCurrentUser() //查询-保险业务员信息 
  },
  methods: {
    async getDa05ByCurrentUser() {
      const params = {
        serviceName: "xytPerson_getDa05ByCurrentUser"
      }
      const res = await commonApi.proxyApi(params)
      console.log(res, "查询-保险业务员信息")
      const data = res.map.data 
      this.baseInfo = data 
      this.$bus.$emit("getAction", data)
    },
    handleChange(activeVal) {
      this.scrollToTop(0, 0, "auto") //滚动到顶部
      const components = ["WorkPlatform", "ThemePage", "MinePage"]
      this.componentName = components[activeVal]
    }
  }
}
</script>

<style lang="less" scoped>
.yilubao {
  padding-bottom: 72px;
}
/deep/ .van-tabbar-item {
  color: #333333;
  &--active {
    font-weight: 500;
  }
}
/deep/ .van-tabbar {
  height: 72px;
  .van-tabbar-item {
    line-height: 14px;
    .van-tabbar-item__icon {
      width: 24px;
      height: 24px;
      img {
        height: 24px;
      }
    }
  }  
}
</style>
