<template>
  <div class="new-digital-human">
    <img src="~@pic/home/<USER>/digital-human-top-img.png" class="digital-human-top-img"/>
    <span class="digital-human-tip">可为您提供社保、就业等政策解答</span>
    <div id="wrapper" class="digital-human-wrapper"></div>
    <MessageBox
      ref="messageBox"
      :messages="messages"
      :isStreaming="isStreaming"
      :lastStatus="lastStatus"
      class="digital-human-message-box"
      :assistantProfile="false"
      :guessQuestions="guessQuestions"
      :virtualHuman="true"
      @handleVolume="handleVolume"
      @sendGuessQuestion="sendGuessQuestion"
    ></MessageBox>
    <SearchBox
      ref="searchBox"
      :messageBoxRef="$refs.messageBox"
      :virtualHuman="true"
      :hasVirtualHuman="true"
      style="z-index: 2"
      :longVoice="longVoice"
      :messages="messages"
      :isPlaying="isPlaying"
      :forcePaused="forcePaused"
      @sendMessage="val => messages = val"
      @update:isStreaming="val => isStreaming = val"
      @update:lastStatus="val => lastStatus = val"
      @toNewDigitalHuman="toNewDigitalHuman"
      @getVoiceData="val => sentMessage(val)"
      @stopAnswer="stopAnswer"
      @back="showConfirmDialog = true"
      @changeLongVoice="changeLongVoice"
      @guessQuestions="val => guessQuestions = val"
    ></SearchBox>
    <div v-if="showConfirmDialog" class="confirm-container">
      <div class="confirm-box">
        <img src="~@pic/home/<USER>/confirm.png" class="confirm-bg">
        <img src="~@pic/home/<USER>/text-dialogue-mode.png" class="confirm-text">
        <div class="confirm-btn" @click="confirmSwitch">确认切换</div>
        <div class="close-btn" @click="showConfirmDialog = false"></div>
      </div>
    </div>
    <MaskContent v-if="showMask"></MaskContent>
  </div>
</template>

<script>
import digitalHumanMixin from "./mixins"
import MessageBox from "@/views/home-modules/components/message-box/index.vue"
import SearchBox from "@/views/home-modules/components/search-box/index.vue"
import MaskContent from "@/views/home-modules/components/mask-content/index.vue"
import { setMessages } from "@/utils/cookie"

export default {
  mixins: [digitalHumanMixin],
  components: {
    MessageBox,
    SearchBox,
    MaskContent
  },
  data() {
    return {
      isStreaming: false,
      lastStatus: false,
      isPlay: true,
      guessQuestions: [],
      showConfirmDialog: false,
      type: "new",
      showMask: false
    }
  },
  watch: {
    messages: {
      handler(newVal) {
        if (newVal && newVal.length) {
          setMessages(this.type, JSON.stringify(newVal))
        }
      },
      deep: true
    },
    showMask: {
      handler(newVal) {
        if (newVal) {
          setTimeout(() => {
            this.showMask = false
          }, 2000)
        }
      }

    }
  },
  methods: {
    setGuessQuestions() {
      this.guessQuestions = []
    },
    changeLongVoice(val) {
      this.longVoice = val
      this.stopRecord()
    },
    toNewDigitalHuman() {
      this.showMask = true
      this.longVoice = true
      this.startRecord()
    },
    confirmSwitch() {
      this.showConfirmDialog = false
      this.$emit("changeLongVoice", false)
    }
  }
}
</script>

<style lang="less" scoped>
.new-digital-human {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
  font-size: 14px;
  background: transparent url("~@pic/home/<USER>/virtual-human-bg.png") no-repeat center / 100% 100%;
  
  .digital-human-top-img {
    width: 100%;
    height: 220px;
    object-fit: fill;
    position: absolute;
    z-index: 3;
  }

  .digital-human-tip {
    position: absolute;
    top: 85px;
    left: 20px;
    color: #6D848F;
    z-index: 5;
  }

  .digital-human-wrapper {
    position: absolute;
    top: 35px;
    right: -20px;
    width: 200px;
    height: 200px;
    /* 确保数字人内容能正确显示 */
    z-index: 5;
  }
  
  .digital-human-message-box {
    z-index: 2;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg,#ebf6ff, #a9e0f4 100%);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    padding-top: 20px;
    padding-bottom: 30px;

    position: relative;
    top: 188px;
    height: calc(100vh - 250px) !important;
  }
}

.confirm-container {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  z-index: 999;

  .confirm-box {
    width: 100%;
    position: relative;

    .confirm-bg {
      width: 100%;
    }

    .confirm-text {
      width: 70%;
      position: absolute;
      top: 42px;
      left: 60px;
    }
    
    .confirm-btn {
      font-size: 20px;
      color: #0194FF;
      padding: 8px 20px;
      position: absolute;
      bottom: 80px;
      left: calc(50% - 20px);
      border-radius: 25px;
      border: 0.5px solid rgba(95,255,255,0.75);
      background: linear-gradient(180deg,#ffffff, #d5fff7 80%, #90ffeb 100%);
      box-shadow: 0px 0px 20px 0px rgba(65,255,198,0.42), 0px 0px 15px 0px rgba(65,255,198,0.47);
    }

    .close-btn {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      position: absolute;
      bottom: 2px;
      left: calc(50% - 15px);
    }
  }
}
</style>