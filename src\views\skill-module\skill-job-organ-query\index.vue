<!--
 * @Description: 新就业形态工种机构培训情况
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="skill-job-organ-query">
    <van-list v-model="loading" :finished="finished" :finished-text="list.length > 0 ? '没有更多了' : ''" @load="onLoad">

      <div class="search-container flex-c-c">
        <van-search class="search-box" v-model.trim="organName" show-action placeholder="请输入工种关键词查询" @search="handleSearch">
          <template #action>
            <div @click="handleSearch">搜索</div>
          </template>
        </van-search>
      </div>      

      <template v-if="list.length > 0">
        <div class="item-box" v-for="(item,index) in list" :key="index">
          <div class="item-box-top flex-c-sb">
            <div class="item-name">
              {{ item.gzmc00 }}
            </div>
            <div class="item-hand flex-c-c" @click="handleViewOrgan(item)">
              <span>查看机构</span>
              <van-icon v-show="!item.flexable" name="arrow" />
              <van-icon v-show="item.flexable" name="arrow-down" />
            </div>
          </div>

          <div class="item-box-bottom flex-c-c" :class="!item.flexable ? 'flexable' : ''">
            <van-list v-if="item.data.length > 0">
              <van-cell v-for="e in item.data" :key="e.pxjgid" 
              @click="handleViewOrganDetails(e)" :title="e.aab004" />
            </van-list>
            <y-empty v-else tips="暂无相关机构开展该工种培训"></y-empty>
          </div>
        </div>
      </template>
      
      <template v-else>
        <y-empty></y-empty>
      </template>
    </van-list>
  </div>
</template>

<script>
import { commonApi } from "@/api"

export default {
  name: "skill-job-organ-query",
  data() {
    return {
      // 列表
      list: [],
      loading: false,
      finished: false,
      searchParams: {
        page: 0,
        size: 10
      },

      // 机构是否显示
      flexable: false,

      // 搜索
      organName: ""
    }
  },
  methods: {
    onLoad() {
      this.searchParams.page++
      this.getXytgzlb()
    },
    // 搜索
    handleSearch() {
      this.searchParams.page = 1
      this.finished = false
      this.list = []
      this.getXytgzlb()
    },
    // 查询工种机构信息
    getXytgzlb() {
      commonApi      
        .proxyApi({
          serviceName: "xytJnpx_getXytgzlb",
          ...this.searchParams,
          gzmc00: this.organName
        })
        .then(res => {
          const { rows = [], total = 0 } = res.map?.data || []
          this.list = [...this.list, ...rows]
          console.log(this.list, "this.list")

          // 加载状态结束
          this.loading = false

          // 数据全部加载完成
          if (this.list.length === total) {
            this.finished = true
          }
        })
    },

    // 查看机构
    handleViewOrgan(data) {
      console.log(data, "data")
      // const {gzmc00} = data
      // this.
      data.flexable = !data.flexable
      console.log(this.list, "list")
      this.$forceUpdate()
    },
    // 查看机构详情
    handleViewOrganDetails(data) {
      const { pxjgid } = data
      this.$router.push({
        path: "/skill-job-organ-details",
        query: { pxjgid }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.skill-job-organ-query {
  padding: 68px 16px 16px; 
  .search-container {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #fff;
    width: 100%;
    padding: 16px;
    z-index: 10;
    /deep/ .search-box { 
      width: 100%;     
      border: 1px solid @main_color;
      height: 36px;
      border-radius: 18px;   
      .van-search__content {
        background-color: #fff;
      }
      .van-search__action {
        color: @main_color;
      }
    }
  }   
  
  .item-box {
    width: 345px;
    background: @background_shallow_color;
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 14px;
    &-top {
      margin: 0 12px 0 30px;
      .item-name {
        flex: 1;
        font-size: 14px;
        color: #bd1a2d;
        line-height: 20px;
        position: relative;
        &::before {
          content: " ";
          width: 6px;
          height: 6px;
          border-radius: 50%;
          border: 1px solid #bd1a2d;
          position: absolute;
          top: 50%;
          transform: translate(0, -50%);
          left: -16px;
        }
      }
      .item-hand {
        width: 84px;
        height: 28px;
        border-radius: 22px;
        border: 1px solid #bd1a2d;
        font-family: PingFangSC, PingFang SC;
        font-size: 14px;
        color: #bd1a2d;
        line-height: 24px;
        text-align: center;
        position: relative;
        .van-icon {
          margin-top: 2px;
        }
      }
    }

    &-bottom {
      width: 100%;
      max-height: 247px;
      background: #fff;
      border-radius: 0 0 8px 8px;
      overflow-y: scroll;
      transition: height 0.3s ease-in-out;
      margin-top: 12px;
      /deep/.van-list {
        width: 100%;
      }
      /deep/.van-cell__title {
        text-align: left;
        width: 100%;
        & > span {
          display: inline-block;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          word-wrap: break-word;
          line-height: 24px;
        }
      }
      &.flexable {
        height: 0;
        margin-top: 0;
        transition: height 0.3s ease-in-out;
      }
    }
  }
}
</style>