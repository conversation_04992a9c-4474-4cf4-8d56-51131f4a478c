<!--
 * @Description: 投保查看
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="ylb-container">
    <van-sticky>
      <van-tabs v-model="active">
        <van-tab v-for="(item, key) in tabList" :title="item.text" :name="item.id" :key="key">
        </van-tab>
      </van-tabs>
    </van-sticky>

    <view-box :dictData="dictData" :formList="formList" @handlePay="handlePay" @handleCancel="handleCancel"></view-box>
  </div>
</template>

<script>
import { commonApi } from "@/api"
import ViewBox from "./components/view-box"
export default {
  name: "insure-view",
  components: {
    ViewBox
  },
  data() {
    return {
      active: 0,
      tabList: [
        {id: "", type: "all", text: "全部"},
        {id: "001", type: "pay", text: "待支付"},
        {id: "002_1", type: "ensure", text: "保障中"},
        {id: "002_2", type: "stop", text: "已终止"}
      ],

      formList: [],
      condition: {
        dac002: "", // 投保人证件号
        daz001: "", // 投保申报ID
        tbzt00: "" // 投保状态 // 001待支付；002保障中；003已终止
      },
      dictData: { // 字典数据
        YES_NO: [], // 是否
        GZPT00: [], // 工作平台
        AAB301_XM: [] // 工作地区
      }
    }
  },
  watch: {
    active(val) {
      this.scrollToTop(0, 0, "auto") //滚动到顶部
      // 不同状态对应不同的formList
      this.condition.tbzt00 = val
      this.formList = []
      this.queryDa02ListByStatus()
    }
  },
  created() {
    this.init()  
  },
  methods: {
    init() {
      this.condition.dac002 = this.$sessionUtil.getItem("userInfo")?.zjhm00
      this.queryDa02ListByStatus()
      this.getDicData()
    },
    // 支付
    async handlePay(data) {
      // console.log(data, "****支付")
      const { payforURL, daz001 } = data || {}
      const checkedRes = await this.checkPolicyApply(daz001, "002") || {}
      const {map: {data: {policyNo}}, msg: message} = checkedRes
      // 订单已支付
      if (policyNo) {
        this.$dialog.alert({
          title: "提示",
          message,
          theme: "round-button",
          className: "ylb-dialog-alert"
        })
        return
      }

      location.href = payforURL
    },
    // 取消支付
    async handleCancel(orderInfo) {
      this.$dialog.confirm({
        title: "温馨提示",
        message: `您确定【取消订单】?`,
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        className: "ylb-dialog-confirm"
      }).then(async() => {
        const { daz001 } = orderInfo|| {}
        const checkedRes = await this.checkPolicyApply(daz001, "001") || {}
        const {map: {data: {policyNo}}, msg: message} = checkedRes
        // 订单已支付
        if (policyNo) {
          this.$dialog.alert({
            title: "提示",
            message,
            theme: "round-button",
            className: "ylb-dialog-alert"
          })
          return
        }
          
        this.cancelPolicyPayment(daz001)
      })
      
    },
    /**
     * @description: 
     * @param {*}
     * @return {*}
     * @author: T
     */    
    async checkPolicyApply(daz001, type) {
      const params = {
        type00: type, // "渠道类型 001取消订单 002支付订单"
        daz001,
        serviceName: "xytDa01Web_checkPolicyApply"
      }
      return await commonApi.proxyApi(params).then(res => {
        return res
      }).catch(() => false)
    },
    cancelPolicyPayment(daz001) {
      const params = {
        daz001,
        serviceName: "xytDa01Web_cancelPolicyPayment"
      }
      commonApi.proxyApi(params).then(res => {
        this.$toast(res?.msg)
        setTimeout(() => {
          this.init()
        }, 1000)
        
      }).catch(() => false)
    },
    queryDa02ListByStatus() {
      const params = {
        ...this.condition,
        serviceName: "xytDa02_queryDa02ListByStatus"
      }
      commonApi.proxyApi(params).then(res => {
        const { data } = res?.map

        // 展示订单状态
        const {tbzt00} = this.condition
        if (tbzt00) {
          data.forEach(item => {
            item.tbzt00 = tbzt00
          })
        }        
        this.formList = data
      })
    },
    /**
     * @description: 获取字典数据
     * @param {*}
     * @return {*}
     * @author: T
     */    
    async getDicData() {
      await commonApi.proxyApi({ 
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["YES_NO", "AAB301_XM", "GZPT00"]
      }).then(res => {
        const { data } = res?.map
        this.dictData = data
      })
    }
  }
}
</script>

<style lang="less" scoped>

</style>