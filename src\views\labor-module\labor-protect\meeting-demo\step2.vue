<!--
 * @Description: 
 * @Author: FBZ
 * @Date: 2024-05-23 17:33:04
 * @LastEditors: FBZ
 * @LastEditTime: 2024-05-24 09:24:09
-->
<template>
  <div>
    <van-button @click="closeMeeting">结束会议</van-button>
    <van-button @click="backStep1">第一步</van-button>
  </div>
</template>

<script>
export default {
  name: "step2",
  methods: {
    closeMeeting() {
      // 彻底关闭会议组件
      this.$store.dispatch("meeting/reset")
    },
    backStep1() {
      // 回到会议发起页
      this.$router.push("/meeting-demo-step1")
    }
  }
}
</script>

<style lang="less" scoped>
</style>
