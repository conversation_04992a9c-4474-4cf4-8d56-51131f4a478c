<!--
 * @Description: 
 * @Author: FBZ
 * @Date: 2024-05-23 11:23:54
 * @LastEditors: FBZ
 * @LastEditTime: 2024-05-24 10:13:57
-->
# YVideoConference 视频会议页面-浮点切换组件

### 介绍

`YVideoConference` 在唤起时默认全屏展示，通过点击左上按钮切换到浮点模式。

* 在`App.vue`中引入浮点组件，设置为跟`router-view`同级，即可保证组件内容不受路由跳转影响
* 通过`vuex`全局管理组件展示状态、记录浮点位置

### 操作步骤

* 使用`this.$store.dispatch("meeting/activeMeeting")`唤起组件
* 使用`this.$store.dispatch("meeting/reset")`关闭并重置浮点初始位置
