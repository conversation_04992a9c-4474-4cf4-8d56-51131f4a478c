<!--
 * @Description: 我的
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div>
    <y-title content="基本信息" :background-color="ylb_color" pleft="22" mBottom="0" fontWeight="bold"></y-title>
    <van-form class="base-form" disabled>
      <van-field
        v-model="formData.aac003"
        name="aac003"
        label="姓名"
      />
      <y-select-dict v-model="formData.ccg981" label="证件类型" disabled dict-type="CCG981" is-link />
      <van-field
        v-model="formData.aac002"
        name="aac002"
        label="证件号码"
      />
      <van-field
        v-model="formData.rygh00"
        name="rygh00"
        label="工号"
      />
      <van-field
        v-model="formData.aae005"
        name="aae005"
        label="手机号码"
      />        
      <van-field
        v-model="formData.aae006"
        name="aae006"
        label="电子邮箱"
      />  
      <van-field
        v-model="formData.yxqsrq"
        name="yxqsrq"
        label="有效起始日期"
      />  
      <van-field
        v-model="formData.yxjzrq"
        name="yxjzrq"
        label="有效截止日期"
      />  

      <van-field
        name="ljxse"
        label="累计销售额"
      >
        <div slot="extra" class="money-field"><span class="money-value">{{formData.ljxse}}</span>元</div>
      </van-field>

      <van-field
        name="ljds"
        label="累计单数"
      >
        <span slot="extra" class="number-field">{{formData.ljds}}单</span>
      </van-field>
    </van-form>    
  </div>
</template>

<script>
import { ylb_color } from "@/styles/theme/theme-params.less"

export default {
  name: "mine-page",
  data() {
    return {
      ylb_color,
      formData: {
        ccg981: "001"
      }
    }
  },
  created() {
    this.formData = this.$attrs.baseInfo
  }
}
</script>
<style lang="less" scoped>
/deep/.money-field {
  color: @third_text_color;
  .money-value {
    color: @maney_color;
  }
}
/deep/.number-field {
  color: @third_text_color;
}
</style>