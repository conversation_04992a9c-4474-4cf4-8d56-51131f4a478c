/*
 * @Description: 接口请求统一配置
 * @Version: 0.1
 * @Autor: Chenyt
 */

import axios from "axios"
import { getToken } from "@/utils/cookie"
import { formatReqData } from "@/utils/formatReqData"
import { addHttpLoading, removeHttpLoading } from "@/utils/http-loading"
import { decrypt } from "@/utils/sm-util" // 加载loading控制工具方法
import router from "@/router"
import { ZHRS_LOGIN_URL } from "@/assets/data/url-config"

// 环境变量
const API_BASEURL = process.env.BASE_URL//api接口基础路径
const CLIENT_TIMEOUT = process.env.VUE_APP_TIMEOUT || 8000//链接时间

const service = axios.create({
  baseURL: API_BASEURL,
  timeout: CLIENT_TIMEOUT,
  headers: {
    "Content-Type": "application/json"
  },
  transformRequest: [(data) => (formatReqData(data))] // 对 data入参进行转换处理
})

const alertList = ["xytDa05_checkDa05"] // serviceName

// 请求
service.interceptors.request.use(config => {
  const token = getToken()
  if (token) {
    config.headers["Access-Token"] = token
  }

  addHttpLoading(config.url) // http请求列队计数加一

  return config
}, error => {
  return Promise.reject(error)
})

//响应拦截
service.interceptors.response.use(
  response => {
    removeHttpLoading() // http请求列队计数减一

    const res = response.data
    if (res.encData) {
      res.data = JSON.parse(decrypt(res.encData))
    }
    const responseType = response.config.responseType
    const { custom } = JSON.parse(response.config.data)

    if (custom) { // 外部接口
      return res
    }

    if (responseType === "getAuth") { // 获取网关token接口
      return res.data
    }

    if (res.respCode === "P_0000") { // 统一能力输出接口
      if (res.data.appcode + "" === "0" || res.data.code + "" === "0") {
        return res.data
      } else {
        // parseToken接口报未授权 重新获取智慧人社token
        if (`${res.data.appcode}` === "1" && res.serviceName === "parseToken") {
          let url = ZHRS_LOGIN_URL
          const { path, fullPath } = router.history.current
          // 分销码益鹭保接口异常重新授权后依旧跳转到益鹭保首页
          if (path === "/yilubao") {
            url += path + fullPath
          }
          window.location.replace(url)
          return
        }
        // 彈窗提示/土司提示
        const { serviceName } = JSON.parse(response.config.data)
        if (alertList.includes(serviceName)) {
          window.gvm.$dialog.alert({
            title: "温馨提示",
            message: res.data.msg || "Error" || "Error",
            theme: "round-button",
            className: "ylb-dialog-alert",
            showConfirmButton: false,
            width: "78%"
          })
        } else {
          const { msg, message } = res.data
          const tipContent = msg || message
          window.gvm.$toast(tipContent || "Error")
        }
        return Promise.reject(new Error(res.data.msg || "Error"))
      }
    } else if (res.respCode === "G_0002") { //当前用户过期
      window.location.replace(ZHRS_LOGIN_URL)
    } else {
      // 正常请求接口
      if (res.appcode === "0" || res.data?.code + "" === "0") {
        return res?.map?.data || res
      }

      window.gvm.$toast(res.message || "Error")
      return Promise.reject(new Error(res.message || "Error"))
    }
  },
  (error) => {
    removeHttpLoading() // http请求列队计数减一
    return Promise.reject(error)
  }
)

export default service
