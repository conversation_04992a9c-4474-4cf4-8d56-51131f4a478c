<!--
 * @Description: 技能培训补贴--新增
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-supervision-handle common-container">
    <!-- 重要提示 -->
    <y-tips
      :tipsList="tipsList"
      v-if="pageType !== 'detail' && active === 1"
    ></y-tips>

    <!-- 步骤条 -->
    <y-society-steps
      v-if="pageType !== 'detail'"
      class="top-header"
      :handleList="handleList"
      :active="active"
    ></y-society-steps>

    <!-- 申请信息 -->
    <apply-info
      v-if="pageType !== 'detail'"
      ref="applyInfo"
      v-show="active === 0"      
      :baseFormData="formData"
      @handleNext="handleNext"
      @updateFormData="updateFormData"
    ></apply-info>

    <!-- 确认信息 -->
    <confirm-info
      ref="submitMaterial"
      v-show="active === 1 || pageType === 'detail'"
      :baseFormData="formData"
      @handleNext="handleNext"
      @handleSubmit="handleSubmit"
    ></confirm-info>    
    
  </div>
</template>

<script>
import ApplyInfo from "./cpns/apply-info"
import ConfirmInfo from "./cpns/confirm-info"

import { commonApi } from "@/api"

export default {
  name: "labor-supervision-handle",
  components: {
    ApplyInfo,
    ConfirmInfo
  },
  data() {
    return {
      tipsList: [
        "确认信息支持修改，允许修改的字段：联系电话、是否本市户籍、户籍地址、通讯地址。"
      ],

      active: 0,
      handleList: ["申请信息", "确认信息"],

      // 表单
      formData: {}
    }
  },
  watch: {
    active() {
      this.scrollToTop(0, 0, "auto")
    }
  },
  computed: {
    userInfo() {
      const { xm0000: aac003, zjhm00: aac002 } =
        this.$sessionUtil.getItem("userInfo")
      return { aac003, aac002 }
    },
    pageType() {
      return this.$route.query.pageType || ""
    }
  },
  created() {
    // 编辑和查看详情 查询已保存信息
    if (this.pageType === "add") {
      return
    }

    this.getDetail()
  },
  mounted() {
    if (window.history && window.history.pushState) {
      if (window.history.length > 1) {
        const state = {
          key: Math.random() * new Date().getTime()
        }
        window.history.pushState(state, null, document.URL)
      }

      //给window添加一个监听事件popstate，拦截返回键，并执行方法 backFn
      window.addEventListener("popstate", this.backFn, false)
    }
  },
  methods: {
    // 更新表单数据
    updateFormData(data) {
      this.formData = { ...this.formData, ...data }
    },

    // 拦截返回键
    backFn() {
      // 未完成提交 改变active状态
      if (this.active === 1) {
        this.active--
        const state = {
          key: Math.random() * new Date().getTime()
        }
        window.history.pushState(state, null, document.URL)
        return
      }

      // 第一步或提交成功状态 返回上一页面
      this.$router.go(-1)
    },

    // 下一步
    handleNext(active) {
      this.active = active
      this.scrollToTop(0, 0, "auto")

      this.formData = this.$refs.applyInfo.formData
    },

    // 提交
    async handleSubmit(formData) {
      //保存接口
      const {id=""} = this.$route.query
      const params = {        
        serviceName: this.pageType === "add" ? "xytjzbt_save" : "xytjzbt_update",
        ...formData,
        id
      }
      await commonApi.proxyApi(params, { timeout: 30 * 1000 })

      // 提交成功 提示
      this.$dialog
        .alert({
          title: "提示",
          message: "提交成功！",
          theme: "round-button"
        })
        .finally(() => {
          this.$router.go(-2)
        })
    },

    // 查看详情
    getDetail() {
      const { id } = this.$route.query
      const params = {
        serviceName: "xytjzbt_toDetail",
        id,
        ...this.userInfo
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "查看详情666")
        const { info = {} } = res.data?.[0] || {}
        this.formData = info
      })
    }
  },
  destroyed() {
    //销毁监听
    window.removeEventListener("popstate", this.backFn, false)
  }
}
</script>

<style lang="less" scoped>
.labor-supervision-handle {
  .business-cell .van-cell__value {
    color: @main_text_color;
  }
  .explain-box {
    .explain-button {
      width: 320px;
      height: 40px;
      background: #f8e8ea;
      border-radius: 20px;
      margin: 14px 0;
      & > img {
        width: 18px;
        height: 20px;
      }
      & > span {
        font-size: 14px;
        color: @main_color;
        line-height: 40px;
        margin-left: 12px;
        font-weight: bold;
      }
    }
  }
  .explain-popup {
    border-radius: 4px;
    width: 96vw;
    height: 95vh;
    .iframe-box {
      & > iframe {
        width: 96vw;
        height: calc(95vh - 40px);
        margin-bottom: 4px;
      }
      .van-button {
        padding: 0 8px;
      }
    }
  }
}
</style>
