<template>
  <div class="search-container">
    <div v-if="showVoice" class="voice-arc-header" :class="{ active: isRecording }"></div>
    <div class="search-box">
      <span v-if="!isRecording" class="icon-left">
        <template v-if="hasVirtualHuman">
          <img src="@pic/home/<USER>/back.png" @click="$emit('back')" />
        </template>
        <template v-if="!longVoice">
          <img v-if="!showVoice" src="@pic/home/<USER>/voice.png" @click="handleShowVoice" />
          <img v-else src="@pic/home/<USER>/keyboard.png" @click="showKeyboard" />
        </template>
      </span>
      <div
        v-if="longVoice"
        class="voice-recorder"
      >
        <div :class="isWillCancel ? 'voice-cancel' : 'voice-animation'">
          <VoiceAnimation />
        </div>
      </div>
      <template v-else>
        <template v-if="!showVoice">
          <van-field
            v-if="isStreaming || lastStatus || isPlaying"
            value="正在接收消息…"
            :border="false"
            :disabled="isStreaming || lastStatus || isPlaying"
          />
          <van-field
            v-else
            ref="searchField"
            v-model.trim="searchValue"
            :placeholder="placeholder"
            :border="false"
            @focus="placeholder = '发消息…'"
            @blur="placeholder = '发消息或按住说话…'"
            @keyup.enter="uploadProblem"
          />
        </template>
        <template v-else>
          <div v-if="isStreaming || lastStatus || isPlaying" class="voice-recorder">正在接收消息…</div>
          <div
            v-else
            @touchstart="startRecording"
            @touchmove="handleTouchMove"
            @touchend="stopRecording"
            @touchcancel="touchcancel"
            class="voice-recorder"
          >
            <div v-if="isRecording" :class="isWillCancel ? 'voice-cancel' : 'voice-animation'">
              <VoiceAnimation />
            </div>
            <template v-else>按住说话</template>
          </div>
        </template>
      </template>
      <span v-if="!isRecording" class="icon-right">
        <template v-if="longVoice">
          <img v-if="!forcePaused && (isStreaming || lastStatus || isPlaying)" src="@pic/home/<USER>/pause-circle-active.png" @click="stopCommunication" />
          <img v-else src="@pic/home/<USER>/keyboard.png" @click="showshortVoiceOrkeyBoard" />
        </template>
        <template v-else>
          <img v-if="!forcePaused && (isStreaming || lastStatus || isPlaying)" src="@pic/home/<USER>/pause-circle-active.png" @click="stopCommunication" />
          <template v-else>
            <template v-if="searchValue && !showVoice">
              <img src="@pic/home/<USER>/sending.png" @click="uploadProblem" />
            </template>
            <template v-else>
              <img :src="switchMode ? require('@pic/home/<USER>/switch.png') : require('@pic/home/<USER>/sound-wave.png')" @click="$emit('toNewDigitalHuman')" />
            </template>
          </template>
        </template>
      </span>
    </div>
    <div v-if="showVoice && isRecording" class="voice-prompt">
      <div>{{ isWillCancel ? "松手取消" : "松手发送，上移取消" }}</div>
    </div>
  </div>
</template>

<script>
import { streamChatRequest } from "@/utils/stream-chat"
import { getYlzinsToken, getEncryptEnabled } from "@/utils/cookie"
import VoiceAnimation from "@/views/home-modules/components/voice-animation/index.vue"
import xunfei from "@/utils/xfyun/xunfei"
import MarkdownIt from "markdown-it"
const md = new MarkdownIt({
  html: true,
  linkify: true,
  xhtmlOut: true,
  typographer: true,
  breaks: true
})
import { encryptApi } from "@/api"

export default {
  components: { VoiceAnimation },
  props: {
    messageBoxRef: {
      type: Object,
      default: () => null
    },
    virtualHuman: {
      type: Boolean,
      default: false
    },
    hasVirtualHuman: {
      type: Boolean,
      default: false
    },
    longVoice: {
      type: Boolean,
      default: false
    },
    messages: {
      type: Array,
      default: () => []
    },
    isPlaying: {
      type: Boolean,
      default: false
    },
    forcePaused: {
      type: Boolean,
      default: false
    },
    switchMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isStreaming: false,
      lastStatus: false,
      tempAssistantIndex: -1,
      searchValue: "",
      placeholder: "发消息或按住说话…",
      showVoice: false,
      message_id: Date.now(),
      isRecording: false,
      url: "https://app.hrss.xm.gov.cn/ylzins-modelengine",
      checkInterval: null,
      startTime: 0,
      isStop: false,
      isWillCancel: false
    }
  },
  created() {
    this.isRecording = false
    this.lastStatus = false
    this.isStop = false
    this.showVoice = false
    xunfei?.releaseMicrophone()
  },
  beforeDestroy() {
    xunfei?.releaseMicrophone()
  },
  activated() {
    document.addEventListener("click", this.lostFocusAndHideKeyboard)
    document.addEventListener("touchend", this.lostFocusAndHideKeyboard)
  },
  deactivated() {
    document.removeEventListener("click", this.lostFocusAndHideKeyboard)
    document.removeEventListener("touchend", this.lostFocusAndHideKeyboard)
  },
  watch: {
    isRecording: {
      handler(nval, oval) { 
        if (nval && !oval) {
          this.clearAllInterval()
        }
        if (!nval) {
          xunfei?.releaseMicrophone()
        }
      }
    },
    isStreaming: {
      handler(nval) {
        this.$emit("update:isStreaming", nval)
      }
    },
    lastStatus: {
      handler(nval) {
        this.$emit("update:lastStatus", nval)
      }
    }
  },
  methods: {
    async getXunFeiData() {
      if (!this.isRecording) {
        return
      }
      this.isRecording = false
      const endTime = Date.now()
      if (endTime - this.startTime <= 500) {
        this.$toast.fail("说话时间太短")
        return
      }
      this.isStreaming = true
      xunfei.setMediaRecorder()
      try {
        this.checkInterval = setInterval(() => {
          if (xunfei.getIsEnd()) {
            const result = xunfei.getUserInputMsg()
            this.searchValue = result
            if (!this.searchValue) {
              this.$toast.fail("未识别到语音，请重试")
            }
            this.uploadProblem()
            this.clearAllInterval()
          }
        }, 300)
      } catch (err) {
        this.$toast.fail("语音识别失败，请重试")
      } finally {
        this.isStreaming = false
        xunfei?.releaseMicrophone()
      }
    },
    startRecording() {
      if (this.isRecording) {
        return
      }
      this.startTime = Date.now()
      this.isRecording = true
      xunfei.start()
    },
    stopRecording() {
      if (this.isWillCancel) {
        // 取消录音
        this.isRecording = false
        this.isWillCancel = false
        xunfei?.releaseMicrophone()
        this.clearAllInterval()
        return
      }
      this.getXunFeiData()
      this.isWillCancel = false
    },
    touchcancel() {
      this.isRecording = false
      this.isWillCancel = false
      xunfei?.releaseMicrophone()
      this.clearAllInterval()
    },
    handleTouchMove(e) {
      // 手指滑动出按钮区域时取消录音
      const buttonRect = e.currentTarget.getBoundingClientRect()
      const touch = e.touches[0]
      const x = touch.clientX
      const y = touch.clientY

      if (
        x < buttonRect.left ||
        x > buttonRect.right ||
        y < buttonRect.top ||
        y > buttonRect.bottom
      ) {
        if (!this.isWillCancel) {
          this.isWillCancel = true
        }
      } else {
        if (this.isWillCancel) {
          this.isWillCancel = false
        }
      }
    },
    getDialogueList() {
      if (this.isStreaming || this.lastStatus) {
        return
      }
      this.isStop = false
      const userInfo = this.$sessionUtil.getItem("userInfo")
      const session_id = this.$sessionUtil.getItem(process.env.VUE_APP_ZHRS_TOKEN)

      // 保存当前输入的消息内容
      const currentMessage = this.searchValue
      this.messages.push({
        role: "user",
        content: currentMessage
      })
      this.$emit("sendMessage", this.messages)
      this.searchValue = ""
      this.clearAllInterval()

      const param = {
        model: "ylzins-model",
        messages: JSON.parse(JSON.stringify(this.messages.filter(m => m.content))),
        message_id: String(this.message_id),
        session_id,
        user_id: userInfo?.zjhm00,
        max_tokens: 4000,
        stream: true,
        dialogueCustom: true
      }
      
      this.isStreaming = true
      this.lastStatus = true
      this.tempAssistantIndex = this.messages.length

      // 创建本轮请求专用的状态对象
      const typingState = {
        charQueue: [],
        isTyping: false,
        currentContent: ""
      }

      this.messages.push({
        role: "assistant",
        content: ""
      })
      this.$emit("guessQuestions", [])
      this.$emit("sendMessage", this.messages)
      const container = this.messageBoxRef && this.messageBoxRef.$refs.dialogueContainer
      const isAtBottom = container && container.scrollHeight - container.scrollTop <= container.clientHeight + 10
      if (isAtBottom) {
        this.scrollToBottom()
      }

      if (this.virtualHuman) {
        this.$emit("getVoiceData", currentMessage)
        return
      }

      streamChatRequest(
        `${this.url}/api/v1/chat/completions`,
        param,
        (content, index) => {
          if (!this.messages[index].stop && !this.isStop && content) {
            // 将新内容拆分为字符队列
            typingState.charQueue = typingState.charQueue.concat(content.split(""))
            // 启动打字机逻辑
            if (!typingState.isTyping) {
              this.startTyping(typingState, index)
            }
          }
        },
        () => {
          this.isStreaming = false
        },
        (err) => {
          this.isStreaming = false
          this.lastStatus = false
          this.messages[err.index].content = `${err.message}`
          this.$emit("sendMessage", this.messages)
        },
        this.tempAssistantIndex,
        {
          "ylzins-token": getYlzinsToken(),
          "Authorization": "Bearer " + process.env.VUE_APP_AUTHORIZATION
        }
      )
    },
    startTyping(typingState, index) {
      typingState.isTyping = true

      const interval = setInterval(() => {
        const container = this.messageBoxRef && this.messageBoxRef.$refs.dialogueContainer
        // 判断是否已经处于底部（允许一定的误差）
        const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 10

        this.lastStatus = true
        if (typingState.charQueue.length === 0 || this.messages[index].stop) {
          clearInterval(interval)
          typingState.isTyping = false
          this.lastStatus = false
          if (!(this.isStreaming || this.lastStatus)) {
            this.guessQuestion(this.messages[index].content)
          }
          return
        }

        const char = typingState.charQueue.shift()
        typingState.currentContent += char

        const regex = /\[\[(.*?)]]/g
        const replacedText = typingState.currentContent.replace(regex, (_, p1) => {
          return `<a style="color: #0194FF;" href="${p1}">一键去办理</a>`
        })

        this.$set(this.messages, index, {
          role: "assistant",
          content: md.render(replacedText)
        })
        this.$emit("sendMessage", this.messages)
        if (isAtBottom) {
          this.scrollToBottom()
        }
      }, 50)
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.messageBoxRef && this.messageBoxRef.$refs.dialogueContainer
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: "smooth"
          })
        }
      })
    },
    showshortVoiceOrkeyBoard() {
      this.$emit("changeLongVoice", false)
      this.showKeyboard()
    },
    showKeyboard() {
      this.showVoice = false
    },
    uploadProblem() {
      if (!this.searchValue) {
        return
      }
      this.getDialogueList()
    },
    handleShowVoice() {
      this.showVoice = true
    },
    stopCommunication() {
      // 普通模式下的停止逻辑
      this.isStreaming = false
      this.lastStatus = false
      this.isStop = true // 当前对话停止
      if (this.messages[this.tempAssistantIndex]) {
        this.messages[this.tempAssistantIndex].stop = true // 单条对话暂停，显示暂停文本
      }
      this.$emit("sendMessage", this.messages)
      if (this.virtualHuman) {
        // 在虚拟人模式下，发送停止事件给父组件
        this.$emit("stopAnswer")
      }
    },
    clearAllInterval() {
      if (this.checkInterval) {
        clearInterval(this.checkInterval)
        this.checkInterval = null
      }
    },
    guessQuestion(currentMessage) {
      const container = this.messageBoxRef && this.messageBoxRef.$refs.dialogueContainer
      const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 10
      encryptApi.guessQuestion({ prompt: currentMessage }, { encryptEnabled: getEncryptEnabled() }).then(res => {
        if (res?.data?.errorCode == 0) {
          this.$emit("guessQuestions", res.data.data)
          if (isAtBottom) {
            this.$nextTick(() => {
              this.scrollToBottom()
            })
          }
        }
      })
    },
    lostFocusAndHideKeyboard(event) {
      // 获取 van-field 里的原生 input
      const fieldComponent = this.$refs.searchField
      const inputElement = fieldComponent && fieldComponent.$el
        ? fieldComponent.$el.querySelector("input")
        : null
      // 如果点击的目标不是 input 元素本身
      if (inputElement && event.target !== inputElement) {
        if (document.activeElement === inputElement) {
          // 隐藏软键盘：通过 blur 失焦实现
          inputElement.blur()
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  padding: 10px;
  position: fixed;
  bottom: 0px;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  background-color: #caecf6;
  overflow: visible;
  .search-box {
    width: 100%;
    height: 60px;
    border-radius: 15px;
    position: relative;
    display: flex;
    align-items: center;
    .icon-left,
    .icon-right {
      display: flex;
      align-items: center;
      img {
        height: 44px;
      }
    }
    ::v-deep .van-field__control {
      caret-color: #0194FF;
    }
    ::v-deep .van-cell {
      border-radius: 10px;
    }
  }
}

.voice-recorder {
  width: 80%;
  height: 44px;
  margin: 0 auto;
  border-radius: 22px;
  position: relative;
  overflow: hidden;
  background: #fbfbfb;
  box-shadow: 0px 2.55px 5.95px 0px rgba(32,56,73,0.10), 0px 10.48px 10.48px 0px rgba(32,56,73,0.09), 0px 23.8px 14.45px 0px rgba(32,56,73,0.05); 
  color: #6D848F;
  font-size: 16px;
  letter-spacing: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-user-select: none;
  user-select: none;
  font-size: 14px;
  .voice-animation,
  .voice-cancel {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .voice-animation {
    background-color: #0194FF;
  }
  .voice-cancel {
    background: #F32F5D;
  }
}

.voice-prompt {
  position: absolute;
  bottom: 70px;
  width: calc(100% - 20px);
  text-align: center;
  z-index: 5;
  div {
    display: inline-block;
    color: #6D848F;
    line-height: 1;
  }
}

.voice-arc-header {
  position: absolute;
  left: 0;
  bottom: calc(100% - 20px);
  width: 100%;
  height: 0;
  background: #caecf6;
  clip-path: ellipse(60% 100% at 50% 100%);
  z-index: 2;
  pointer-events: none;
  transition: height 0.4s cubic-bezier(0.4,0,0.2,1);
}
.voice-arc-header.active {
  height: 35px;
}

@keyframes pulse {
  0% { height: 5px; }
  25% { height: 10px; }
  50% { height: 30px; }
  75% { height: 10px; }
  100% { height: 5px; }
}
</style>