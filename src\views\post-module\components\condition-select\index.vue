<!--
 * @Description: 条件筛选
 * @Version: 0.1
 * @Author: T
-->
<template>
  <div class="selection-wrapper">
    <span @click="show=true">{{ data.title }}</span>
    <van-action-sheet v-model="show" title="筛选" :round="false" :lock-scroll="true">
      <MoreTypesSelection v-if="data.type === 'tree'"/>
      <template v-else>
        <div v-for="(item, index) in data.list" :key="index" class="container">
          <div class="title">{{ item.title }}</div>
          <CommonSelection ref="selection" @onActiveList="onActiveList" :list="item.list" :type="item.type"></CommonSelection>
        </div>
      </template>
      <div class="footer-container">
        <div class="footer">
          <van-button size="small" round @click="handleReset">重置</van-button>
          <van-button size="small" round type="info" @click="handleSubmit()">确定</van-button>
        </div>
      </div>
    </van-action-sheet>
  </div>
</template>
<script>
import CommonSelection from "./common-selection.vue"
import MoreTypesSelection from "./more-types-selection.vue"

export default {
  components: { CommonSelection, MoreTypesSelection },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      dataInfo: {},
      type: ""
    }
  },
  methods: {
    onActiveList(list, type) {
      this.dataInfo = {
        ...this.dataInfo,
        [type]: list
      }
    },
    handleReset() {
      this.dataInfo = {}
      const refList = this.$refs.selection
      refList?.map(item => item.onReset())
    },
    handleSubmit() {
      this.$emit("onActiveList", this.dataInfo)
      this.show = false
    }
  }
}
</script>

<style lang="less" scoped>
.selection-wrapper {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  position: relative;
  top: 0;
  left: 0;
  padding: 5px 16px 5px 5px;
  user-select: none; /* 禁止文本被选中 */
  &::after {
    content: " ";
    position: absolute;
    top: 50%;
    right: 0;
    border-top: 5px solid #C7C7C7;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid transparent;
  }
  .footer-container{
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    .van-divider {
      margin-top: 0;
      margin-bottom: 8px;
    }
    .footer{
      display: flex;
      align-items: center;
      padding-bottom: 8px;
      .van-button{
        width: 160px;
        height: 40px;
        margin: auto;
      }
    }
  }
}
::v-deep.van-popup.van-action-sheet {
  height: 100%;
  max-height: 100%;
  padding-bottom: 40px;
  .van-action-sheet__header {
    padding: 16px;
  }
  .van-action-sheet__close {
    margin-top: 16px;
    &::before {
      font-size: 16px;
    }
  }
  .container {
    // height: 100%;
    .title {
      padding-left: 16px;
      font-size: 16px;
      font-weight: 600;
      
    }
  }
}
.van-popup--bottom {
  border-radius: 0;
}
</style>