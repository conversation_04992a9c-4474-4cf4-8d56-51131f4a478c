<!--
 * @Description: 工作台-销售数据分析
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <div class="card-box ylb-container">
      <div class="card-title">销售数据分析</div>

      <div class="content-box flex-c-c-c">
        <van-tabs v-model="active">
          <van-tab v-for="(item, key) in tabList" :title="item.text" :key="key">
          </van-tab>
        </van-tabs>

        <div class="data-list flex-c-sb">
          <div class="data-item">
            <div class="item-top flex-c-sb">
              <span>销售金额</span>
              <span class="percent">{{dataInfo.xszb}}</span>
            </div>
            <div class="item-bottom">
              <span class="money">{{dataInfo.xsje}}</span>
              <span class="money-unit">元</span>
            </div>
          </div>

          <div class="data-item">
            <div class="item-top flex-c-sb">
              <span>订单数量</span>
              <span class="percent">{{dataInfo.ddzb}}</span>
            </div>
            <div class="item-bottom">
              <span class="money">{{dataInfo.dds}}</span>
              <span class="money-unit">笔</span>
            </div>
          </div>

          <div class="data-item">
            <div class="item-top flex-c-sb">
              <span>在保人数</span>
            </div>
            <div class="item-bottom">
              <span class="money">{{dataInfo.zbrs}}</span>
              <span class="money-unit">人</span>
            </div>
          </div>

          <div class="data-item">
            <div class="item-top flex-c-sb">
              <span>到期人数</span>
            </div>
            <div class="item-bottom">
              <span class="money">{{dataInfo.dqrs}}</span>
              <span class="money-unit">人</span>
            </div>
          </div>
        </div>

      </div>      
    </div>
  </div>
</template>

<script>
import {commonApi} from "@/api"

export default {
  data() {
    return {
      active: 0,
      tabList: [
        {type: "day", text: "日报"},
        {type: "week", text: "周报"},
        {type: "month", text: "月报"}
      ],

      dayData: {},
      weekData: {},
      monthData: {}
    }
  },
  computed: {
    dataInfo() {
      const mapData = {
        0: "dayData",
        1: "weekData",
        2: "monthData"
      }
      const data = this[mapData[this.active]]
      return data
    }
  },
  created() {
    this.$bus.$on("getAction", this.onAction)
    this.$once("hook:beforeDestroy", () => {
      this.$bus.$off("getAction")
    })
  },
  methods: {
    onAction(data) {
      const {daz005} = data
      const fetchMap = {
        "dayData": "xytPerson_getSalesDailyByDaz005",
        "weekData": "xytPerson_getSalesWeeklyByDaz005",
        "monthData": "xytPerson_getSalesMonthlyByDaz005"
      }
      for (const key in fetchMap) {
        this.fetchData({daz005, serviceName: fetchMap[key], dataName: key})
      }
    },
    fetchData(data) {
      const {daz005, serviceName, dataName} = data
      const params = {
        serviceName,
        daz005
      }
      commonApi.proxyApi(params).then((res) => {
        this[dataName] = res.map.data || {}
      })
    }
  }
}
</script>
<style lang="less" scoped>
.card-box {
  .content-box {
    padding: 26px 0 0;
    /deep/ .van-tabs {
      width: 190px;
    }
    .data-list {   
      margin-top: 14px;   
      .data-item {
        width: 150px;
        height: 76px;
        background: #F8F9FA;
        border-radius: 8px;
        margin-bottom: 12px;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        padding: 10px;
        .item-top .percent {
          font-weight: bold;
          font-size: 14px;
          color: #2CD8B8;
          line-height: 20px;          
        }
        .item-bottom {
          margin-top: 6px;          
          .money {
            font-weight: bold;
            font-size: 20px;  
            line-height: 28px;          
          }
          .money-unit {
            margin-left: 6px;
            line-height: 28px;
          }
        }
      }
    }
  }
}
</style>