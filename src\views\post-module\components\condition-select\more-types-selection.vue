<!--
 * @Description: 多选模式
 * @Version: 0.1
 * @Author: T
-->
<template>
  <div class="tabs-wrapper">
    <van-search
      v-model="input"
      show-action
      shape="round"
      placeholder="请输入搜索关键词"
      @search="onSearch"
    >
      <template #action>
        <div class="search-btn" @click="onSearch" >搜索</div>
      </template>
    </van-search>
    <div class="container-box">
      <div class="aside">
        <div class="list">
          <div v-for="(item, index) in treeList" :key="`tab-${index}`" class="tab-box">
            {{ item.title }}
          </div>
        </div>
      </div>
      <div class="content">
        <div v-for="(item, index) in treeList[this.activeIndex].industryList" :key="`box_${index}`" class="content-box"  >
          <div class="title">{{ item.type }}</div>
          <div v-for="(subItem, subIndex) in item.list" :key="`${subItem.value}-${subIndex}`" :class="{mr0: (subIndex+1)%3===0, 'item-box-active': itemActive(subItem)}"  @click="handleClick(subItem)" class="item-box" >
            {{ subItem.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      input: "",
      activeIndex: 0,
      selectedRecords: {},
      treeList: [
        {
          title: "生产制造",
          industryList: [
            {
              type: "aaa",
              list: [
                {
                  label: "999",
                  value: "999"
                },
                {
                  label: "999",
                  value: "999"
                },
                {
                  label: "999",
                  value: "999"
                }
              ]
            }
          ]
        }
      ]
    }
  },
  computed: {
    itemActive() {
      return (info) => {
        const { value } = info || {}
        return value && this.selectedRecords[this.activeIndex]?.find(item => item.value === value)
      }
    }
  },
  methods: {
    onSearch() {

    },
    handleClick(itemInfo) {
      const selectedRecords = {...this.selectedRecords}
      const { value } = itemInfo
      const selectedIndex = selectedRecords[this.activeIndex]?.findIndex(item => item.value === value)
      console.log(selectedIndex, "selectedIndex")
      if (selectedIndex === undefined) {
        selectedRecords[this.activeIndex] = []
      }
    
      if (selectedIndex !== -1 && selectedIndex !== undefined) {
        selectedRecords[this.activeIndex].splice(selectedIndex, 1)
        selectedRecords[this.activeIndex] = [...selectedRecords[this.activeIndex]]
      } else {
        selectedRecords[this.activeIndex] = [...selectedRecords[this.activeIndex], itemInfo]
        this.selectedRecords = selectedRecords
      }
      console.log(selectedRecords, "selectedRecords")
      this.$emit("onActiveList", this.selectedRecords, this.type)
    }
  }
}
</script>

<style lang="less" scoped>
.mr0 {
  margin-right: 0!important;
}
.tabs-wrapper {
  .container-box {
    display: flex;
    flex-direction: row;
    .aside {
      width: 80px;
      border-right: 1px solid #000;
    }
    .content {
      flex: 1;
      height: calc(100vh - 120px);
      padding: 8px;
      .item-box {
        width: calc((100% - 2*8px)/3);
        border-radius: 2px;
        float: left;
        background: #F6F6F6;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        text-align: left;
        font-style: normal;
        text-align: center;
        margin-right: 8px;
        margin-bottom: 12px;
        padding-top: 8px;
        padding-bottom: 8px;
        border: 1px solid transparent;
        user-select: none; /* 禁止文本被选中 */
        // &:nth-child(3n) {
        //   margin-right: 0;
        // }
        &-active {
          border-color: @dark_blue_color;
          color: @dark_blue_color;
          background-color: #fff;
        }

      }

    }
  }
}

</style>