<!--
 * @Description: 入口页面
 * @Version: 0.1
 * @Autor: Chenyt
-->

<template>
  <div id="app">
    <!-- 页面切换容器 -->
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive" />
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" />
    <YVideoConference v-if="showVideoConference" />
  </div>
</template>

<script>
import { mapGetters } from "vuex"

export default {
  name: "app",
  computed: {
    ...mapGetters(["videoConferenceActive"]),
    showVideoConference() {
      return this.videoConferenceActive
    }
  },
  created() {    
    this.$store.dispatch("meeting/reset")
  }
}
</script>

<style lang="less">
#app {
  background: #fff;
  height: 100%;
}
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  .change-older {
    position: fixed;
    right: 24px;
    top: 24px;
    z-index: 99;
  }
  .y-float-ball {
    border: 1px solid @second_border_color;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  }
}
</style>
