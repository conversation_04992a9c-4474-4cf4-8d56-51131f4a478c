/******* 
 * @Description: 投保信息
 * @Version: 0.1
 * @Autor: T
 */
export default {
  namespaced: true,
  state: {
    personList: [], // 人员列表
    form: null// 基础表单信息
  },

  mutations: {
    UPDATE_FORM(state, form) {
      state.form = form
    },
    UPDATE_PERSONLIST(state, person) {
      const personList = [...state.personList]
      personList.push(person)
      state.personList = personList
    }
  },

  actions: {
    updateForm({ commit }, form) {
      commit("UPDATE_FORM", form)
    },
    addPerson({ commit }, person) {
      commit("UPDATE_PERSONLIST", person)
    }
  }
}
