<!--
 * @Description: 资讯详情
 * @Author: wujh
 * @date: 2024/1/31 17:21
 * @LastEditors: Please set LastEditors
-->
<template>
  <div class="detail-container">
    <div v-if="showType === '0'" class="container">
      <div class="content-title">
        <div class="title-content">{{ pageData.title }}</div>
        <p class="source-content">
          <span class="source">{{ pageData.source }}</span>
          <span class="date">{{ dayFormatFn(pageData.publishDate, "date") }}</span>
        </p>
      </div>

      <div v-if="videoSrc" class="iframe-box">        
        <iframe 
          :src="videoSrc" 
          frameborder="0"
          allowfullscreen
          width="100%"
          height="auto"
          style="aspect-ratio: 16/9;"
          webkit-playsinline
          playsinline>
        </iframe>
      </div>
      <div v-else v-html="pageData.contentHtml" class="content-body"/>
    </div>
    
    <typical-case-detail v-else :bcz007="$route.query.bcz007" ></typical-case-detail>
    
  </div>
</template>

<script>
import {commonApi} from "@api"
import TypicalCaseDetail from "./components/typical-case-detail.vue"

export default {
  name: "information-center-detail",
  components: { TypicalCaseDetail },
  data(){
    return {
      showType: "0", //展示类型 0富文本展示 1查询字段展示
      pageData: {},
      videoSrc: ""
    }
  },
  mounted() {
    const { showType="0", catalogId, contentId, contentType } = this.$route.query
    this.showType = showType
    if (showType === "0") {
      this.queryDetailFn({ catalogId, contentId, contentType }) // 查询内容
    } else { //查询典型案例详情       
      this.getTypicalCaseDetail()
    }    
  },
  methods: {
    // 查询内容
    queryDetailFn(queryInfo){
      commonApi.proxyApi({
        serviceName: "getContentDetail",
        ...queryInfo
      }).then((res) => {
        this.pageData = res.data
        const {contentType, videoList=[]} = this.pageData
        if (contentType === "video") {
          this.videoSrc = videoList[0].src
        }
      })
    },
    //查询典型案例详情
    getTypicalCaseDetail(){
      commonApi.proxyApi({
        serviceName: "xytDxal_getBc07ById",
        bcz007: this.$route.query.bcz007
      }).then((res) => {
        this.pageData= res?.map?.data || {}
      })
    }
  }
}
</script>

<style scoped lang="less">
.detail-container{
  height: 100vh;  
  //border: 1px solid #ccc;
  .container{
    height: 100%;
    min-height: 480px;
    padding: 20px;
    .content-title {
      .title-content {
        font-size: 18px;
        color: #303133;
        text-align: left;
      }

      .source-content{
        display: flex;
        flex-flow: row;
        justify-content: space-between;
        font-size: 16px;
        font-weight: normal;
        .source{
          color: rgb(87, 107, 149);
        }
        .date{
          color: rgba(0, 0, 0, 0.3);
        }
      }
      .title-date {
        padding-top: 10px;
        text-align: center;
        font-size: 16px;
        color: #787b80;
      }
    }
    .iframe-box {
      width: 100%;
      height: 50%;
      margin-top: 14px;
      iframe {
        width: 100%;
        height: auto;
      }
    }
    .content-body {
      min-height: 295px;
      padding: 12px 15px;
      line-height: 1.42;
      outline: none;
      font-size: 14px;
      ::v-deep .ql-toolbar {
        display: none;
        border: none;
      }
      ::v-deep .ql-container {
        border: none;
      }
    }
  }

  ::v-deep .art-body-img{
    max-width: 100%;
    object-fit: contain;
  }
}
</style>