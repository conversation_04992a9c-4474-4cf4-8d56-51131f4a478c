<!--
 * @Description: 劳动维权--新增
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-protect-detail">
    <div class="info-container">
      <div class="business-container">
        <div class="business-box flex-c-sb mt10 pl20 pr20">
          <div class="business-item flex-c-c-c" v-for="(item, index) in businessList" :key="index"
            @click="handleJump(item)">
            <div class="imgdiv">
              <img :src="item.imgUrl" alt="">
              <span class="badge" v-if="item.number">{{ item.number }}</span>
            </div>
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>
      <div class="business-box business-img">
        <van-image width="309" height="calc(100vh - 210px)" :src="require('@pic/labor-protect/process.jpg')" />
      </div>
    </div>
  </div>
</template>

<script>

import {
  main_color
} from "@/styles/theme/theme-params.less"

export default {
  name: "labor-protect-handle",
  data() {
    return {
      active: 0,
      colorMore: main_color,
      businessList: [
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "调解指南",
          href: "/labor-protect-guide",
          isOpen: true
        },
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "留言",
          href: "/labor-protect-message",
          isOpen: true
        },
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "典型案例",
          href: "/labor-protect-typical",
          isOpen: true
        }
      ]

    }
  },
  watch: {
    active() {
      this.scrollToTop(0, 0, "auto")
    }
  },
  computed: {
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    },
    pageType() {
      return this.$route.query.pageType || ""
    },
    primaryKey() {
      return this.$route.query.bcz001 || ""
    },
    materialId() {
      console.log(this.formData.abz200, "this.formData.abz200")
      return this.formData.abz200 || ""
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    handleJump(item) {
      const { isOpen, href } = item
      if (!isOpen) {
        this.$toast("功能建设中,敬请期待！")
        return
      }
      this.$router.push(href)
    }
  },
  destroyed() {
    //销毁监听
    window.removeEventListener("popstate", this.backFn, false)
  }
}
</script>

<style lang="less" scoped>
.info-box {
  margin-bottom: 16px;
  padding: 8px 8px 8px 8px;
  background: @white_text_color;

  /deep/.van-cell-group {
    .van-cell {
      padding: 0;

      .van-cell__title {
        color: @six_text_color;
      }

      .van-cell__value {
        color: @main_text_color;
      }
    }
  }

  /deep/.y-title {
    border-bottom: 0.5px solid #EEEEEE !important;

    .content {
      position: relative;
    }

    .right-box {
      position: absolute;
      right: 0;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;

      &>.point {
        width: 8px;
        height: 8px;
        border-radius: 4px;
        display: inline-block;
        margin-bottom: 1px;
      }

      &>.text {
        margin-left: 6px;
      }
    }
  }
}

.labor-protect-detail {
  .info-container {
    padding: 24px 16px;

    .business-container {
      background-color: @white_bg_color;
      padding: 8px 18px 12px;
      border-radius: 4px;
      margin-bottom: 16px;

      .business-box .business-item {
        .imgdiv {
          position: relative;

          &>img {
            width: 44px;
          }
        }

        .item-title {
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 14px;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 20px;
          margin: 4px 7px 0 9px
        }
      }
    }
  }
}

.step-text {
  overflow-wrap: break-word;
  color: rgba(48, 49, 51, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.step-time {
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 8px 56px 0 0;
}

/deep/ [class*='van-hairline']::after {
  border: none;
}

.business-img {
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 11px 17px 18px 17px;
}
</style>
