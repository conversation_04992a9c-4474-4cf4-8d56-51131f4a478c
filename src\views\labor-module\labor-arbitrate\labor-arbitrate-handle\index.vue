<!--
 * @Description: 劳动仲裁--新增
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-arbitrate-handle common-container">
    <apply-people ref="applyPeople" :organId="organId" :propList="bc05SqrList"></apply-people>
    <applied-people ref="appliedPeople" :organId="organId" :propList="bc05BsqrList"></applied-people>
    <arbitrate ref="arbitrate" :organId="organId" :propList="bc05QqmsList"></arbitrate>

    <van-form class="reason-form" :disabled="pageType === 'details'">
      <van-field class="van-field-textarea" v-model="formData.abb015" name="abb015" label="事实理由"
        placeholder="请输入理由，字数500字以内" type="textarea" :required="true" :rules="formRules.abb015" maxlength="500"
        show-word-limit>
      </van-field>
    </van-form>

    <evidence-list ref="evidenceList" :organId="organId" :propList="bc05ZjqdList"></evidence-list>

    <div class="button-box-more" v-if="pageType !== 'details'">
      <van-button round block type="primary" native-type="button" @click="handleSave">
        保存
      </van-button>
    </div>
  </div>
</template>

<script>
import ApplyPeople from "./cpns/apply-people"
import AppliedPeople from "./cpns/applied-people"
import Arbitrate from "./cpns/arbitrate"
import EvidenceList from "./cpns/evidence-list"

import { commonApi } from "@/api"

export default {
  name: "labor-arbitrate-handle",
  components: {
    ApplyPeople,
    AppliedPeople,
    Arbitrate,
    EvidenceList
  },
  data() {
    return {
      // 表单
      formData: {
        abb015: ""
      },
      formRules: {},

      bc05SqrList: [],
      bc05BsqrList: [],
      bc05ZjqdList: [],
      bc05QqmsList: [],
      otherFormData: {}
    }
  },
  computed: {
    pageType() {
      return this.$route.query.pageType
    },
    organId() { //仲裁委员会
      const { organId } = this.$route.query
      return organId
    },
    bcz005() {
      const { bcz005 = "" } = this.$route.query
      return bcz005
    }
  },
  created() {
    if (this.pageType !== "add") {
      this.getBc05ById() // 查询详情
    } else {
      // 接收并处理传递过来的formData
      const { formData } = this.$route.query
      if (formData) {
        try {
          const parsedFormData = JSON.parse(formData)
          this.otherFormData = parsedFormData
          // 将数据传递给对应的组件
          this.setFormDataToComponents(parsedFormData)
        } catch (error) {
          console.error("解析formData失败:", error)
        }
      }
    }
  },
  methods: {
    // 查询详情
    getBc05ById() {
      const { bcz005 } = this
      commonApi.proxyApi({
        serviceName: "xytBc05_getBc05ById",
        bcz005
      }).then((res) => {
        console.log("查询详情 全部", res)
        const { bc05SqrList, bc05BsqrList, bc05ZjqdList, bc05QqmsList, abb015 } = res.map.data
        this.bc05SqrList = bc05SqrList || []
        this.bc05BsqrList = bc05BsqrList || []
        this.bc05ZjqdList = bc05ZjqdList || []
        this.bc05QqmsList = bc05QqmsList || []
        this.formData.abb015 = abb015 || ""
        console.log(this.bc05BsqrList, "this.bc05BsqrList")
      }).catch((err) => {
        console.error(err)
      })
    },
    // 保存
    async handleSave() {
      const applyPeopleList = this.$refs.applyPeople.list
      const appliedPeopleList = this.$refs.appliedPeople.list
      const evidenceList = this.$refs.evidenceList.list
      const arbitrateList = this.$refs.arbitrate.list
      const list = [
        {
          tips: "预申请人",
          list: applyPeopleList
        },
        {
          tips: "被申请人",
          list: appliedPeopleList
        },
        {
          tips: "证据清单",
          list: evidenceList
        },
        {
          tips: "仲裁诉求",
          list: arbitrateList
        }
      ]
      for (const item of list) {
        if (item.list.length === 0) {
          this.$dialog.alert({
            title: "提示",
            message: `请填写${item.tips}信息`,
            theme: "round-button"
          })
          return
        }
      }

      const { abb015 } = this.formData
      if (!abb015) {
        this.$dialog.alert({
          title: "提示",
          message: `请输入事实理由！`,
          theme: "round-button"
        })
        return
      }

      this.submit(applyPeopleList, abb015)

    },
    // 设置表单数据到各个组件
    setFormDataToComponents(formData) {
      // 这里根据实际需求将formData中的数据分配给各个组件
      // 例如：申请人、被申请人信息等
      console.log("接收到的formData:", formData)

      // 假设formData中有申请人和被申请人信息
      // 我们需要将这些信息转换为组件需要的格式
      if (formData.abb286) {
        // 设置事实理由
        this.formData.abb015 = formData.abb286 || "" // 申请人陈诉作为事实理由
      }
      if (formData.aab004) {
        // 设置单位名称
        this.formData.aab004 = formData.aab004 || ""
      }
      // if (formData.aae010) {
      //   this.formData.aae010 = formData.aae010 || ""
      // }
      // if (formData.aae012) {
      //   this.formData.aae012 = formData.aae012 || ""
      // }
      // if (formData.aab013) {
      //   this.formData.aab013 = formData.aab013 || ""
      // }
      if (formData.aae006) {
        this.formData.aae006 = formData.aae006 || ""
      }

      if (formData.aae015) {
        this.formData.aae015 = formData.aae015 || ""
      }
      if (formData.abb281) {
        this.formData.aae005 = formData.abb281 || ""
      }
      // if (formData.aae008) {
      //   this.formData.aae008 = formData.aae008 || ""
      // }
      if (formData.aae005) {
        this.formData.aae005 = formData.aae005 || ""
      }

    },

    // 提交
    submit(applyPeopleList, abb015) {
      const { bcz005 } = applyPeopleList[0]
      const params = {
        bcz005, //主键
        abb015, //事实理由
        zcy000: this.organId //仲裁委员会
      }
      this.$dialog.confirm({
        title: "提示",
        message: "保存后信息将不能修改。\n您是否确定保存！",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        if (this.otherFormData){
          const params = {}
          if (this.otherFormData.bcz001) {
            params.bcz001 = this.otherFormData.bcz001
          } else {
            params.id = this.otherFormData.abz001
            params.aae391 = this.otherFormData.aae391
          }
          this.updateInfoSfmy(params)
        }
        commonApi.proxyApi({
          serviceName: "xytBc05_saveOrUpdateBc05",
          ...params
        }).then((res) => {
          this.$toast("保存成功！")
          this.$router.go(-1)
        }).catch((err) => {
          console.error(err)
        })
      })
    },
    updateInfoSfmy(params){
      if (params.bcz001){
        params.serviceName = "xytBc01_updateBc01Sfmy00"
      } else {
        params.serviceName = "jftjsq_updateInfoSfmy"
      }
      params.sfmy00 = "000"
      commonApi.proxyApi(params).then((res) => {
      })
    }
  }
}
</script>

<style lang="less" scoped>
.labor-arbitrate-handle {
  .button-box-more {
    background: #fff;
  }
}
</style>
