<!--
 * @Description: 确认弹窗页面
 * @Version: 0.1
 * @Autor: Chenyt
 * @Date: 2020-07-03 16:25:29
 * @LastEditors: yjm
 * @LastEditTime: 2020-11-04 11:26:54
-->
<template>
  <div class="dialog-component" v-if="showDialog">
    <div class="dialog-gray-bg"></div>
    <div class="pop-content flex-v">
      <!-- 弹窗头部 -->
      <slot name="dialog-header">
        <div class="header flex-h">
          <span class="title">{{ title }}</span>
          <van-icon
            class="close"
            name="cross"
            @click="cancelClicked"
            v-if="!cancelText || showClose"
          />
        </div>
      </slot>
      <!-- 弹窗中间内容 -->
      <slot name="dialog-content">
        <div class="scroll-content" :style="{ width: contentWidth }">
          <span v-html="content"></span>
        </div>
      </slot>
      <!-- 弹窗底部内容 -->
      <slot name="dialog-footer">
        <div class="actions">
          <button class="confirm" @click="confirmClicked">
            {{ confirmText }}
          </button>
          <button class="cancel" @click="cancelClicked" v-if="cancelText">
            {{ cancelText }}
          </button>
        </div>
      </slot>
    </div>
  </div>
</template>
<script>
import { Icon } from "@ylz/vant"

export default {
  name: "y-confirm-dialog",
  components: { "van-icon": Icon },
  props: {
    title: {
      //提示标题
      type: String,
      default: "温馨提示"
    },
    showClose: {
      //关闭
      type: Boolean,
      default: false
    },
    contentWidth: {
      //内容宽度
      type: String,
      default: "80%"
    },
    popShow: {
      //展示弹窗
      type: Boolean,
      default: true
    },
    content: {
      //提示内容，图片等
      type: String,
      default: () => {
        return `提交时附加说明图片，可帮助您更快地解决问题。`
      }
    },
    confirmText: {
      //再次确认
      type: String,
      default: () => {
        return `直接提交`
      }
    },
    cancelText: {
      //取消的文字
      type: String,
      default: () => {
        return `取消`
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    showDialog: {
      get() {
        return this.popShow
      },
      set(newValue) {
        this.$emit("input", newValue)
      }
    }
  },
  methods: {
    /**
     * @description: 取消
     * @param {type}
     * @return:
     * @author: Chenyt
     */
    cancelClicked() {
      this.$emit("update:popShow", false)
      console.log(this.showDialog, "this.showDialog")
      this.$emit("closePop")
    },
    /**
     * @description: 确认
     * @param {type}
     * @return:
     * @author: Chenyt
     */
    confirmClicked() {
      this.$emit("update:popShow", false)
      this.$emit("confirm")
    }
  }
}
</script>

<style lang="less" scoped>
.dialog-gray-bg {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  width: 100%;
  height: 100%;
  background: rgba(48, 49, 51, 0.4);
}
.pop-content {
  width: 270px * @ratio;
  position: absolute;
  z-index: 10;
  left: 50%;
  margin-left: -135px * @ratio;
  top: 50%;
  margin-top: -85px * @ratio;
  background: @white_bg_color;
  border-radius: 12px * @ratio;
  overflow: hidden;

  .header {
    align-items: center;
    width: 270px * @ratio;
    height: 40px * @ratio;
    line-height: 40px * @ratio;
    position: relative;

    .title {
      font-size: 18px * @ratio;
      color: @main_text_color;
      font-weight: 600;
      display: block;
      text-align: center;
      margin-top: 10px * @ratio;
    }

    .tips {
      width: 85px * @ratio;
      height: 44px * @ratio;
      position: absolute;
      bottom: 0;
      right: 18px * @ratio;
    }
    .close {
      position: absolute;
      width: 14px * @ratio;
      height: 14px * @ratio;
      right: 18px * @ratio;
      top: 2px * @ratio;
      color: @second_text_color;
    }
  }
  .scroll-content {
    margin: 0 auto;
    padding: 18px * @ratio 16px * @ratio 8px * @ratio;
    overflow: scroll;
    margin: 0 auto;
    text-align: center;
    word-wrap: break-word;
    word-break: normal;
    span {
      font-size: 14px * @ratio;
      line-height: 20px * @ratio;
      display: block;
      color: @main_text_color;
    }
  }
  .actions {
    margin: 10px * @ratio 15px * @ratio 25px * @ratio 15px * @ratio;
    button {
      width: 100%;
      font-size: 16px * @ratio;
      border-radius: 0;
      line-height: 35px * @ratio;
      border: none;
      border: 0.5px * @ratio solid @main_color;
    }

    button::after {
      border: none;
      outline: none;
    }

    .cancel {
      margin-top: 10px * @ratio;
      border-radius: 18px * @ratio;
      color: @main_color;
      border: 0.5px * @ratio solid @main_color;
      background: none;
    }

    .confirm {
      color: @white_bg_color;
      background: @main_color;
      border-radius: 18px * @ratio;
    }
  }
}
</style>
