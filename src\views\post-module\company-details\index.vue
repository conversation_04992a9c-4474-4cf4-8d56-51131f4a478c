<!--
 * @Description: 企业详情
 * @Version: 0.1
 * @Author: hwx
-->
<template>
  <div class="company-details">
    <!-- 公司信息 -->
    <div class='company-box'>
      <div class='company-left'>
        <img src="@/assets/imgs/post-module/company-logo.png" alt="">
      </div>
      <div class='company-right'>
        <p class="company-name">{{companyInfo.name}}</p>
        <div class="label-list">
          <span class="label" v-if="companyInfo.aab022">{{companyInfo.aab022}}</span>
          <span class="label" v-if="companyInfo.aab020">{{companyInfo.aab021}}</span>
          <span class="label" v-if="companyInfo.dwgm00">{{companyInfo.dwgm01}}</span>
        </div>
      </div>
    </div>
    <div class='split-line'></div>

    <div class='tabs-container'>
      <van-sticky>
        <van-tabs v-model="active">
          <van-tab v-for="(item, key) in tabList" :title="item.text" :name="item.type" :key="key">
          </van-tab>
        </van-tabs>
      </van-sticky>

      <div v-if="active === 'company'" class='comp-details'>
        <y-title content="公司详情" fontContSize="14"/>
        <p class="company-introduce">
          {{ companyInfo.introduce || '暂无单位简介' }}
        </p>

        <y-title content="工作地点" fontContSize="14"/>
        <div class='adress-box'>
          <div class='location-line'>
            <van-icon name="location-o" />
            <p class="adress-name">{{companyInfo.address}}</p>
          </div>
        </div>
      </div>

      <div v-if="active === 'post'" class='post-details'>
        <div v-if="otherPostList?.length > 0">
          <div
            class="tabs-item"
            v-for="(item, key) in otherPostList"
            :key="key"
          >
          <van-row>
            <van-col class="item-title text-ellipsis-2 ellipsis-2" span="12" @click="handleClickPost(item)">{{ item.gwmc00 }}</van-col>
            <van-col class="item-price" span="12">{{ item.salary }}</van-col>
          </van-row>
          <div class="label-list">
            <span class="label" v-if="item.countyName">{{item.countyName}}</span>
            <span class="label" v-if="item.workExperienceName">{{item.workExperienceName}}</span>
            <span class="label" v-if="item.educationName">{{item.educationName}}</span>
            <span class="label" v-if="item.natureName">{{item.natureName}}</span>
          </div>
          <van-row>
            <van-col class="inviter" span="24">            
              <img class="user-logo" src="@/assets/imgs/post-module/<EMAIL>" alt="">
              <span class="inviter-name">招聘者：{{ item.contacts }}</span>            
            </van-col>
          </van-row>
          <van-row class="item-bottom">
            <van-col span="16 text-ellipsis-2 ellipsis-2">
              {{ item.aab004 }}
            </van-col>
            <van-col class="text-align-right text-ellipsis-2 ellipsis-2" span="8">
              {{ item.countyName }}
            </van-col>
          </van-row>
          </div>
        </div>

        <y-empty v-else></y-empty>
      </div>
    </div>        
  </div>
</template>

<script>
/* eslint-disable no-undef */
import {commonApi} from "@/api"

export default {
  components: {
    
  },
  data() {
    return {
      aab001: "", //单位编号
      postType: "daily", // 当查看模式 日常用工/灵活用工

      companyInfo: {}, //企业信息

      active: "company",
      tabList: [
        { type: "company", text: "企业详情"},
        { type: "post", text: "在招岗位"}
      ],
      otherPostList: [] //在招岗位
    }
  },
  created() {
    const { aab001, postType } = this.$route.query
    this.aab001 = aab001
    this.postType = postType
    this.findRcygByPage(aab001) //其它在招岗位
    this.searchCompDetails(aab001) // 查询公司详情
  },
  methods: {
    // 查询岗位详情
    findRcygByPage(aab001) {
      const params = {
        serviceName: "xytQzzp_findRcygByPage",
        aab001
      }
      commonApi.proxyApi(params).then(res => {
        console.log(res, "其它在招岗位")
        const {rows} = res.map?.data || {}
        this.otherPostList = rows.slice(0, 6)
        this.otherPostList = [...this.otherPostList, ...this.otherPostList]
      })
    },
    // 查询公司详情
    searchCompDetails(aab001) {      
      const params = {
        serviceName: "xytQzzp_getQzzpCompanyById",
        aab001
      }
      commonApi.proxyApi(params).then(res => {
        console.log(res, "查询公司详情")
        this.companyInfo = res.map?.data || {}
      })
    },
    // 查看岗位信息
    handleClickPost(item) {
      const {id, aab001} = item
      const {postType} = this
      this.$router.push({path: "/post-details", query: {id, aab001, postType}})
    }
  }
}
</script>

<style lang="less" scoped>
.company-details {
  .company-box {
    padding: 16px;
    display: flex;
    padding-bottom: 20px;
    .company-left {
      width: 40px;
      height: 40px;
      & > img {
        width: 100%;
        height: 100%;
      }
    }
    .company-right {
      margin-left: 10px;        
      .company-name {
        font-size: 16px;
        color: #3C3D40;
        line-height: 20px;
        font-weight: bold;
      }
    }
  }
  .split-line {
    width: 100%;
    height: 4px;
    background: #F4F8FB;
  }
  .tabs-container {
    .comp-details {
      padding: 16px;
      .company-introduce {
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        padding-bottom: 16px;
      }
      .adress-box {      
        .location-line {
          display: flex;
          .van-icon-location-o {
            font-size: 18px;
          }
          .adress-name {
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            margin-left: 10px;
          }
        }
        .map-container {
          width: 100%;
          height: 120px;
          margin-top: 16px;
        }    
      }
    }  
    .post-details {
      background: #F6F6F6;
      .tabs-item {
        padding: 20px 16px;
        background-color: #fff;
        margin-bottom: 16px;
        .item-title {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 16px;
          color: #222327;
          text-align: left;
          font-style: normal;
        }
        .item-price {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 16px;
          color: #FF3B37;
          line-height: 22px;
          text-align: right;
          font-style: normal;
        }
        .label-list {
          padding-bottom: 18px;
          border-bottom: 1px solid #E5E5E5;
          margin-top: 16px;
          .label {
            padding: 5px;
            background: #F6F6F6;
            border-radius: 2px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            line-height: 20px;
            text-align: left;
            font-style: normal;
            margin-right: 4px;
          }
        }
        .inviter {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          display: flex;
          align-items: center;
          margin-top: 11px;
          .user-logo {
            width: 12px;
            height: 15px;
          }
          .inviter-name {
            margin-left: 10px;
          }
          .icon {
            margin-right: 9px;
            width: 12px;
            height: 15px;
            border: 1px solid;
          }
        }
        .item-bottom {
          margin-top: 6px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          .text-align-right {
            text-align: right         ;
          }
        }
      }
    }  
  }
  .label-list {
    .label {
      padding: 5px;
      background: #F6F6F6;
      border-radius: 2px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      margin-right: 16px;
    }
  }
}

</style>