<!--
 * @Description: 益鹭保
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="ylb-container">
    <!-- <y-title content="理赔信息列表" :background-color="ylb_color"  />
     -->
    <view-box :formList="formList"></view-box>
  </div>
</template>

<script>
import ViewBox from "./components/view-box"
import {ylb_color} from "@/styles/theme/theme-params.less"
import { commonApi } from "@/api"
export default {
  name: "claims-check",
  components: {
    ViewBox
  },
  data() {
    return {
      ylb_color,
      formList: []
    }
  },
  created() {
    this.init()  
  },
  methods: {
    init() {
      this.queryDa13ByPage()
    },
    queryDa13ByPage() {
      const params = {
        serviceName: "xytDa13_findDa13ByPage"
      }
      commonApi.proxyApi(params).then(res => {
        const { data } = res?.map
        this.formList =data.rows.map(item => {
          return {
            ...item,
            sjgs00: item.sjgs00+"元",
            yjpk00: item.yjpk00+"元"
          }
        })
        // this.formList = data.rows
      })
    }
  }
 
}
</script>

<style lang="less" scoped>
.ylb-container {
  padding: 10px 16px 10px;
}
</style>