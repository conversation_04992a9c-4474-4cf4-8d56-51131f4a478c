/*******
 * @Description: 骑手服务
 * @Version: 0.1
 * @Autor: T
 */
export default [
  {
    path: "/rider",
    name: "骑手服务",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "riderModule" */"@/views/rider-module")
  },
  {
    path: "/rider-online-study",
    name: "骑手在线学习",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "riderModule" */"@/views/rider-module/rider-online-study")
  },
  {
    path: "/learn-exam-query",
    name: "学习与考试情况查询",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "riderModule" */"@/views/rider-module/learn-exam-query")
  }
]