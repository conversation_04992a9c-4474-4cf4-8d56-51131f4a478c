<!--
 * @Description: 
 * @Version: 0.1
 * @Autor: lzx
 * @Date: 2020-07-27 16:45:59
 * @LastEditors: lzx
 * @LastEditTime: 2020-07-29 19:01:30
--> 
# YSocietySteps 步骤条 - 人社

### 介绍

多用于人社的步骤条组件，可设置步骤数和当前进行的步骤

### 引入

```js
import Vue from 'vue';
import { YSocietySteps } from '@ylz/vant';

Vue.use(YSocietySteps);
```

## 代码演示

### 简单示例

```html
<y-society-steps :handleList="handleList" :active="1"></y-society-steps>
```

```js
import YSocietySteps from '@/components/template/y-society-steps'
export default {
  components: {
    YSocietySteps
  },
  data () {
    return {
      handleList: [
        '申请人信息',
        '失业信息',
        '求职信息',
        '申请'
      ]
    }
  }
}
```

## Api

### Props

| 参数        | 说明         | 类型     | 默认值   |
| ----------- | ------------ | -------- | -------- |
| active   | 当前进行到的步骤数，从0开始 | _number_ | `0`|
| handleList | 步骤数组 | _array_ | - |
| activeColor | 完成步骤的颜色 | _string_ | `@main_color` |
| unActiveColor | 未完成步骤颜色 | _string_ | `@four_text_color` |
