<!--
 * @Description: 资讯详情
 * @Author: wujh
 * @date: 2024/1/31 17:21
 * @LastEditors: Please set LastEditors
-->
<template>
  <div class="revoke-box">

    <van-form class="base-form" >
      <!--      <y-title content="撤回申请"/>-->
      <van-cell-group inset>

        <van-field
            v-model="formData.title"
            name="title"
            label="标题"
            :required="true"
            placeholder="请输入标题"
            :disabled="isView === '1'"
        />

        <van-field
            class="van-field-textarea"
            v-model="formData.message"
            name="context"
            label="留言内容"
            placeholder="请输入留言内容"
            type="textarea"
            :required="true"
            :disabled="isView === '1'"
        />
      </van-cell-group>

      <van-cell-group >
        <div class="uploader-title">附件</div>
        <div class="uploader-container">
          <van-uploader
              v-model="attachmentList"
              :preview-full-image="true"
              :disabled="isView === '1'"
              :deletable="isView !== '1'"
              :required="true"
              @oversize="handleOversize"
              accept = '.jpg,.jpeg,.png'
              max-size="10 * 1024 * 1024"
              max-count="1"
          >
          </van-uploader>
        </div>
      </van-cell-group>
      <div class="button-box mt18" >
        <van-button plain type="info" @click="handleCancel" native-type="button">
         返 回
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import {commonApi} from "@api"
import {addBase64Header} from "@/utils/fileUtil"

export default {
  name: "information-center-detail",
  components: {},
  data(){
    return {
      showType: "0", //展示类型 0富文本展示 1查询字段展示
      formData: {},
      attachmentList: [],
      isView: "1"
    }
  },
  mounted() {
    this.getTypicalCaseDetail()
  },
  methods: {
    handleCancel(){
      this.$router.go(-1)
    },
    // 限制上传文件大小
    handleOversize() {
      this.$dialog.alert({
        title: "提示",
        message: "请上传小于10M的图片！",
        theme: "round-button"
      })
    },
    //查询典型案例详情
    getTypicalCaseDetail(){
      commonApi.proxyApi({
        serviceName: "xytBc08Web_getBc08ById",
        bcz008: this.$route.query.bcz008
      }).then((res) => {
        this.formData= res?.map?.data || {}
        const { attachment } = this.formData
        this.attachmentList = attachment ? [{content: addBase64Header(attachment, "image/png")}] : []
      })
    }
  }
}
</script>

<style scoped lang="less">
.van-button{
  width: 100%;
}
.uploader-title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 18px;
  padding: 16px 0 0 16px;
}
.uploader-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: 20px;
  /deep/.van-uploader {
    .van-uploader__input-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;

      .uploader-box {
        margin: 20px 0 0;
        border-radius: 8px;
        border: 1px dashed #cecece;
        width: 180px;
        height: 112px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        & > img {
          width: 98px;
          height: 64px;
        }
        & > span {
          font-size: 14px;
          color: #303133;
          line-height: 16px;
        }
      }
    }
    .van-uploader__preview {
      margin: 20px 0 0;
      border-radius: 8px;
      border: 1px dashed #cecece;
      width: 180px;
      height: 112px;
      display: flex;
      justify-content: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
  }
}
.uploader-container-alone {
  padding: 8px 16px;
}
/deep/.alone-name {
  &::after {
    border-bottom: none !important;
  }
  .van-cell__value {
    display: none;
  }
}
.information-center-container{
  .seek-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    .seek-title {
      font-size: 18px;
      font-weight: bold;
      color: @main_text_color;
      line-height: 25px;
    }
    .more {
      font-size: 14px;
      font-weight: bold;
      color: @five_text_color;
      line-height: 20px;
    }
  }
  .tabs-box {
    .van-list{
      padding: 0 16px;
    }
    ::v-deep .van-tab {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }
    ::v-deep .van-tabs__content {
      padding: 0 0 0 14px;
    }
    .tabs-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      &:not(:last-child) {
        border-bottom: 1px solid #EEEEEE;
      }
      .item-left {
        .item-top {
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
        }
        .item-bottom {
          margin-top: 20px;
          .item-tip {
            font-size: 11px;
            font-weight: 400;
            color: @main_color;
            line-height: 16px;
          }
          .item-date {
            font-size: 12px;
            font-weight: 400;
            color: @five_text_color;
            line-height: 18px;
            //margin-left: 20px;
          }
        }
      }
      .item-right {
        width: 108px;
        margin-left: 18px;
        border-radius: 4px;
        & > img {
          width: 100%;
          max-width: 180px;
          object-fit: contain;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
