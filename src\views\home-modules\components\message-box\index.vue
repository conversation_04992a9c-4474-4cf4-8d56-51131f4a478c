<template>
  <div ref="dialogueContainer" class="dialogue-container">
    <div
      v-for="(item, index) in messages"
      :key="index" class="message-box"
      :class="{ 'last-message': index === messages.length - 1 }"
    >
      <div v-if="item.role === 'assistant' && assistantProfile" class="assistant-icon">
        <img src="@pic/home/<USER>/profile-picture.png"/>
        <span>智慧小新</span>
      </div>
      <div :class="['message-content', item.role]">
        <div v-html="item.content"></div>
        <!-- 加载动画 -->
        <div v-if="item.role === 'assistant' && !item.content && !item.stop && (isStreaming || lastStatus)" class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <div v-if="item.role === 'assistant' && (messages.length > 1 ? index !== 0 : true)" class="message-item-more">
          <span>
            <img
              v-if="index === messages.length - 1"
              :src="playingStatus ? require('@pic/home/<USER>/volume-up-highlight.png') : require('@pic/home/<USER>/volume-up.png')"
              @click="handleVolume"
              style="margin-right: 5px;"
            />
          </span>
          <span v-if="index !== 0">
            <img
              :src="item.upHighlight ? require('@pic/home/<USER>/thumb-up-highlight.png') : require('@pic/home/<USER>/thumb-up.png')"
              @click="handleThumb(index, 'up', item.upHighlight)"
            />
            <img
              :src="item.downHighlight ? require('@pic/home/<USER>/thumb-down-highlight.png') : require('@pic/home/<USER>/thumb-down.png')"
              @click="handleThumb(index, 'down', item.downHighlight)"
            />
          </span>
        </div>
      </div>
      <div v-if="item.role === 'assistant' && item.stop && !item.expired" class="message-tip">这条消息已停止</div>
    </div>
    <div v-if="messages.length && guessQuestions.length" class="message-guess-question">
      <span
        v-for="(question, questionIndex) in guessQuestions"
        :key="questionIndex"
      ><span style="color: #0194FF;" @click="$emit('sendGuessQuestion', question)">{{ question }}</span></span>
    </div>
    <span v-if="messages.length" class="message-tip">内容由 AI 生成</span>
  </div>
</template>

<script>
export default {
  props: {
    messages: {
      type: Array,
      default: () => []
    },
    isStreaming: {
      type: Boolean,
      default: false
    },
    lastStatus: {
      type: Boolean,
      default: false
    },
    assistantProfile: {
      type: Boolean,
      default: true
    },
    guessQuestions: {
      type: Array,
      default: () => []
    },
    virtualHuman: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      playingStatus: true,
      isSpeechReady: false,
      speechQueue: []
    }
  },
  watch: {
    messages: {
      handler(newVal, oldVal) {
        if (this.isStreaming) {
          // 流式传输时，由 isStreaming 的 watcher 处理
          return
        }
        if (this.playingStatus && newVal.length > oldVal.length) {
          this.playLatestMessage()
        }
      },
      deep: true
    },
    isStreaming(newVal, oldVal) {
      if (oldVal === true && !newVal) {
        // 流式传输结束
        if (this.playingStatus) {
          this.playLatestMessage()
        }
      }
    }
  },
  mounted() {
    if (window.speechSynthesis) {
      if (window.speechSynthesis.getVoices().length) {
        this.isSpeechReady = true
      } else {
        window.speechSynthesis.onvoiceschanged = () => {
          this.isSpeechReady = true
          this.processSpeechQueue()
        }
      }
    }
  },
  methods: {
    handleVolume() {
      this.playingStatus = !this.playingStatus
      if (this.virtualHuman) {
        this.$emit("handleVolume", this.playingStatus)
        return
      }
      if (this.playingStatus) {
        this.playLatestMessage()
      } else {
        window.speechSynthesis && window.speechSynthesis.cancel()
      }
    },
    playLatestMessage() {
      if (!this.messages.length) { return }
      const lastIndex = this.messages.length - 1
      const lastMsg = this.messages[lastIndex]
      if (this.virtualHuman) {
        return
      }
      if (lastMsg.role === "assistant" && lastMsg.content) {
        this.startPlayingVoice(lastMsg.content)
      }
    },
    startPlayingVoice(content) {
      if (!this.isSpeechReady) {
        this.speechQueue.push(content)
        return
      }
      window.speechSynthesis && window.speechSynthesis.cancel()
      const text = this.stripHtml(content)
      if (text) {
        const utter = new window.SpeechSynthesisUtterance(text)
        utter.lang = "zh-CN"
        utter.rate = 1.5
        utter.onend = () => {
        }
        utter.onerror = (e) => {
          console.error("SpeechSynthesisUtterance.onerror", e)
        }
        window.speechSynthesis.speak(utter)
      }
    },
    processSpeechQueue() {
      if (this.speechQueue.length > 0) {
        const content = this.speechQueue.shift()
        this.startPlayingVoice(content)
      }
    },
    handleThumb(index, type, value) {
      if (type === "up") {
        this.$set(this.messages[index], "upHighlight", this.messages[index].upHighlight ? false : true)
        this.$set(this.messages[index], "downHighlight", false)
      } else {
        this.$set(this.messages[index], "upHighlight", false)
        this.$set(this.messages[index], "downHighlight", this.messages[index].downHighlight ? false : true)
      }
      if (!value) {
        this.$toast.success("感谢您的反馈")
      }
    },
    stripHtml(html) {
      const tmp = document.createElement("div")
      tmp.innerHTML = html
      return tmp.textContent || tmp.innerText || ""
    }
  },
  beforeDestroy() {
    window.speechSynthesis && window.speechSynthesis.cancel()
  }
}
</script>

<style lang="less" scoped>
.dialogue-container {
  height: calc(100vh - 120px);
  padding: 0px 16px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
  display: flex;
  flex-direction: column;
  .message-box {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    &.last-message {
      margin-bottom: 0;
    }
    .assistant-icon {
      margin-bottom: 10px;
      height: 36px;
      display: flex;
      align-items: center;
      img {
        height: 36px;
      }
      span {
        margin-left: 15px;
        color: @second_text_color;
      }
    }
    .message-content {
      min-height: 42px;
      padding: 10px 15px;
      line-height: 1.6;
      border-radius: 12px;
      max-width: 75%;
      word-break: break-all;
      position: relative;
      &.assistant {
        background-color: #fff;
        align-self: flex-start;
        border-top-left-radius: 4px;
      }
      &.user {
        background-color: #0194FF;
        align-self: flex-end;
        color: #fff;
        border-bottom-right-radius: 4px;
      }
      .message-item-more {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 0.5px dashed #DCDFE6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        span {
          height: 24px;
        }
        img {
          width: 24px;
          height: 24px;
          & + img {
            margin-left: 4px;
          }
        }
      }
    }
  }
  .message-tip {
    display: flex;
    align-items: center;
    padding: 3px 15px 0;
    color: #999;
    font-size: 12px;
  }
}

.loading-dots {
  display: flex;
  align-items: center;
  height: 24px;
  span {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin: 0 2px;
    border-radius: 50%;
    background: #4db3ff;
    opacity: 1;
    animation: loading-color 1.2s infinite;
  }
  span:nth-child(2) {
    animation-delay: 0.4s;
  }
  span:nth-child(3) {
    animation-delay: 0.8s;
  }
}

@keyframes loading-color {
  0%   { background: #4db3ff; }
  33%  { background: #7ed6ff; }
  66%  { background: #b2e0ff; }
  100% { background: #4db3ff; }
}

.message-guess-question {
  background: rgba(255,255,255,0.50);
  border-radius: 15px;
  border: 1px solid #fff;
  padding: 10px 15px;
  margin-top: 10px;
  display: flex;
  flex-direction: column;

  span + span {
    margin-top: 5px;
  }
}
</style>