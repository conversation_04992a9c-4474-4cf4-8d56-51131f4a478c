<!--
 * @Description: 人社主题步骤条
 * @Version: 0.1
 * @Autor: lzx
-->
<template>
  <div class="page-step">
    <div class="step-wrap" v-for="(item, index) in handleList" :key="index">
      <div
        class="step-round"
        :style="{
          backgroundColor: active >= index ? activeColor : unActiveColor,
          '--active': index < active ? activeColor : unActiveColor
        }"
      />
      <div
        class="step-text"
        :style="{ color: active >= index ? activeColor : unActiveTextColor }"
      >
        {{ item }}
      </div>
    </div>
  </div>
</template>

<script>
import { step_main_color, four_text_color, five_text_color } from "@/styles/theme/theme-params.less"
export default {
  name: "y-society-steps",
  props: {
    //当前步骤，从0开始
    active: {
      type: Number,
      default: 0
    },
    //步骤名称数组
    handleList: {
      type: Array
    },
    //已完成步骤的颜色
    activeColor: {
      type: String,
      default: step_main_color
    },
    //未完成颜色
    unActiveColor: {
      type: String,
      default: four_text_color
    },
    //未完成颜色-文字
    unActiveTextColor: {
      type: String,
      default: five_text_color
    }
  }
}
</script>

<style lang="less" scoped>
.page-step {
  height: 90px * @ratio;
  padding: 3px * @ratio 5px * @ratio;
  display: flex;
  align-items: center;
  justify-content: space-between;
  counter-reset: section;
  border-bottom: 16px * @ratio solid rgba(@second_border_color, 1);
  .step-wrap {
    //text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    position: relative;
    z-index: 0;
    &:last-child {
      .step-round::after {
        display: none;
      }
    }
    .step-round {
      width: 20px * @ratio; 
      height: 20px * @ratio;
      background-color: @main_bg_color;
      border-radius: 50%;
      flex: 1;
      &::before {
        counter-increment: section;
        content: counter(section);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px * @ratio;
        color: @white_text_color;
        width: 20px * @ratio;
        height: 20px * @ratio;
        border-radius: 50%;
      }
      @num: counter(section);
      &::after {
        content: "";
        width: 100%;
        height: 3px * @ratio;
        background-color: var(--active);
        position: absolute;
        //left: -40%;
        top: 9px * @ratio;
        z-index: -1;
      }
    }
    .step-text {
      font-size: 14px * @ratio;
      color: @main_text_color;
      line-height: 20px * @ratio;
      margin-top: 8px * @ratio;
    }
  }
}
</style>
