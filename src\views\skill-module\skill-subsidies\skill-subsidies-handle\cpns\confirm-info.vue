<!--
 * @Description: 确认信息
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="submit-material">
    <!-- 人员信息 -->
    <van-form
      ref="materialForm"
      class="base-form"
      :disabled="pageType === 'detail'"
      @failed="onFailed"
      @submit="handleSubmit"
    >
      <y-title content="人员信息" />
      <van-cell-group inset class="border-bottom-wide">
        <van-field
          v-model="formData.aac003"
          name="aac003"
          label="姓名"
          placeholder="请输入"
          :required="true"
          disabled
          :rules="formRules.aac003"
        />
        <van-field
          v-model="formData.aac002"
          name="aac002"
          label="身份证号"
          placeholder="请输入"
          :required="true"
          disabled
          :rules="formRules.aac002"
        />
        <y-select-dict
          class="laber-wider"
          v-model="formData.sfgat0"
          label="是否港澳台地区"
          :disabled="pageType === 'detail'"
          :filterabled="false"
          :required="true"
          :rules="formRules.sfgat0"
          dict-type="JZBT_SFGAT0"
          is-link
        />
        <van-field
          v-if="formData.sfgat0 !== '1'"
          class="laber-wider"
          v-model="formData.gatzjh"
          name="gatzjh"
          label="港澳台相应证件号码"
          placeholder="请输入"
          :required="true"
          :rules="formRules.gatzjh"
        />
        <van-field
          v-model="formData.acb051"
          name="acb051"
          label="移动电话"
          placeholder="请输入"
          :required="true"
          :rules="formRules.acb051"
        />
        <y-select-dict
          v-model="formData.jyqk00"
          label="就业情况"
          :filterabled="false"
          :required="true"          
          :rules="formRules.jyqk00"
          dict-type="JZBT_JYQK00"
          is-link
          :disabled="pageType === 'detail'"
        />
        <y-select-dict
          v-model="formData.rysx00"
          label="人员属性"
          :filterabled="false"
          :required="true"
          :rules="formRules.rysx00"
          dict-type="JZBT_RYSX00"
          is-link
          :disabled="pageType === 'detail'"
        />
        <y-select-dict
          v-model="formData.aac009"
          label="户口性质"
          :filterabled="false"
          :required="true"
          :rules="formRules.aac009"
          dict-type="JZBT_HKXZ00"
          is-link
          :disabled="pageType === 'detail'"
        />
        <y-select-dict
          v-model="formData.sfbsry"
          label="是否本市户籍"
          :required="true"
          :filterabled="false"
          :rules="formRules.sfbsry"
          dict-type="JZBT_SFBSRY"
          is-link
          :disabled="pageType === 'detail'"
        />
        <van-field
          v-model="formData.aac0j3"
          name="aac0j3"
          label="户籍地址"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aac0j3"
        />
        <van-field
          v-model="formData.aae006"
          name="aae006"
          label="通讯地址"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aae006"
        />
      </van-cell-group>

      <y-title content="补贴信息" />
      <van-cell-group inset class="border-bottom-wide">
        <y-select-dict
          v-model="formData.jzbtlx"
          label="补贴申报类型"
          disabled
          :required="true"
          :rules="formRules.jzbtlx"
          dict-type="JZBT_BTLX00"
          is-link
        />
        <van-field
          v-model="formData.zsbh00"
          name="zsbh00"
          label="证书编号"
          placeholder="请输入"
          :required="true"
          disabled
          :rules="formRules.zsbh00"
        />
        <van-field
          v-model="formData.fzrq00"
          name="fzrq00"
          label="发证时间"
          placeholder="请选择"
          :required="true"
          disabled
          :rules="formRules.fzrq00"
        />
        <van-field
          v-model="formData.fzjg00"
          name="fzjg00"
          label="发证机构"
          placeholder="请输入"
          :required="true"
          disabled
          :rules="formRules.fzjg00"
        />
        <van-field
          v-model="formData.gzmc00"
          name="gzmc00"
          label="工种"
          placeholder="请输入"
          :required="true"
          disabled
          :rules="formRules.gzmc00"
        />
        <y-select-dict
          v-model="formData.djbm00"
          label="证书等级"
          disabled
          :required="true"
          :rules="formRules.djbm00"
          dict-type="JZBT_DJBM00"
          is-link
        />
        <y-select-dict
          v-model="formData.jnjdlx"
          label="证书类型"
          disabled
          :required="true"
          :rules="formRules.jnjdlx"
          dict-type="JZBT_ZSLX00"
          is-link
        />
        <van-field
          v-model="formData.btzje0"
          name="btzje0"
          label="补贴总金额"
          placeholder="请输入"
          :required="true"
          disabled
          :rules="formRules.btzje0"
        />
      </van-cell-group>

      <y-title content="银行信息" />
      <van-cell-group inset class="border-bottom-wide">
        <van-field
          v-model="formData.yhkh00"
          name="yhkh00"
          label="银行卡号"
          placeholder="请输入"
          :required="true"
          disabled
        />
        <y-select
          class="y-select"
          :required="true"
          label="银行机构"
          v-model="formData.yhjg00"
          :columns="bankList"
          :format="conmonFormat"
          disabled
        />

        <!-- <y-select
          class="y-select"
          :required="true"
          label="分行（省）"
          v-model="formData.province"
          :columns="provinceList"
          :format="format"
          disabled
        />

        <y-select
          :required="true"
          label="分行（市）"
          v-model="formData.city"
          :columns="cityList"
          :format="format"
          disabled
        /> -->

        <van-field
          v-model="formData.fkhzh0"
          name="fkhzh0"
          label="银行分行"
          placeholder="请输入"
          :required="true"
          disabled
        />
        
      </van-cell-group>

      <template v-if="pageType === 'detail'">
        <y-title content="申请进度" />
        <div class="apply-steps-box">
          <apply-steps :baseFormData="baseFormData"></apply-steps>
        </div>
      </template>      

      <!-- 操作按钮 -->
      <div class="button-box-more">
        <van-button v-if="pageType !== 'detail'" plain type="info" @click="handleBack" native-type="button">
          上一步
        </van-button>

        <van-button
          v-if="pageType !== 'detail'"
          round
          block
          type="primary"
          native-type="submit"
        >
          提 交
        </van-button>
        <van-button
          v-else
          plain
          type="info"
          @click="handleClose"
          native-type="button"
        >
          关 闭
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import { commonApi } from "@/api"
import { checkMobile } from "@utils/check"

import { provinceList, bankList } from "@/assets/data/jnbt-dict"
import ApplySteps from "./apply-steps"

export default {
  name: "submit-material",
  components: {
    ApplySteps
  },
  props: {
    baseFormData: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    baseFormData: {
      handler(val) {
        for (const key in this.formData) {
          if (val[key]) {
            this.formData[key] = val[key]
          }
        }

        console.log(this.formData, "this.formData0")
      },
      immediate: true,
      deep: true
    },
    "formData.sfgat0": {
      handler() {
        this.formData.gatzjh = ""
      }
    }
  },
  data() {
    return {
      // 表单信息
      formData: {
        aac003: "", //姓名
        aac002: "", //身份证号
        sfgat0: "", //是否港澳台地区
        acb051: "", //移动电话
        gatzjh: "", //港澳台相应证件号码
        jyqk00: "", //就业情况
        rysx00: "", //人员属性
        sfbsry: "", //是否本市户籍
        aac009: "", //户口性质
        aac0j3: "", //户籍地址
        aae006: "", //通讯地址

        jzbtlx: "", //补贴申报类型
        zsbh00: "", //证书编号
        fzrq00: "", //发证时间
        fzjg00: "", //发证机构
        gzbm00: "", //工种
        gzmc00: "", //工种名称
        djbm00: "", //证书等级
        jnjdlx: "", //证书类型
        btzje0: "", //补贴总金额

        yhkh00: "", //银行卡号
        yhjg00: "", //银行机构
        province: "", //省
        city: "", //市
        fkhzh0: ""
      },
      formRules: {
        sfgat0: [{ required: true, message: "请选择是否港澳台地区" }],
        gatzjh: [{ required: true, message: "请输入" }],
        acb051: [
          { required: true, message: "请输入" },
          {
            validator: checkMobile,

            message: "请输入正确的移动电话",
            trigger: "onBlur"
          }
        ],
        jyqk00: [{ required: true, message: "请选择就业情况" }],
        rysx00: [{ required: true, message: "请选择人员属性" }],
        aac009: [{ required: true, message: "请选择户口性质" }],
        sfbsry: [{ required: true, message: "请选择是否本市户籍" }],
        aac0j3: [{ required: true, message: "请选择户籍地址" }],
        aae006: [{ required: true, message: "请选择通讯地址" }]
      },

      materialNum: "", //材料编号
      iframeUrl: "", //上传材料页面地址

      // 地区选择
      provinceList,
      format: {
        name: "name",
        value: "name"
      },

      // 银行机构
      bankList,
      conmonFormat: {
        name: "label",
        value: "value"
      }
    }
  },
  computed: {
    pageType() {
      return this.$route.query.pageType || ""
    },
    userInfo() {
      const { xm0000: aac003, zjhm00: aac002 } =
        this.$sessionUtil.getItem("userInfo")
      return { aac003, aac002 }
    },
    cityList() {
      const { province } = this.formData
      if (!province) {
        return [{ name: "请选择", areaList: [""] }]
      }

      const list =
        this.provinceList.find((item) => item.name === province)?.cityList ||
        []
      return list
    }
  },
  created() {
    if (this.pageType !== "add") {
      return
    }
    
    this.initForm() // 初始化表单
    this.enterSub() // 新增获取必要参数
  },
  methods: {
    // 初始化表单
    initForm() {
      this.formData = {...this.formData, ...this.userInfo}
    },
    // 获取必要参数
    enterSub() {
      const params = {
        serviceName: "xytjzbt_enterSub",
        ...this.userInfo
      }
      commonApi.proxyApi(params).then((res) => {
        const { info = {} } = res.data[0] || {}
        this.formData = {...this.formData, ...info, ...this.userInfo}
      })
    },
    // 表单校验失败
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },
    // 上一步
    handleBack() {
      this.$emit("handleNext", 0)
    },
    // 提交
    handleSubmit() {
      this.$dialog
        .confirm({
          title: "提示",
          message: "您确定提交",
          showCancelButton: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        })
        .then(() => {
          this.$emit("handleSubmit", this.formData)
        })
    },

    // 选择诉求
    submitAppeal(resultText) {
      this.formData.aba003 = resultText

      this.isShowAppealPopup = false
    },

    // 选择纠纷案由
    handleConfirmReason(data) {
      console.log(data, "选择纠纷案由")
      const { resultIds, resultText } = data
      this.formData.aba001 = resultIds
      this.formData.aba002 = resultText

      console.log(this.formData, "选择纠纷案由 formData")

      this.isShowReasonPopup = false
    },

    // 关闭
    handleClose() {
      window.history.go(-2)
    }
  }
}
</script>

<style lang="less" scoped>
.submit-material {
  .y-title {
    border-bottom: none !important;
  }
  .submit-material-tips {
    margin-bottom: 24px;
  }
  .van-field-textarea {
    position: relative;
    padding: 8px;
    .textarea-select {
      position: absolute;
      top: -40px;
      right: 8px;
      color: @main_color;

      display: flex;
      justify-content: flex-end;
      align-items: center;
      & > img {
        width: 16px;
        height: 8px;
        margin-left: 4px;
      }
    }
    /deep/.van-cell__value .van-field__body > textarea {
      padding: 8px;
    }
  }
  .iframe-box {
    height: calc(100vh - 384px);
    width: 100vw;
    overflow: hidden;
    .my-iframe {
      width: 200%;
      height: 200%;
      transform: scale(0.47);
      transform-origin: 0 0;
      margin-left: 12px;
    }
  }
  .business-popup {
    width: 92%;
    border-radius: 8px;
    max-height: calc(100vh - 80px);
    .business-popup-button {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 12px 0;
      .confirm-button {
        margin-left: 16px;
      }
    }

    .checkbox-case {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .checkbox-line {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-left: 8px;
        color: @main_text_color;
        .van-cell {
          width: 20px;
          border-bottom: 1.5px solid @main_color;
          padding: 0;
          margin: 0 4px;
          line-height: 18px;
          &.money-field {
            width: 40px;
          }
        }
      }
      &-other {
        height: 20px;
      }
    }
    /deep/ .van-checkbox,
    .van-radio {
      height: auto;
      margin: 14px 0;
    }

    .wages-form {
      /deep/.van-field__error-message {
        display: none;
      }
    }

    .check-message {
      font-size: 14px;
      color: @main_color;
      text-align: center;
    }
  }
  .apply-steps-box {
    padding: 0 20px;
    /deep/ .van-step__circle {
      width: 10px;
      height: 10px;
    }
    /deep/ .van-step__circle-container {
      font-size: 22px;
    }
    /deep/ .van-step__title {
      & > h3 {
        font-size: 14px;
        line-height: 20px;
      }
      & > p {
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
}
</style>
