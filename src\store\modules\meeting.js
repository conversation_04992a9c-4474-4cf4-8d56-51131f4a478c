/*
 * @Description: 控制视频浮点相关参数
 * @Author: FBZ
 * @Date: 2024-05-23 11:08:49
 * @LastEditors: FBZ
 * @LastEditTime: 2024-05-23 18:08:50
 */
const defaultState = () => {
  return {
    videoConferenceActive: false,
    // 浮点初始位置，按需调整
    floatingDotPosition: {
      x: window.innerWidth - 60,
      y: window.innerHeight - 180
    }
  }
}
export default {
  namespaced: true,
  state: defaultState(),

  mutations: {
    SET_POSITION(state, position) {
      state.floatingDotPosition = position
    },
    SET_ACTIVE(state, active) {
      state.videoConferenceActive = active
    }
  },

  actions: {
    updatePosition({ commit }, position) {
      commit("SET_POSITION", position)
    },
    activeMeeting({ commit }) {
      commit("SET_ACTIVE", true)
    },
    closeMeeting({ commit }) {
      commit("SET_ACTIVE", false)
    },
    reset({ commit, dispatch }) {
      // 结束通话时调用此方法重置按钮位置
      dispatch("closeMeeting")
      commit("SET_POSITION", defaultState().floatingDotPosition)

    }
  }
}
