<!--
 * @Description: 技能培训补贴--记录
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="labor-supervision-record">
    <div class="info-container">
        <div v-if="list?.length > 0">
          <info-box v-for="(item, index) in list" :key="index" :moreText="item.sqjd00"  title="申请进度" :colorMore="item.themeColors" customMoreText
          >
            <template #cells>
              <van-cell title="证书编号" :value="item.zsbh00" :border="false" />
              <van-cell title="补贴申报类型" :value="getApplyType(item.jzbtlx)" :border="false" />
              <van-cell title="补贴金额" :value="item.btzje0" :border="false" />
              <van-cell title="开户行" :value="item.fkhzh0" :border="false" />
              <van-cell title="是否有效" :value="getEffective(item.sfyxbz)" :border="false" />
            </template>
            <template #buttons>
              <template v-if="item.sfyxbz === '001'">
                <van-button v-if="item.shzt00 === '004'" type="warning" class="info-button" @click="handleEdit(item)">修改</van-button>
                <van-button v-if="item.shzt00 === '001'" type="danger" @click="handleDelete(item)">删除</van-button>
              </template>                
              <van-button type="primary" @click="handleDetails(item)">查看详情</van-button>
            </template>
          </info-box>
        </div>         

        <y-empty v-else></y-empty>
    </div>

  </div>
</template>

<script>
import InfoBox from "@/components/business/info-box"
import {
  status_one_color, status_two_color, status_three_color
} from "@/styles/theme/theme-params.less"

import { commonApi } from "@/api"

export default {
  name: "labor-supervision-record",
  components: {
    InfoBox
  },
  data() {
    return {
      // 信息列表
      list: [],
      loading: false,
      finished: false,

      // 字典列表
      platformList: [], //平台字典列表
      yesNoList: [] //是否同步参会字典列表
    }
  },
  computed: {
    userInfo() {
      const {xm0000: aac003, zjhm00: aac002} = this.$sessionUtil.getItem("userInfo")      
      return {aac003, aac002}
    },
    applyTypeDict() {      
      const {JZBT_BTLX00} = this.$sessionUtil.getItem("codeList")
      return JZBT_BTLX00
    }
  },
  mounted() {
    this.queryList() // 查询补贴列表
  },

  methods: { 
    // 是否有效
    getEffective(value) {
      return value === "001" ? "有效" : "无效"
    },
    // 查询补贴列表
    queryList() {
      const params = {
        serviceName: "xytjzbt_queryList",
        ...this.userInfo
      }
      commonApi.proxyApi(params).then(res => {
        console.log(res, "res 查询补贴列表")
        this.list = res.data
        this.list.forEach(item => {
          if (item.sqjd00.includes("不通过")) { //通过
            item.themeColors = status_three_color
          } else if (item.sqjd00.includes("通过")) { //不通过
            item.themeColors = status_one_color
          } else { //进行中
            item.themeColors = status_two_color
          }
        })
      })
    },
    // 获取补贴申报类型
    getApplyType(value) {
      const data = this.applyTypeDict.find(item => item.value === value)
      return data.label
    },
    // 查看详情
    handleDetails(data) {
      console.log(data, "查看详情")
      const {id} = data
      this.$router.push({path: "/skill-subsidies-handle", query: {pageType: "detail", id}})
    },
    // 修改
    handleEdit(data) {
      const {id} = data
      this.$router.push({path: "/skill-subsidies-handle", query: {pageType: "edit", id}})
    },
    // 删除
    handleDelete(data) {
      this.$dialog
        .confirm({
          title: "提示",
          message: "您确定删除",
          showCancelButton: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        })
        .then(() => {
          const {id, aac003} = data
          const params = {
            serviceName: "xytjzbt_del",
            id,
            aac003
          }
          commonApi.proxyApi(params).then(res => {
            this.$toast("删除成功！")
            this.queryList()
          })
        })      
    }
  }
}
</script>

<style lang="less" scoped>
.labor-supervision-record {
  .info-container {
    background: @background_gray_color;  
    /deep/.info-box {
      .y-title .content .more {
        position: relative;
        &::before {
          content: ' ';
          width: 8px;
          height: 8px;
          border-radius: 4px;
          // background: @main_color;
          position: absolute;
          top: 50%;
          left: -25%;
          transform: translate(0,-50%);
        }
      }
      .van-cell-group .van-cell {
        .van-cell__title {
          flex: auto;
          width: 30%;
        }
        .van-cell__value {
          flex: auto;
          width: 70%;
        }
      }
    }
    /deep/.handle-cell .van-cell__value {
      color: @main_color !important;
    }    
  } 
  .evaluate-popup {
    width: 80% !important;
    border-radius: 8px;
    .evaluate-box {
      .evaluate-title {
        text-align: center;
        font-size: 16px;
        margin: 16px 0;
      }
      .evaluate-button {
        padding: 12px 0;
        .van-button--primary {
          margin-left: 8px;
        }
      }
      /deep/.van-radio-group {
        padding: 4px 16px;
        font-size: 14px;
      }
      /deep/.van-field-textarea{
        font-size: 14px;
      }
    }
  }
}
 
</style>