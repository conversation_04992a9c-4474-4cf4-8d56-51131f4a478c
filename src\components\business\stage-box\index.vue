<!--
 * @Description: 驿站盒子
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="stage-box">
    <div class="item">
      <div class="item-top">
        <div class="item-top-left">
          <p class="name">
            <span>{{ stageData.yzmc00 }}</span>
            <span class="rang">{{ formatRang(stageData.jl0000) }}</span>
          </p>
          <p class="text">驿站联系人：{{ stageData.glry00 }}</p>
          <p class="text">联系电话：{{ stageData.aae005 }}</p>
          <p class="text">网点地址：{{ stageData.yzdz00 }}</p>
          <div class="hour-icon flex-c-s" v-if="stageData.kfsj00">
            <img src="@pic/life-service/<EMAIL>" alt="" />
            <span>{{ stageData.kfsj00 }}</span>
          </div>
        </div>
        <div class="item-top-right">
          <div
            class="handle-box navigate-box"
            @click="handleNavigate(stageData)"
          >
            <img src="@pic/common/navigate.svg" alt="" />
            <span>到这去</span>
          </div>
          <div class="handle-box call-box" v-if="stageData.aae005">
            <a :href="`tel:${stageData.aae005}`">
              <img class="phone-icon" src="@pic/common/call.svg" alt="" />
            </a>
            <span>电话</span>
          </div>
        </div>
      </div>
      <div class="item-bottom">
        <div class="service-title">服务内容：</div>
        <div class="service-list flex-c-s">
          <div class="service" v-for="(e, v) in stageData.serviceList" :key="v">
            <img :src="e.imgUrl" alt="" />
            <span>{{ e.label }}</span>
          </div>
        </div>
        <div class="service-title title-margin">其它:</div>
        <div class="service-list flex-c-s">
          <span class="other-service">{{ stageData.fwnrqt || "无" }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { openLocation } from "@/utils/wechat"
import { cloneDeep } from "lodash"

export default {
  name: "stage-box",
  props: {
    stageInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      // 服务内容列表
      serviceList: [
        {
          label: "休息",
          value: "01",
          imgUrl: "@/assets/imgs/trade-union-service/service1.svg"
        },
        {
          label: "饮水",
          value: "02",
          imgUrl: "@/assets/imgs/trade-union-service/service1.svg"
        },
        {
          label: "如厕",
          value: "03",
          imgUrl: "@/assets/imgs/trade-union-service/service1.svg"
        },
        {
          label: "充电",
          value: "04",
          imgUrl: "@/assets/imgs/trade-union-service/service1.svg"
        },
        {
          label: "急救药箱",
          value: "05",
          imgUrl: "@/assets/imgs/trade-union-service/service1.svg"
        },
        {
          label: "求职招聘",
          value: "07",
          imgUrl: "@/assets/imgs/trade-union-service/service1.svg"
        },
        {
          label: "法律援助",
          value: "08",
          imgUrl: "@/assets/imgs/trade-union-service/service1.svg"
        },
        {
          label: "劳动仲裁",
          value: "09",
          imgUrl: "@/assets/imgs/trade-union-service/service1.svg"
        },
        {
          label: "阅读",
          value: "10",
          imgUrl: "@/assets/imgs/trade-union-service/service1.svg"
        },
        {
          label: "纠纷调解",
          value: "11",
          imgUrl: "@/assets/imgs/trade-union-service/service1.svg"
        }
      ]
    }
  },
  computed: {
    stageData() {
      return this.formatServiceList(this.stageInfo)
    }
  },
  methods: {
    // 格式化服务内容数据
    formatServiceList(data) {
      const list = data?.fwnr00?.split(";") || []
      const serviceList = cloneDeep(this.serviceList)
      data.serviceList = serviceList
        .filter((item) => list.includes(item.value))
        .map((item) => {
          return {
            ...item,
            imgUrl: require(`@/assets/imgs/trade-union-service/service${item.value}.svg`)
          }
        })
      return data
    },
    // 格式化距离
    formatRang(value) {
      let text = ""
      if (value && value > 0) {
        text = `距您${Math.ceil(value)}m`
      }
      return text
    },

    // 到这去
    handleNavigate(item) {
      const { zdwd00: lat, zdjd00: lng, yzdz00: address, yzmc00: name } = item
      openLocation({ lat, lng, address, name }) //微信查看地图SDK
    }
  }
}
</script>

<style lang="less" scoped>
.stage-box {
  overflow: auto;
  background: #ffffff;
  border-radius: 8px;
  .item {
    padding: 8px 16px 16px;
    &:not(:last-child) {
      border-bottom: 1px solid #ebebeb;
    }
    &-top {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      &-left {
        flex: 1;
        .name {
          font-weight: bold;
          font-size: 16px;
          color: #333333;
          line-height: 22px;
          margin-bottom: 6px;
          .rang {
            font-size: 12px;
            color: @main_color;
            margin-left: 4px;
            line-height: 22px;
            font-weight: normal;
          }
        }
        .text {
          font-size: 12px;
          color: #666666;
          line-height: 18px;
        }
        .hour-icon {
          margin-top: 10px;
          & > img {
            width: 13px;
            height: 14px;
          }
          & > span {
            font-size: 12px;
            color: #999999;
            line-height: 18px;
            margin-left: 4px;
          }
        }
      }
      &-right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-left: 12px;
        .handle-box {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          img {
            width: 32px;
            height: 32px;
          }
          & > span {
            margin-top: 4px;
            font-size: 10px;
            color: #999999;
            line-height: 14px;
            text-align: center;
          }
          &.call-box {
            margin-top: 8px;
            a {
              width: 32px;
              height: 34px;
            }
          }
        }
      }
    }
    &-bottom {
      .service-title {
        font-size: 12px;
        color: #666666;
        line-height: 18px;
        margin-top: 12px;
        &.title-margin {
          margin-top: 0;
        }
      }
      .service-list {
        margin-top: 12px;
        .service {
          width: calc((100% - 3 * 14px) / 4);
          height: 52px;
          border-radius: 4px;
          border: 1px solid #eeeeee;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin-bottom: 10px;
          &:not(:nth-child(4n + 1)) {
            margin-left: 14px;
          }
          & > img {
            width: 16px;
            height: 16px;
          }
          & > span {
            font-size: 12px;
            color: #666666;
            line-height: 18px;
            text-align: center;
            margin-top: 2px;
          }
        }
        .other-service {
          font-size: 12px;
          color: #bd1a2d;
          line-height: 18px;
          text-align: left;
        }
      }
    }
  }
}
</style>
