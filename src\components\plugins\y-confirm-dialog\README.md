<!--
 * @Description:
 * @Version: 0.1
 * @Autor: yjm
 * @LastEditors: lzx
 * @Date: 2020-07-07 15:50:33
 * @LastEditTime: 2020-07-28 16:34:34
-->

# y-confirm-dialog 确认弹窗

### 介绍

符合医保 ui 规范的提示确认弹窗

### 引入

```js
import Vue from 'vue';
import { YConfirmDialog } from '@ylz/vant';

Vue.use(YConfirmDialog);
```

## 代码演示

### 基础弹窗

```html
<!-- 调用弹窗提示 -->
<y-confirm-dialog
  :popShow.sync="dialogShow"
  confirmText="确 定"
  content="请确保备案人本人进行签名，以免无法通过备案"
  @confirm="confirm"
>
</y-confirm-dialog>
```

```js
export default {
  name: 'confirm-dialog-demo',
  data() {
    return {
      //是否展示弹窗
      dialogShow: false,
    };
  },
  methods: {
    confirm(val) {
      console.log('执行确认操作');
    },
  },
};
```

### 自定义弹窗

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| popShow | 是否显示弹窗 | \_boolean | `true` |
| confirmText | 确认按钮文案定义 | _string_ | `直接提交` |
| showClose | 是否展示关闭“x”按钮 | _boolean_ | `false` |
| title | 弹窗标题 | _string_ | `温馨提示` |
| contentWidth | 弹窗内容展示区域宽度 | _string_ | `80%` |
| content | 弹窗内容区域展示内容 | _string_ | `提交时附加说明图片，可帮助您更快地解决问题。` |
| cancelText | 确认按钮文案定义 | _string_ | `取消` |

### Slots

| 参数           | 说明                   |
| -------------- | ---------------------- |
| dialog-header  | 自定义弹窗头部模块     |
| dialog-content | 自定义弹窗中间内容区域 |
| dialog-footer  | 自定义弹窗底部内容     |
