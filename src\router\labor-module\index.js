
/*
 * @Description: 劳动仲裁
 * @Version: 0.1
 * @Autor: hwx
 */

export default [
  /****  劳动仲裁 ****/
  {
    path: "/labor-arbitrate-record",
    name: "劳动仲裁智慧调解申请",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborArbitrateModule" */"@/views/labor-module/labor-arbitrate/labor-arbitrate-record")
  },
  {
    path: "/labor-arbitrate-handle",
    name: "新增（个案）",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborArbitrateModule" */"@/views/labor-module/labor-arbitrate/labor-arbitrate-handle")
  },
  {
    path: "/add-apply-people",
    name: "添加预申请人",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborArbitrateModule" */"@/views/labor-module/labor-arbitrate/add-apply-people")
  },
  {
    path: "/add-applied-people",
    name: "添加被申请人",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborArbitrateModule" */"@/views/labor-module/labor-arbitrate/add-applied-people")
  },
  {
    path: "/add-evidence-list",
    name: "添加证据清单",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborArbitrateModule" */"@/views/labor-module/labor-arbitrate/add-evidence-list")
  },
  {
    path: "/add-arbitrate",
    name: "添加仲裁诉求",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborArbitrateModule" */"@/views/labor-module/labor-arbitrate/add-arbitrate")
  },
  {
    path: "/add-arbitrate/view-case",
    name: "结案查看",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborArbitrateModule" */"@/views/labor-module/labor-arbitrate/view-case")
  },

  /****  劳动仲裁 ****/
  {
    path: "/labor-protect",
    name: "我要维权",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect")
  },
  {
    path: "/labor-protect-record",
    name: "人民调解",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-record")
  },
  {
    path: "/labor-protect-handle",
    name: "人民调解申请",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-handle")
  },
  {
    path: "/labor-protect-revoke",
    name: "撤回申请",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-revoke")
  },
  {
    path: "/labor-protect-upload",
    name: "材料上传",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-upload")
  },
  {
    path: "/labor-protect-case",
    name: "结案查看",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-case")
  },
  {
    path: "/labor-protect-open",
    name: "在线调解",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-open")
  },
  {
    path: "/labor-protect-verify",
    name: "人脸识别认证",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-verify")
  },
  {
    path: "/labor-protect-verify-success",
    name: "人脸识别认证成功",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-verify-success")
  },
  {
    path: "/labor-protect-detail",
    name: "调解进展（调解中）",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-detail")
  },
  {
    path: "/labor-protect-more",
    name: "更多服务",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-more")
  },
  {
    path: "/labor-protect-writ",
    name: "文书/笔录",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-detail/writ")
  },
  {
    path: "/labor-protect-proof",
    name: "证据材料",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/labor-protect-detail/proof")
  },
  {
    path: "/labor-protect-guide",
    name: "调解指南",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborSupervisionModule" */"@/views/labor-module/labor-protect/labor-protect-guide")
  },
  {
    path: "/labor-protect-message",
    name: "留言",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborSupervisionModule" */"@/views/labor-module/labor-protect/labor-protect-message")
  },
  {
    path: "/labor-protect-message/detail",
    name: "留言详情",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborSupervisionModule" */"@/views/labor-module/labor-protect/labor-protect-message/detail")
  },
  {
    path: "/labor-protect-message/add",
    name: "新增留言",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborSupervisionModule" */"@/views/labor-module/labor-protect/labor-protect-message/add")
  },
  {
    path: "/labor-protect-typical",
    name: "典型案例",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborSupervisionModule" */"@/views/labor-module/labor-protect/labor-protect-typical")
  },
  /****  劳动监察 ****/
  {
    path: "/labor-supervision-record",
    name: "劳动监察",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborSupervisionModule" */"@/views/labor-module/labor-supervision/labor-supervision-record")
  },
  {
    path: "/labor-supervision-handle",
    name: "劳动监察申请",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborSupervisionModule" */"@/views/labor-module/labor-supervision/labor-supervision-handle")
  },
  {
    path: "/labor-supervision-revoke",
    name: "撤回申请",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborSupervisionModule" */"@/views/labor-module/labor-supervision/labor-supervision-revoke")
  },
  {
    path: "/labor-supervision-upload",
    name: "材料上传",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborSupervisionModule" */"@/views/labor-module/labor-supervision/labor-supervision-upload")
  },
  {
    path: "/labor-supervision-case",
    name: "结案查看",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborSupervisionModule" */"@/views/labor-module/labor-supervision/labor-supervision-case")
  },
  /**** 地图服务 ****/
  {
    path: "/map-service",
    name: "地图服务",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborModule" */"@/views/labor-module/map-service")
  },
  {
    path: "/map-service-details",
    name: "地图服务",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborModule" */"@/views/labor-module/map-service/map-service-details")
  },

  /**** 在线学习 ****/
  {
    path: "/on-line-study",
    name: "在线学习",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "personInfoModule" */"@/views/skill-module/on-line-study-modules")
  },
  {
    // TODO 删除这个测试demo
    path: "/meeting-demo-step1",
    name: "视频会议demo1",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/meeting-demo/step1.vue")
  },
  {
    // TODO 删除这个测试demo
    path: "/meeting-demo-step2",
    name: "视频会议demo2",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "laborProtectModule" */"@/views/labor-module/labor-protect/meeting-demo/step2.vue")
  }
]
