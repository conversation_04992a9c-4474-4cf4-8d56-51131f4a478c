<!--
 * @Description: 在线学习
 * @Author: wujh
 * @date: 2024/5/22 15:18
 * @LastEditors: Please set LastEditors
-->
<template>
  <div class="on-line-study-container">
    <p class="title">新就业形态云课堂</p>
    <p class="sub-title">知识共享 技能提升</p>
    <van-sticky>
      <div class="tabs-container">
        <van-tabs v-model="active" @click="onClick">
          <template  v-for="(item, key) in menuList">
            <van-tab
              v-if="!item.noShow"
              :key="key"
              :title="item.gzmc00"
              :name="item.gzbm00"
            />
          </template>
        </van-tabs>
          <p class="more-flag" @click="handleMore()">更多</p>
      </div>
    </van-sticky>

    <template v-if="videoList.length > 0">
        <van-list
          v-model="loading"
          :finished="finished"
          :finished-text="videoList?.length > 0 ? '没有更多了' : ''"
          :immediate-check="false"
          @load="onLoad"
        >
          <van-grid :column-num="2">
            <van-grid-item v-for="(subItem, subkey) in videoList" :key="subkey + 'videoMap'" @click="handleCard(subItem)">
              <div class="image-wrap">
                <van-image v-if="subItem.sptp00" :src="`data:image/jpeg;base64,${subItem.sptp00}`" />
              </div>
              <p class="item-title text-ellipsis-2">{{ subItem.gzspmc }}</p>
              <div class="item-sub-title">
                <span class="text-ellipsis">{{ subItem.sply00 }}</span>
                <span class="count">
                  <van-icon name="eye-o" />
                  {{ subItem.js0000 }}
                </span>
              </div>
            </van-grid-item>
          </van-grid>
        </van-list>
    </template>
    <y-empty v-else />
    <van-action-sheet
      v-model="sheetVisible"
      title="全部工种"
      class="sheet-container"
    >
      <div class="content">
        <div
          v-for="(item, key) in menuList"
          :key="key" class="menu-button"
          :class="[{'active': active === item.gzbm00 }, {'transparent': item.noShow }]"
          @click="!item.noShow && (active = item.gzbm00)"
        >
          {{ item.gzmc00 }}
        </div>
      </div>
      <div class="footer-container">
        <van-divider />
        <div class="footer">
          <van-button size="small" round @click="handleCancel">取消</van-button>
          <van-button size="small" round type="info" @click="handleSubmint()">确定</van-button>
        </div>
      </div>
    </van-action-sheet>
    <register-info :visible="resisterInfoVisible" :list="menuList" />
  </div>
</template>

<script>
import { commonApi } from "@/api"
import YEmpty from "@/components/global/y-empty"
import detect from "@/utils/detect"
import { isEmpty as _isEmpty } from "lodash"
import RegisterInfo from "./register-info"

export default {
  name: "on-line-study",
  components: {
    YEmpty,
    RegisterInfo
  },
  data(){
    return {
      resisterInfoVisible: false, // 意愿弹窗
      userType: "", // 人员类型
      active: "",
      sheetVisible: false,
      searchForm: {
        page: 1,
        size: 12
      },
      total: 0,
      menuList: [],
      videoList: [],
      loading: false,
      finished: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init(){
      const result = await this.checkStudyuserFn()
      if (result){
        this.resisterInfoVisible = true
        // // 未备案
        // this.$toast("没有访问权限，请做人员备案后再访问！")
        // this.$router.go(-1)
        return
      }

      this.findXytgzxx()
    },
    async checkStudyuserFn(){
      const { zjhm00 } = {...this.$sessionUtil.getItem("userInfo")}
      return await commonApi.proxyApi({
        serviceName: "xytPerson_checkStudyuser",
        ...{
          aac002: zjhm00
        }
      }).then((res) => {
        this.userType = res?.map?.data || ""
        // 1已备案，0未备案
        return res?.msg === "0"
      }).catch(() => {
        return true
      })
    },

    findXytgzxx(){
      commonApi.proxyApi({
        serviceName: "xytJnpx_findAllXytGz",
        gzmc00: "",
        page: 1,
        size: 500
      }).then(res => {
        const sortList = ["9999999999", "6666666666", "8888888888", "7777777777"]
        const { rows: data } = res.map?.data || {}

        if (data.length % 2 === 1){
          data.push({
            noShow: true
          })
        }

        // 按sortList排序
        data.forEach((item, index) => {
          sortList.forEach((e, v) => {
            if (item.gzbm00 === e) {
              item.sort = v + 1
            }
          })
          !item.sort && (item.sort = index + 10)
        })
        data.sort((a, b) => a.sort - b.sort)
        
        data.unshift({gzmc00: "全部", gzbm00: "all"})
        this.menuList = data
        this.active = this.menuList[0].gzbm00
        this.findSpxxByPageFn()
      }).catch((err) => {
        console.error("findXytgzxx", err)
      })
    },

    findSpxxByPageFn(){
      this.loading = true
      commonApi.proxyApi({
        serviceName: "xytJnpx_findSpxxByPage",
        ...{
          gzbm00: this.active === "all" ? "" : this.active,
          spzt00: "001"
        },
        ...this.searchForm
      }).then(res => {
        const { rows = [], total = 0 } = res?.map?.data
        if (Number(total) === 0){
          this.finished = true
          this.videoList = []
        }

        if (_isEmpty(rows)){
          this.finished = true
          this.isEmpty = true
        }

        if (this.searchForm.page + "" === "1"){
          this.videoList = rows
        } else {
          this.videoList = this.videoList.concat(rows)
        }

        this.finished = this.videoList.length >= Number(total) // 根据结果修改当前的结束状态
      }).catch((err) => {
        console.error("findXytgzxx", err)
      }).finally(() => {
        this.loading = false
      })
    },
    handleCancel(){
      this.sheetVisible = false
    },
    handleSubmint(){
      this.findSpxxByPageFn()
      this.handleCancel()
    },
    handleMore(){
      this.sheetVisible = true
    },
    onClick(){
      this.searchForm.page = 1
      this.findSpxxByPageFn()
    },
    handleCard(item){
      commonApi.proxyApi({
        serviceName: "xytJnpx_saveBfjl",
        ...{
          gzsp01: item.gzsp01,
          bfqd00: "002"
        }
      }).then((res) => {
        console.log(res)
        item.js0000 = Number(item.js0000) + 1
      }).catch((err) => {
        console.error(err)
        return false
      })
      const {xm0000: aac003, zjhm00: aac002 } = this.$sessionUtil.getItem("userInfo")
      commonApi.proxyApi({
        serviceName: "xytJnpx_getZxxxURL",
        ...{
          aac002,
          aac003,
          ly0000: "1", // 来源：1微信 2PC
          rylx00: this.userType,
          gzsp01: item.gzsp01,
          gzspmc: item.gzspmc
        }
      }).then((res) => {
        if (!res.msg){
          this.$toast("无对应视频")
          return
        }
        
        if (detect.isIOS){
          window.location.href = res.msg
        } else {
          window.open(res.msg)
        }
      }).catch((err) => {
        console.error("findXytgzxx", err)
      }).finally(() => {
        this.loading = false
      })
    },
    onLoad(){
      console.log("onLoad")
      this.finished = true
      this.searchForm.page += 1
      this.findSpxxByPageFn()
    }
  }
}
</script>

<style scoped lang="less">
.on-line-study-container{
  position: relative;
  min-height: 100vh;
  background-image: url("~@pic/on-line-study-modules/bg.png");
  background-repeat: no-repeat;
  background-size: contain;
  padding: 20px 16px 40px;
  .title{
    position: absolute;
    top: 34px;
    left: 26px;
    font-size: 30px;
    font-weight: bold;
    color: #3674E8;
    line-height: 42px;
    text-align: left;
  }
  .sub-title{
    position: absolute;
    top: 82px;
    left: 26px;
    font-size: 14px;
    color: #666666;
    line-height: 20px;
    text-align: left;
  }
  .tabs-container{
    position: relative;
    margin-top: 136px;
    background: #FFFFFF;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    overflow: hidden;
    .more-flag{
      position: absolute;
      right: 0;
      top: 14px;
      width: 38px;
      height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: #BD1A2D;
      text-align: center;
      background-color: #fff;
    }

    /deep/.van-tabs{
      &__wrap{
        height: 48px;
      }
      .van-tab{
        font-weight: 400;
        font-size: 16px;
        color: #333;
        line-height: 16px;
        &--active{
          font-weight: 600;
          font-size: 16px;
          color: #333;
          line-height: 16px;
          text-align: left;
        }
      }

      &__nav{
        padding-right: 50px;
      }
      &__line{
        width: 40px !important;
        height: 4px;
      }
    }
  }

  /deep/.van-grid{
    width: 100%;
    background-color: #FFF;
    .van-grid-item{
      border: none;
      &__content{
        padding-bottom: 0;
        box-shadow: 0 2px 4px 0 rgba(0,0,0,0.04);
        border-radius: 6px;
        .image-wrap{
          width: 100%;
          height: 88px;
          background: #E8F4FF;
          box-shadow: inset 0 0 4px 0 #D9ECFD;
          border-radius: 6px 6px 0 0;
          overflow: hidden;
          .van-image{
            width: 100%;
            height: 100%;
          }
        }
        .item-title{
          width: 100%;
          font-size: 14px;
          color: #333;
          //height: 50px;
          line-height: 20px;
          max-height: 40px;
          min-height: 40px;
          font-weight: 600;
          text-align: left;
          //padding-top: 8px;
        }
        .item-sub-title{
          display: flex;
          justify-content: space-between;
          width: 100%;
          font-size: 12px;
          color: #666;
          line-height: 20px;
          padding-top: 8px;
          padding-bottom: 8px;
          .count{
            display: flex;
            align-items: center;
            .van-icon{
              margin-right: 4px;
            }
          }
        }
        &.van-hairline{
          &::after{
            border: none;
          }
        }
      }
    }
    &.van-hairline--top{
      &::after{
        border: none;
      }
    }
  }
  .van-sticky--fixed{
    .tabs-container{
      margin-top: 0;
      .van-tab__pane{
        padding: 0 12px;
      }
    }
  }

  .sheet-container{
    .van-action-sheet__header{
      padding-top: 16px;
      padding-left: 16px;
      text-align: left;
      font-weight: 600;
      font-size: 16px;
      color: #333;
      line-height: 22px;
      .van-action-sheet__close{
        top: 16px;
        color: #979797;
        font-size: 18px;
      }
    }
    .van-action-sheet__content{
      padding-bottom: 60px;
      overflow-y: auto;
      .content{
        display: flex;
        flex-flow: wrap;
        .menu-button{
          width: 160px;
          height: 40px;
          line-height: 40px;
          background: #EEEEEE;
          border-radius: 22px;
          min-width: 40%;
          text-align: center;
          margin: 10px auto;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          &.active{
            background: #F8E8EA;
            color: #BD1A2D;
          }
          &:nth-child(2n){
            margin-left: 0;
          }
          &.transparent{
            opacity: 0;
          }
        }
      }
      .footer-container{
        position: fixed;
        bottom: 0;
        width: 100%;
        background-color: #fff;
        .van-divider {
          margin-top: 0;
          margin-bottom: 8px;
        }
        .footer{
          display: flex;
          align-items: center;
          padding-bottom: 8px;
          .van-button{
            width: 160px;
            height: 40px;
            margin: auto;
          }
        }
      }
    }
  }
}
</style>