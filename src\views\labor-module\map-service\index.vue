<!--
 * @Description: 地图服务
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="map-service">
    <div class='search-container'>
      <div class="search-box flex-c-c">
        <img class="search-icon" src="@pic/life-service/<EMAIL>" alt="" />
        <van-field
          v-model="yzmc00"
          input-align="left"
          placeholder="请输入名称"
          :border="false"
          clearable
        />
        <span class="search-btn" @click="findDa12ByPage">搜索</span>
      </div>
    </div>

    <div class="tabs-box">
      <van-tabs v-model="activeName" @change="findDa12ByPage">
        <van-tab v-for="(item, key) in bannerList" :key="key" :title="item.title" :name="item.name"></van-tab>
      </van-tabs>
    </div>

    <div class='banner-box'>
      <img :src="require(`@pic/labor-protect/service-${activeName}@2x.png`)" alt="">
    </div>

    <div v-if="serviceList.length > 0" ref="serviceList" class='service-list'>
      <div class='service-item flex-c-s' v-for="item in serviceList" :key="item.daz012">
        <img :src="require(`@pic/labor-protect/service-${activeName}-<EMAIL>`)" alt="">        
        <div class='item-center'>
          <p class="unit-name xz-ellipsis">{{item.yzmc00}}</p>
          <p v-if="item.aae005" class="unit-desc xz-ellipsis">电话：{{item.aae005}}</p>       
          <p class="unit-desc" :class="item.aae005 ? 'xz-ellipsis' : 'xz-ellipsis-2'">地址：{{item.yzdz00}}</p>
        </div>
        <div class='item-right'>
          <img src="@pic/common/address-icon2x.png" alt="" @click="handleViewDetails(item)">
          <a v-if="item.aae005" :href="`tel:${item.aae005}`">
            <img class="phone-icon" src="@pic/common/<EMAIL>" alt="">
          </a>
        </div>
      </div>
    </div>
    <y-empty v-else></y-empty>
  </div>
</template>

<script>
import { commonApi } from "@/api"

//001环卫爱心驿站，002近邻图书馆，003调解组织，004法律援助，005仲裁机构
const typeMap = {
  "mediate": "003",
  "law": "004",
  "arbitration": "005"
}

export default {
  name: "map-service",

  data() {
    return {
      yzmc00: "",
      activeName: "mediate",
      bannerList: [
        {
          title: "调解组织",
          name: "mediate"
        },
        {
          title: "法律援助",
          name: "law"
        },
        {
          title: "仲裁机构",
          name: "arbitration"
        }
      ],

      serviceList: [],
      listTotal: 10
    }
  },  
  async created() {
    const {type} = this.$route.query
    this.activeName = type

    this.findDa12ByPage()
  },
  methods: {
    //查询列表
    async findDa12ByPage() {      
      this.serviceList = []

      const {yzmc00} = this
      const ywlx00 = typeMap[this.activeName]
      const params = {
        serviceName: "xytHwyz_findDa12ByPage",
        yzmc00,
        ywlx00,
        page: 1,
        size: 10
      }
      const res1 = await commonApi.proxyApi(params)    
      const { total } = res1.map.data
      params.size = total
      
      const res2 = await commonApi.proxyApi(params)   
      const { rows } = res2.map.data
      this.serviceList = rows
    },
    // 查看地图详情
    handleViewDetails(item) {
      const {activeName: type} = this
      const {daz012} = item      
      this.$router.push({path: "/map-service-details", query: {type, daz012}})
    }    
  }
}
</script>

<style lang="less" scoped>
.map-service {
  .search-container {
    width: 100vw;
    height: 44px;
    background: #F6F6F6;
    position: relative;
    padding: 0 16px;
    .search-box {
      position: absolute;
      width: calc(100% - 32px);
      height: 36px;
      background: #ffffff;
      border-radius: 4px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 0 18px;
      .search-icon {
        width: 16px;
        height: 14px;
      }
      .van-field {
        flex: 1;
        padding: 0 10px;
        /deep/.van-icon-clear {
          font-size: 14px;
        }
        /deep/.van-field__control::-webkit-input-placeholder {
          font-size: 14px;
        }
      }
      .search-btn {
        font-size: 14px;
        color: #bd1a2d;
        display: inline-block;
        height: 100%;
        line-height: 36px;
      }
    }
  }
  .tabs-box {
    /deep/.van-tabs__line {
      bottom: 20px;
      width: 44px !important;
    }
  }
  .banner-box {
    width: 100vw;
    & > img {
      width: 100%;
    }
  }
  .service-list {    
    height: calc(100vh - 286px);
    overflow-y: auto;
    .service-item {
      width: 100%;
      padding: 18px 24px;
      position: relative;      
      &:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 24px;
        right: 24px;
        bottom: 0;
        height: 1px;
        background-color: #EEEEEE;
      }
      & > img {
        width: 98px;
        height: 80px;
      }
      .item-center {
        width: calc(100% - 136px);
        height: 80px;
        margin-left: 10px;
        .unit-desc {
          font-size: 12px;
          color: #999999;
          line-height: 17px;
          margin-top: 10px;
        }
        .unit-name {
          font-weight: bold;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
          margin-top: 4px;
        }
      }
      .item-right {
        width: 24px;
        height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-left: 4px;
        & > img {
          width: 100%;
        }
        .phone-icon {
          width: 100%;
          margin-top: 8px;
        }
      }
    }
    
  }
  
}
</style>