<!--
 * @Description: 申请信息
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="apply-info">    
    <van-form ref="baseForm" class="base-form" label-width="120" :disabled="pageType === 'detail'" @failed="onFailed" @submit="handleNext">
      <base-form ref="baseFormInfo" :businessType="businessType" :pageType="pageType"></base-form>

      <div class="button-box-more">
        <van-button plain type="info" @click="handleCancle" native-type="button">
          {{ pageType !== 'detail' ? '取 消' : '关 闭' }}
        </van-button> 

        <!-- @click="handleNext" -->
        <van-button round block type="primary" native-type="submit">
          下一步
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import BaseForm from "./components/base-form"

import {five_text_color} from "@/styles/theme/theme-params.less"

import {commonApi} from "@/api"
import {removeEmptyChildren} from "@/utils/common"

export default {
  name: "apply-info",
  components: {BaseForm},
  props: {
    businessType: {
      type: String,
      default: "xztj" //业务类型 默认行政调解
    }
  },  
  data() {
    return {
      
      // 标题
      colorMore: five_text_color,
      showBaseInfo: true, //申请人基本信息
      showUnitInfo: true, //被申请人人单位信息
      showRequestInfo: true, //申请人请求
      required: true,

      //案件类别
      showCase: false,
      caseValue: "",
      caseList: [], //案件类别列表

      // 单位名称
      showPicker: false, //单位名称弹窗
      unitName: "", //单位名称
      unitList: [] //单位列表

    }
  },
  watch: {
    async businessType() {
      await this.$nextTick()
      this.$refs["baseForm"].resetValidation()
    }
  },
  computed: {
    pageType() {
      return this.$route.query.pageType || ""
    }
  },
  methods: {
    confirmBusinessType(data) {
      const {text, value} = data
      this.formData.oprTypeName = text
      this.formData.oprType = value

      this.showBusinessType = false 
      console.log(this.formData, "this.formData111")     
    },
    
    // 查询案件类别
    getTreeAa10ByAaa100() {
      const params = {
        serviceName: "xytCommon_getTreeAa10ByAaa100",
        aaa100: "ABA002"
      }
      commonApi.proxyApi(params).then((res) => {
        const {data} = res.map

        const newData = removeEmptyChildren(data)
        this.caseList = this.formatCaseList(newData)
        
      })
    },
    // 案件类别数据格式化
    formatCaseList(arr) {
      if (arr?.length) {
        for (const i in arr) {
          arr[i].text = arr[i].aaa103
          arr[i].value = arr[i].aaa102
          this.formatCaseList(arr[i].children)
        }
      }
      return arr
    },

    // 查询单位信息
    handleSearchUnit() {
      const params = {
        serviceName: "xytCommon_findAb01NewByName",
        aab004: this.unitName
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "res")
        const { rows } = res.map.data
        this.unitList = rows.map(item => {
          return { text: item.aab004, value: item.aab001, aab503: item.aae004, aab505: item.aae005 }
        })  
        console.log(this.unitList, "this.unitList")
      })
    },

    // 确认选择单位
    handleConfirmUnit(data) {
      this.showPicker = false
      const { text: aab004, value: aab001, aab503, aab505 } = data
      this.$emit("updateFormData", {aab004, aab001, aab503, aab505})
    },

    // 表单校验失败
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },

    // 折叠表单
    changeFold({foldName, state}) {
      console.log(foldName, "foldName")
      console.log(state, "state")
      this[foldName] = state
    },

    // 保存 存储数据 返回
    handleSave() {
      this.$emit("handleSave", "info", this.materialNum)    
    },
    
    // 取消
    handleCancle() {
      window.history.go(-1)
    },

    // 下一步
    handleNext() {
      this.$emit("handleNext", 1)
    }
  }
}
</script>

<style lang="less" scoped>
.button-box {
  padding: 15px 0;
  width: 100%;
  display: flex;
  background-color: #fff;
  
  .van-button  {
    flex: 1;
    .van-button__content {
      color: @main_color;
    }
  }
}
.search-field {
  /deep/.van-field__control {
    text-align: left;
  }
}
</style>