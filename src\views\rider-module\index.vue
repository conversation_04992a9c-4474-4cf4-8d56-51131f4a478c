<!--
 * @Description: 骑手综合查询
 * @Version: 0.1
 * @Author: hwx
-->
<template>
  <div class="">
    <van-sticky>
      <van-tabs v-model="active">
        <van-tab v-for="(item, key) in tabList" :title="item.text" :name="item.id" :key="key">
        </van-tab>
      </van-tabs>
    </van-sticky>
    <div class="content-box">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        > 
        <div v-if="list.length">
          <div v-for="(item, index) in list" :key="index" class="item-box">
            <van-cell-group  :border="false" v-if="active==='iLlegalListRecords'" >
              <van-cell title="姓名" :value="item.aac003" :border="false" />
              <van-cell title="证件号码" title-class="short-label" :value="item.aac002" :border="false" />
              <!-- <van-cell title="违章类型" :value="formatData(item.wflx00, 'WFLX00')" :border="false" /> -->
              <van-cell title="违章地点" :value="item.ccf004" :border="false" />
              <van-cell title="违章原因" :value="item.ccf005" :border="false" />
              <van-cell title="违章时间" :value="dayFormat(item.ccf003, 'date')" :border="false" />
              <!-- <van-cell title="违章罚款" :value="item.ccf006" :border="false" /> -->
              <!-- <van-cell title="处罚信息" :value="item.ccf007" :border="false" /> -->
              <!-- <van-cell title="车牌号码" :value="item.ccf002" :border="false" /> -->
              <van-cell title="" icon="">
                <!-- 使用 right-icon 插槽来自定义右侧图标 -->
                <template #right-icon>
                  <van-button size="small" type="primary" round @click="showDetail(index)">查看平台反馈信息</van-button>
                </template>
              </van-cell>
              <van-cell-group  :border="false" v-if="index === plateInfo" >
                <y-title content="平台反馈信息" />
                <van-cell title="所属平台" :value="item.mtmc00" :border="false" />
                <van-cell title="电动车是否上牌" :value="formatData(item.ccf019, 'YES_NO')" :border="false" />
                <van-cell title="是否进行培训" :value="formatData(item.ccf020, 'YES_NO')" :border="false" />
                <van-cell title="企业惩戒类型" :value="formatData(item.cjlx00, 'CJLX00')" :border="false" />
                <van-cell title="反馈时间" :value="dayFormat(item.ccf027, 'datetime')" :border="false" />
              </van-cell-group>
            </van-cell-group>
            <van-cell-group  :border="false" v-if="active==='blackListRecords'" >
              <van-cell title="姓名" :value="personInfo.aac003" :border="false" />
              <van-cell title="证件号码" title-class="short-label" :value="personInfo.aac002" :border="false" />
              <van-cell title="进入名单时间" :value="dayFormat(item.ccf028, 'datetime')" :border="false" />
              <van-cell title="进入安全警示名单原因" :value="item.ccf029" :border="false" />
              <van-cell title="退出时间" :value="dayFormat(item.ccf034, 'datetime')" :border="false" />
              <van-cell title="学习进度" :border="false">
                <template>
                  <span v-if="item.ccf032" class="label" :class="{'label-finished': item.ccf032 === '1'}">{{ formatData(item.ccf032, 'FINISHED') }}</span>
                  <span v-else>无</span>
                </template>
              </van-cell>
              <van-cell title="考试情况" :border="false">
                <template>
                  <span v-if="item.ccf033" class="label" :class="{'label-finished': item.ccf033 === '1'}">{{ formatData(item.ccf033, 'FINISHED') }}</span>
                  <span v-else>无</span>
                </template>
              </van-cell>
              <div class='handle-btn-container flex-c-e'>
                <div class='handle-btn-box'>       
                  <van-button v-if="item.ccf032 !== '1' && item.ccf033 !== '1'" size="small" class="mr12" type="primary" round @click="handleLearn(item)">去学习</van-button>
                  <van-button v-if="item.ccf032 !== '1' && item.ccf033 !== '1'" size="small" class="mr12" type="primary" round @click="handleExam(item)">去考试</van-button>
                  <van-button size="small" round type="warning" @click="handleSeeQuery(item)">查看学习(考试)情况</van-button>                  
                </div>
                <div v-if="item.ccf035" class='handle-btn-box handle-btn-box-bottom'>
                  <van-button v-if="item.ccf035 === '01' && item.ccf032 !== '1' && item.ccf033 !== '1'"  size="small" round type="warning" :loading="syncLoading" @click="handleLearnSync(item)">同步学习记录</van-button>                      
                  <van-button v-if="item.ccf035 === '02' && item.ccf032 !== '1' && item.ccf033 !== '1'" size="small" round type="warning" :loading="syncLoading" @click="handleExamSync(item)">同步考试记录</van-button>
                </div>   
              </div>
              
              <div class='tips'>
                <p>温馨提示：</p>
                <p class="text-indent">学习或考试完成后，系统会定时更新学习或考试记录，这期间存在时间差，若需提前获取学习或考试记录，可以点击“同步”按钮，获取学习或考试记录。</p>
              </div>           
            </van-cell-group>
          </div>
        </div>
          <y-empty v-else></y-empty>
        </van-list>
      </van-pull-refresh>

    </div>
  </div>
</template>

<script>
import { commonApi } from "@/api"
import { dayFormat } from "@/utils/dayjs"
import detect from "@/utils/detect"

const fromRouterList = [
  "/learn-exam-query",
  "/rider-online-study"
]

export default {
  name: "online-study",
  data() {
    return {
      loading: false,
      finished: false,
      refreshing: false,
      active: "iLlegalListRecords",
      tabList: [
        {id: "iLlegalListRecords", type: "pay", text: "违章记录"},
        {id: "blackListRecords", type: "all", text: "安全警示名单记录"}
      ],
      list: [],
      dictData: { // 字典数据
        YES_NO: [], // 是否
        GZPT00: [], // 工作平台
        AAB301_XM: [], // 工作地区
        CJLX00: [],
        WFLX00: [],
        FINISHED: []
      },
      plateInfo: -1,
      pageInfo: {
        page: 0,
        size: 10
      },
      personInfo: {
        aac002: "",
        aac003: ""
      },

      fromPath: "", //from页面路由
      syncLoading: false, //按钮加载
      syncMessage: "已同步成功，详情请点击“查看学习(考试)情况”进行查看！"
    }
  },
  computed: {
    formatData() {
      return (code, type) => {
        if (!code) {
          return ""
        }

        console.log(this.dictData, "this.dictData")
        const { aaa103 } = this.dictData[type]?.find(item => item.aaa102 === code) || {}
        return aaa103 || ""
      }
    }
  },
  watch: {
    active(val) {
      this.scrollToTop(0, 0, "auto") //滚动到顶部
      this.list = []
      this.active = val
      this.onRefresh()
    }
  },
  created() {
    this.getDicData()  
  },
  methods: {
    developAlert() {
      this.$dialog.alert({
        title: "温馨提示",
        message: "功能正在开发中，敬请期待！",  
        theme: "round-button",
        showConfirmButton: true,
        width: "78%"
      })
    },
    dayFormat,
    showDetail(index) {
      if (this.plateInfo === index) {
        this.plateInfo = -1
        return
      }
      this.plateInfo = index
    },
    // 下拉刷新
    onRefresh() {
      // 清空列表数据
      this.list = []
      this.finished = false
      // 重新加载数据      
      this.pageInfo.page = 0
      this.loading = true
      this.onLoad() 
    },
    // 获取列表数据
    onLoad() {
      this.pageInfo.page++
      this.queryData()
    },
    // 获取字典数据  
    async getDicData() {
      await commonApi.proxyApi({ 
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["YES_NO", "CJLX00", "WFLX00", "FINISHED"]
      }).then(res => {
        const { data } = res?.map
        this.dictData = Object.assign(this.dictData, data)
      })
    },
    queryData() {
      const serviceName = this.active === "iLlegalListRecords" ? "xytQsgl_findCf01WfPerson" : "xytQsgl_findCf05Person"
      // const serviceName = "xytQsgl_findCf05Person"
      commonApi.proxyApi({
        serviceName,
        ...this.pageInfo
      }).then((res) => {
        if (this.refreshing) { // 清空列表数据
          this.list = []
          this.refreshing = false
        }
        
        const { rows, total } = res?.map?.data
        if (rows?.length) {
          this.personInfo = rows[0] || {}
        }

        this.list = [...this.list, ...rows]
        this.loading = false
        if (this.list.length >= total) {
          this.finished = true
        }
        console.log(this.list, "listInfo")
      }).finally((err) => {
        console.error(err)
      })
    },
    // 去学习（去学习去考试二选一）
    async handleLearn(data) {
      const {ccf035, cf05id} = data
      const message = ccf035 === "02" ? "您已选择“考试方式”，无法变更，请重新选择！" : "您本次将学习交通安全课程，学习完成后才能退出安全警示名单。点击下方【确定】后将无法更改，是否确认学习！"
      if (ccf035 === "02") { //已选择去考试
        this.$dialog.alert({
          title: "提示",
          message,
          theme: "round-button"
        })
      } else {
        const isAllowedExams = await this.checkAllowedExams(cf05id)        
        if (isAllowedExams) {
          this.$dialog.confirm({
            title: "提示",
            message,
            showCancelButton: true,
            confirmButtonText: "确认",
            cancelButtonText: "取消"
          }).then(() => {          
            this.$router.push({path: "/rider-online-study", query: {cf05id}})
          }) 
        }        
      }            
    },
    // 校验是否允许考试
    async checkAllowedExams(cf05id) {
      const params = {
        serviceName: "xytQsgl_findSpxxHmdByPage",
        cf05id,
        page: 1,
        size: 10
      }
      const res = await commonApi.proxyApi(params)
      if (res?.map?.data?.total === 0) {
        const message = "您当前没有可以学习的交通安全课程，请选择“去考试”！"
        this.$dialog.alert({
          title: "提示",
          message,
          theme: "round-button"
        })
        return false
      }

      return true
    },
    // 去考试（去学习去考试二选一）
    handleExam(data) {
      const {ccf035, aac002, cf05id } = data
      const message = ccf035 === "01" ? "您已选择“学习方式”，无法变更，请重新选择！" : "您本次将进行考试（及格分60分及以上），考试合格后才能退出安全警示名单，点击下方【确定】后将无法更改，是否确认学习？"
      if (ccf035 === "01") { //已选择去学习
        this.$dialog.alert({
          title: "提示",
          message,
          theme: "round-button"
        })
      } else {
        this.$dialog.confirm({
          title: "提示",
          message,
          showCancelButton: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        }).then(() => {          
          commonApi.proxyApi( //调用考试接口
            {
              serviceName: "xytKsxt_gotoKs",
              aac002, 
              cf05id
            }
          ).then((res) => {
            console.log(res, "考试res66")
            const url = res.msg
            if (!res.msg){
              this.$toast("无对应考试!")
              return
            }

            if (detect.isIOS){
              window.location.href = url
            } else {
              window.open(url)
            }
          })
        }) 
      } 
    },
    // 查看学习(考试)情况
    handleSeeQuery(data) {
      const {cf05id, ccf032, examId} = data
      this.$router.push({path: "/learn-exam-query", query: {cf05id, ccf032, examId}})
    },
    // 同步学习记录
    handleLearnSync(data) {
      const {cf05id} = data
      this.syncLoading = true
      commonApi.proxyApi({
        serviceName: "xytQsgl_syncCfXxjlbJob",
        cf05id
      }).then(res => {  
        this.onRefresh()
        this.$dialog.alert({
          title: "提示",
          message: this.syncMessage,
          theme: "round-button"
        })
      }).finally(res => {        
        this.syncLoading = false
      })
    },     
    // 同步考试记录
    handleExamSync(data) {
      const {examId} = data
      this.syncLoading = true
      commonApi.proxyApi({
        serviceName: "xytKsxt_syncKsCj",
        examId
      }).then(res => {  
        this.onRefresh()
        this.$dialog.alert({
          title: "提示",
          message: this.syncMessage,
          theme: "round-button"
        })
      }).finally(res => {
        this.syncLoading = false
      })
    }
  },
  beforeRouteEnter(to, from, next) {
    console.log(to, "to")
    console.log(from, "from")
    next(vm => { // vm 就是当前组件的实例相当于this     
      vm.fromPath = from.fullPath //记录fromPath
      const checkRouterPath = (fullPath, fromRouterList) => fromRouterList.some(path => fullPath.includes(path))
      if (checkRouterPath(vm.fromPath, fromRouterList)) { //从在线学习来
        vm.active = "blackListRecords"
      }
    })
  }
}
</script>

<style lang="less" scoped>
.content-box {
  min-height: calc(100vh - 40px);
  background: @background_gray_color;
  /deep/.y-title {
    margin-left: 16px;
  }
  .van-pull-refresh {
    .van-list {
      height: 100%;
    }
    .item-box {
      margin: 16px;
      padding: 0;
      background: @white_text_color;
      .short-label {
        flex: 0.8
      }
    }
  }
  .label {
    padding: 4px 16px;
    border-radius: 4px;
    color: #F5222D;
    border: 1px solid #F5222D;
    background: rgba(245,34,45,0.06);
    font-size: 14px;
    &-finished {
      color: #007EE4;
      border: 1px solid #007EE4;
      background: rgba(0,126,228,0.06);
    }
  }
  .handle-btn-container {
    .handle-btn-box {    
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;
      padding-left: 16px;
      padding-right: 16px;
      &:last-child {
        padding-left: 0;
      }
      &-bottom {
        justify-content: flex-end;
      }
    }     
  }
  .tips {
    display: inline-block;
    width: 100%;
    margin-top: 16px;
    font-size: 12px;
    color: @warn_color;
    line-height: 16px;
    padding: 0 16px 16px;
    .text-indent {
      margin-top: 4px;
      // text-indent: 2em;
    }
  }
}
</style>