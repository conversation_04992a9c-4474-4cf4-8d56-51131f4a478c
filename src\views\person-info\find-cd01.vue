<!--
 * @Description: 就业登记信息
 * @Author: wujh
 * @date: 2024/5/16 15:16
 * @LastEditors: wujh
-->
<template>
  <div class="card-container">
    <template v-if="!isEmpty">
      <van-list
        v-model="loading"
        :finished="finished"
        :finished-text="list?.length > 0 ? '没有更多了' : ''"
        :immediate-check="false"
        @load="onLoad"
      >
        <van-form
          ref="formData"
          v-for="(item, index) in list"
          :key="index"
          input-align="right"
          readonly
        >
          <van-field v-model="item.aae036" name="就业时间" label="就业时间" label-width="70px" readonly/>
          <van-field v-model="item.ccd017" name="失业时间" label="失业时间" label-width="70px" readonly/>
          <van-field class="long-van-field" v-model="item.aab004" name="单位名称" label="单位名称" label-width="70px" readonly/>
        </van-form>
      </van-list>
    </template>
    <div v-else class="empty-style">
      <img src="@/assets/imgs/common/<EMAIL>" alt="">
      <p class="tips">暂无数据</p>
    </div>
  </div>
</template>

<script>
import {commonApi} from "@/api"
import { isEmpty as _isEmpty } from "lodash"

export default {
  name: "find-ce10",
  data(){
    return {
      list: [],
      isEmpty: false,
      loading: false,
      finished: false,
      queryInfo: {
        aac002: "",
        page: 1,
        total: 0,
        size: 5
      }
    }
  },
  mounted() {
    const { zjhm00 } = {...this.$sessionUtil.getItem("userInfo")}
    this.queryInfo.aac002 = zjhm00
    this.searchDataFn(this.queryInfo)
  },
  methods: {
    searchDataFn(queryInfo){
      commonApi.proxyApi({
        serviceName: "xytCd01_queryJydjxx",
        ...queryInfo
      }).then((res) => {
        console.log("从业登记信息", { ...res })
        const { rows = [], total = 0 } = res?.map?.data
        if (Number(total) === 0){
          this.finished = true
          this.isEmpty = true
          this.list = []
        }

        if (_isEmpty(rows)){
          this.finished = true
        }

        if (this.queryInfo.page + "" === "1"){
          this.list = rows
        } else {
          this.list = this.list.concat(rows)
        }

        this.finished = this.list.length >= Number(total) // 根据结果修改当前的结束状态
      }).catch((err) => {
        console.error(err)
      }).finally(() => {
        this.loading = false
      })
    },
    onLoad(){
      console.log("onLoad")
      this.finished = true
      this.queryInfo.page += 1
      this.searchDataFn(this.queryInfo)
    }
  }
}
</script>

<style scoped lang="less">
.card-container{
  overflow: auto;
  height: 100vh;
  background-color: #F6F6F6;
  padding: 6px 10px 6px;
  /deep/.van-form{
    border-radius: 8px;
    overflow: hidden;
    &+.van-form{
      margin-top: 12px;
    }
  }

  .empty-style{
    width: 100%;
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
    &>img{
      width: 194px;
      height: 136px;
    }
    .tips{
      font-size: 16px;
      font-weight: 400;
      color: #909399;
      text-align: center;
    }
  }

  /deep/ .long-van-field {
    .van-field__control {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>