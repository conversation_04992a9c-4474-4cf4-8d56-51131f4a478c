<!--
 * @Description: 免赔规则
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="page-container pb15">

    <div class="shortcuts">
      <div class="item">
        <div class="short-wrapper" v-for="(item, index) in shortcuts" :class="sortcutClass(index)"  :key="index" @click="handleClick(index)">
          <div class="value">{{ item.value }}</div>
          <div class="title">{{ item.label }}</div>
        </div>
      </div>
      
      <div class="description" :class="arrowTop">
        {{ shortcuts[activeIndex].discription }}
      </div>
    </div>
  </div>  
</template>

<script>
export default {
  name: "special-rules",
  data() {
    return {
      activeIndex: 0,
      shortcuts: [
        {
          label: "免赔天数",
          value: "0",
          discription: "本产品中针对意外伤害住院津贴保险保障，每天赔偿限额60元，免赔0天，单次赔偿天数不超过30天，累计赔偿天数不超过30天。"
        },
        {
          label: "免赔金额（元）",
          value: "100元",
          discription: "本保险意外伤害医疗费用保障:厦门市社会医疗保险主管部门规定可以报销的合理且必要的医疗费用，扣除100元人民币绝对免赔后，在责任范围内按照100%的比例赔付。"
        },
        {
          label: "给付比例",
          value: "100%",
          discription: "本保险意外伤害医疗费用保障:厦门市社会医疗保险主管部门规定可以报销的合理且必要的医疗费用，扣除100元人民币绝对免赔后，在责任范围内按照100%的比例赔付。"
        }
      ]
    }
  },
  computed: {
    sortcutClass() {
      return (index) => {
        return index === this.activeIndex ? "short-wrapper-active" : ""
      }
    },
    arrowTop() {
      return `arrow-${this.activeIndex}`
    }
  },
  methods: {
    handleClick(index) {
      this.activeIndex = index
    }
  }

}
</script>

<style scoped lang="less">
.pb15 {
  padding-bottom: 15px;
}
.shortcuts {
  
  .item {
    display: flex;
    font-size: 16px;
    flex-wrap: wrap;
    justify-content: space-between;
    color: @main_text_color;
    & > :not(:last-child) {
      margin-right: 12px;
    }
  }
  .short-wrapper {
    flex: 1;
    border-radius: 10px;
    border: 1px solid #CECECE;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 0;
    .value {
      font-size: 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #FF3B37;
    }
    .title {
      margin-top: 11px;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #333333;
    }
    &-active {
      border-color: @ylb_color;
    }
  }
  .description {
    margin-top: 10px;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    background-color: #DCF8F3;
    line-height: 20px;
    padding: 15px 13px;
    position: relative;
    color: @ylb_color;
    top: 0;
    left: 0;
    width: 100%;
    border-radius: 4px;
    z-index: 1;
    &::before {
      content: ' ';
      position: absolute;
      top: 0;
      width: 18px;
      height: 18px;
      transform: translateY(-20%) rotate(45deg);
      background-color: #DCF8F3;
      z-index: -1;
    }
    
  }
  .arrow-0 {
    &::before {
      left: 10%;
    }
  }

  .arrow-1 {
    &::before {
      left: 40%;
    }
  }
  .arrow-2 {
    &::before {
      right: 10%;
    }
  }
  
}

</style>