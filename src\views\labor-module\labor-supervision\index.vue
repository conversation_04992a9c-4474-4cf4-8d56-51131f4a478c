<!--
 * @Description: 劳动监察
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-supervision">
    <div class="header-box">
      <img :src="require('@pic/labor-protect/<EMAIL>')" alt="">
    </div>
    <div class="info-container">
      <div class="business-container">
        <y-title content="业务办理" fontWeight="bold"></y-title>
        <div class="business-box flex-c-sb mt30 pl16 pr16">
          <div class="business-item flex-c-c-c" v-for="(item,index) in businessList" :key="index" @click="handleJump(item)">
            <img :src="require('@pic/labor-protect/<EMAIL>')" alt="">
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "labor-supervision",

  data() {
    return {
      businessList: [
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "人民调解",
          isOpen: true
        },
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "劳动监察",
          isOpen: false
        },
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "劳动仲裁",
          isOpen: false
        }
      ]
    }
  },
  methods: {
    handleJump(item) {
      if (!item.isOpen) {
        this.$toast("功能建设中,敬请期待！")
        return
      }
      this.$router.push("/labor-supervision-record")
    }
  }
}
</script>

<style lang="less" scoped>
.labor-supervision {
  .header-box {
    width: 100%;
    & > img {
      width: 100%;
    }
  }
  .info-container {
    padding: 24px 16px;
    .business-container {
      background-color: @white_bg_color;
      padding: 8px 18px 24px;
      border-radius: 4px;
      .business-box .business-item {
        & > img {
          width: 44px;
        }
        .item-title {
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
          margin-top: 12px;
          text-align: center;
        }
      }
    }
  }
}
</style>