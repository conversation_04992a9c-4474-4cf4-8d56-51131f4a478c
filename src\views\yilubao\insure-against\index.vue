<!--
 * @Description: 首页
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div>
    <component :is="activeComponent" @onInsure="onInsure"></component>
  </div>
</template>

<script>
import ProductPresentation from "@/views/yilubao/insure-against/product-presentation"
import InsureProcess from "@/views/yilubao/insure-against/insure-process/index"

export default {
  name: "insure-against",
  components: {
    ProductPresentation,
    InsureProcess
  },
  data() {
    return {
      activeComponent: "ProductPresentation"
    
    }
  },
  methods: {
    onInsure() {
      this.activeComponent = "InsureProcess"
    }
  }
}
</script>

<style lang="less" scoped>
</style>