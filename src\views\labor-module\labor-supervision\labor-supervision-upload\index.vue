<!--
 * @Description: 材料上传
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="submit-material">
    <div class="iframe-box">
      <iframe id="iframe" class="my-iframe" :src="iframeUrl" frameborder="0"></iframe>
    </div>

    <div class="button-box-more mt30">
      <van-button plain type="info" @click="$router.go(-1)">
        返 回
      </van-button>
    </div>    
  </div>
</template>

<script>
import {commonApi} from "@/api"
export default {
  name: "submit-material",
  props: {
    active: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      iframeUrl: "" //上传材料页面地址
    }
  },
  created() {
    const {abz200} = this.$route.query
    console.log(abz200, "abz200 query")
    this.getArchivesPhoneData(abz200) //获取外网材料电子档案
  },
  methods: {
    //获取外网材料电子档案
    getArchivesPhoneData(abz200) {
      const nwFlag = process.env.VUE_APP_NW_FLAG === "true"
      const params = {
        serviceName: "xytCommon_getArchivesPhoneData",
        aaa121: "JC0001", //业务编号
        dza001: "C4",
        modifyFlag: true, //修改标识
        dzd999: abz200, //业务受理ID
        nwFlag //	内网标识
      }
      commonApi.proxyApi(params).then((res) => {
        const { url } = res.map.data
        this.iframeUrl = url
      })
    }
  }
}
</script>

<style lang="less" scoped>
.submit-material {
  .iframe-box {
    height: 500px;
    width: 100vw;
    overflow: hidden;
    .my-iframe {
      width: 200%;
      height: 1100px;
      transform: scale(0.47);
      transform-origin: 0 0;
      margin-left: 12px;
    }
  }
  .button-box-more {
    background: @white_text_color;
  }
}

</style>