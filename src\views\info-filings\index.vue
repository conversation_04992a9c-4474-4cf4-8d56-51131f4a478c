<!--
 * @Description: 信息备案
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="info-filings">

    <!-- 顶部tab -->
    <business-tabs :tabList="tabList" @handleChangeTabs="handleChangeTabs" @handleAdd="handleAdd"></business-tabs>

    <!-- 信息列表 -->
    <div class="info-container">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">

        <van-list
          v-model="loading"
          :finished="finished"
          :finished-text="list?.length > 0 ? '没有更多了' : ''"
          @load="onLoad"
        >
          <div v-if="list?.length > 0">
            <info-box v-for="item in list" :key="item.cce010" title="平台名称" :moreText="getDictName(platformList, item.ptbm00)|| ' '">
              <template #cells>
                <van-cell v-if="item.ptbm00 !== '999'" title="录用企业名称" :value="item.aab004" :border="false" />
                <van-cell v-if="item.yglx00 === '003'" title="岗位" :value="getDictName(postGrlgList, item.aca111)" :border="false" />
                <van-cell v-else title="岗位" :value="getDictName(postList, item.aca111)" :border="false" />
                <van-cell title="签约方式" :value="getDictName(signTypeList, item.ccd028)" :border="false" />
                <!-- <van-cell title="是否同步参会" :value="getDictName(yesNoList, item.sftbch)" :border="false" />-->
                <van-cell title="签约起始时间" :value="dayFormatFn(item.ccd006, 'date')" :border="false" />
                <van-cell v-if="userInfo.sfzz00 === '0'" title="签约截止时间" :value="dayFormatFn(item.ccd007, 'date')" :border="false" />
                <van-cell v-if="userInfo.sfzz00 === '1'" class="handle-cell" title="签约截止时间" :border="false">
                  <span @click="showCalendar = true">点击解约</span>
                </van-cell>

                <van-calendar v-model="showCalendar" get-container="body" :min-date="minDate" :max-date="maxDate" @confirm="onConfirmUnAgree($event,item)" />
              </template>

              <template #buttons>
                <van-button type="primary" @click="handleSeeDetails(item.cce010)">查看详情</van-button>
              </template>
            </info-box>
          </div>

          <y-empty v-else></y-empty>
        </van-list>
      </van-pull-refresh>
    </div>

  </div>
</template>

<script>
import BusinessTabs from "@/components/business/business-tabs"
import InfoBox from "@/components/business/info-box"

import {commonApi} from "@api"
import {getDictName} from "@utils/common"
export default {
  name: "info-filings",
  components: {
    BusinessTabs,
    InfoBox
  },
  data() {
    return {
      // 顶部tab
      tabList: [
        {title: "正在录用", number: "0"},
        {title: "已解约", number: "0"}
      ],

      // 信息列表
      demoInfo: {
        ptmc: "厦门市达达闪送分公司",
        aca111: "3",
        ccd028: "2",
        sftbch: "1",
        ccd006: "20231115"
      },

      // 列表
      list: [],
      loading: false,
      finished: false,
      refreshing: false, //下拉刷新
      showCalendar: false, //解约日历

      // 用户信息
      userInfo: {
        ...this.$sessionUtil.getItem("userInfo"),

        sfzz00: "1", //1正在录用 0已解约
        aaa028: "2", //数据来源（0 经办 1 单位 2 个人 3 主管部门）
        source: "002", //渠道来源（001 PC端 002 移动端）

        page: 0,
        size: 3
      },

      // 时间区间
      minDate: new Date(new Date().getFullYear() - 10, 1, 1),
      maxDate: new Date(),

      // 字典列表
      platformList: [], //平台字典列表
      postList: [], //岗位字典列表
      postGrlgList: [], //岗位字典列表--个人零工
      signTypeList: [], //签约方式字典列表
      yesNoList: [] //是否同步参会字典列表
    }
  },
  created() {
    this.getCount() //查询人员在职数量

    this.getPlatformList() //查询字典列表
  },
  methods: {
    // 查看字典对应名称
    getDictName,

    // 获取录用与解约条数
    getCount() {
      const params = {
        serviceName: "xytPerson_countCe10OnJobStatus"
      }
      commonApi.proxyApi(params).then((res) => {
        const {onJobNum="0", terminateNum="0"} = res.map.data
        this.tabList[0].number = onJobNum
        this.tabList[1].number = terminateNum
      })
    },

    //查询字典列表
    getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["PTBM00", "ACA111_GRLG", "ACA111", "CCD028", "YES_NO"]
      }
      commonApi.proxyApi(params).then((res) => {
        const {data} = res.map
        const dictInfo = {
          "PTBM00": "platformList",
          "ACA111_GRLG": "postGrlgList",
          "ACA111": "postList",
          "CCD028": "signTypeList",
          "YES_NO": "yesNoList"
        }
        for (const key in dictInfo) {
          this[dictInfo[key]] = data[key].map((item) => {
            return {label: item.aaa103, value: item.aaa102}
          })
        }
      })
    },

    // 获取列表数据
    onLoad(index) {
      this.userInfo.page++
      this.findCe10ByPage()
    },

    // 查询从业人员备案信息
    findCe10ByPage() {
      const {page, size, sfzz00, aaa028, source} = this.userInfo
      const params = {
        serviceName: "xytPerson_findCe10ByPage",
        ...{page, size, sfzz00, aaa028, source}
      }
      commonApi.proxyApi(params).then((res) => {
        if (this.refreshing) { // 清空列表数据
          this.list = []
          this.refreshing = false
        }

        const {rows=[], total=0} = res.map.data
        this.list = [...this.list, ...rows]
        console.log(this.list, "this.list")

        this.loading = false

        if (this.list.length === total) {
          this.finished = true
        }
      }).catch(() => {
        this.loading = false
        this.finished = true
      })
    },

    // 下拉刷新
    onRefresh() {
      // 清空列表数据
      this.finished = false

      // 重新加载数据
      this.loading = true
      this.userInfo.page = 0
      this.onLoad()
    },

    // 切换tab
    handleChangeTabs(index) {
      this.userInfo.sfzz00 = index === 0 ? "1" : "0" //切换在职状态
      this.list = []
      this.onRefresh()
    },

    // 点击确认解约
    onConfirmUnAgree(date, item) {
      this.showCalendar = false

      this.$dialog.confirm({
        title: "提示",
        message: `您选择的解约日期为：${this.dayFormatFn(date, "date")}，是否解约？`,
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        const ccd007 = this.dayFormatFn(date, "other")
        const params = {
          serviceName: "xytPerson_saveAndRecheckCe09Web",
          ...item,
          aaa028: "2", //数据来源（0 经办 1 单位 2 个人 3 主管部门）
          source: "002", //渠道来源（001 PC端 002 移动端）
          ccd007 //解约时间
        }
        commonApi.proxyApi(params).then((res) => {
          this.$toast.success("已解约")
          this.getCount()

          this.userInfo.page = 0
          this.list = []
          this.onRefresh()
        })
      }).catch(() => { })
    },

    // 查看详情
    handleSeeDetails(cce010) {
      this.$router.push({path: "/info-filings-details", query: {cce010}})
    },

    // 新增
    handleAdd() {
      this.$router.push("/info-filings-add")
    }
  }
}
</script>

<style lang='less' scoped>
.info-filings {
  .info-container {
    min-height: calc(100vh - 44px);
    /deep/.handle-cell .van-cell__value {
      color: @main_color !important;
    }
  }
  .info-box .van-cell-group .van-cell .van-cell__title {
    width: 40%;
    flex: unset;
  }
}
</style>

