<!--
 * @Description: 人脸识别认证中间页面
 * @Version: 0.1
 * @Autor: yc
-->

<template>
  <div class="labor-protect-record">
    <div class="box_2 flex-col">
      <div class="text-wrapper_2">
        <van-icon class="item-icon" name="success" :color="colorMore" size="36" />
        <span class="text_4">认证成功</span>
      </div>

    </div>
  </div>
</template>

<script>
import { main_color } from "@/styles/theme/theme-params.less"
import axios from "axios"
import { SHA256 } from "crypto-js"
import { commonApi } from "@/api"

export default {
  name: "labor-protect-record",
  components: {},
  data() {
    return {
      colorMore: main_color,
      token: "",
      bcz003: ""
    }
  },
  created() {
    // 初始化表单数据
    console.log(this.$route.query.token, "token")
    this.token = this.$route.query.token
    this.bcz003 = this.$route.query.bcz003
    this.handleJumpDetail()
  },
  methods: {

    async handleJumpDetail() {
      await this.handleJumpFaceVerify()
    },
    handleJumpFaceVerify() {
      const data = { key: "value" }
      axios.post("https://app.hrss.xm.gov.cn/SBServer/face/getFaceResult?token=" + this.token, data)
        .then(response => {
          // 移除Base64字符串中可能存在的空格
          console.log(response.data, "返回内容")
          const encoded = response.data.resultBody.data.trim()
          // const decodedString = this.decodeBase64(encoded)

          const verifyObj = JSON.parse(encoded)
          const signStr = "aac002=" + verifyObj.aac002 + "&aac003=" + verifyObj.aac003 + "&faceid=" + verifyObj.faceid + "&token=xmrs"
          const encryptedData = SHA256(signStr).toString()

          if (encryptedData === verifyObj.sign) {
            const params = {
              serviceName: "xytBc03Web_faceResultSave",
              aac002: verifyObj.aac002,
              bcz003: this.bcz003,
              result: "1"
            }
            localStorage.setItem("faceVerifySuccess", JSON.stringify(params))
            commonApi.proxyApi(params).then((res) => {
              console.log("认证结果存储成功")
            })
            console.log("验签成功")
          } else {
            localStorage.setItem("isVerifyFailed", false)
            // const params = {
            //   serviceName: "xytBc03Web_faceResultSave",
            //   aac002: verifyObj.aac002,
            //   result: "0"
            // }
            // commonApi.proxyApi(params).then((res) => {
            //   console.log("认证结果存储成功")
            // })
            console.log("验签失败")
          }
        })
        .catch(error => {
          console.error("Error:", error)
        })
    },
    decodeBase64(base64String) {
      // 将Base64字符串分成每64个字符一组
      const padding = base64String.length % 4 === 0 ? 0 : 4 - (base64String.length % 4)
      base64String += "=".repeat(padding)

      // 使用atob()函数解码Base64字符串
      const binaryString = window.atob(base64String)

      // 将二进制字符串转换为TypedArray
      const bytes = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) { bytes[i] = binaryString.charCodeAt(i) }

      // 将TypedArray转换为字符串
      return new TextDecoder("utf-8").decode(bytes)
    }
  }
}
</script>

<style lang="less" scoped>
.box_2 {
  position: relative;
  padding: 58px 18px 9px 18px;
}

.image_4 {
  position: absolute;
  left: 88px;
  top: 218px;
  width: 200px;
  height: 200px;
}

.text-wrapper_2 {
  width: 108px;
  height: 60px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  line-height: 30px;
  margin: 0 115px 0 122px;
}

.paragraph_1 {
  width: 108px;
  height: 60px;
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 30px;
}

.text_4 {
  width: 108px;
  height: 60px;
  overflow-wrap: break-word;
  color: rgba(251, 111, 19, 1);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 30px;
}

.text_5 {
  overflow-wrap: break-word;
  color: rgba(51, 51, 51, 1);
  font-size: 12px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
}

.text_6 {
  overflow-wrap: break-word;
  color: rgba(251, 111, 19, 1);
  font-size: 12px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
}

.text-group_1 {
  display: flex;
}

.image_3 {
  width: 134px;
  height: 5px;
  align-self: center;
  margin-top: 51px;
}

.image_2 {
  width: 184px;
  height: 200px;
  margin: 100px 77px 0 84px;
}

.text-wrapper_3 {
  background-color: rgba(189, 26, 45, 1);
  border-radius: 22px;
  margin-top: 30px;
  padding: 11px 142px 11px 139px;
}

.image-text_1 {
  margin-top: 151px;

  .imge-text-button-div {
    margin-top: 30px;
  }

  .imge-text-radio-div {
    display: flex;
    justify-content: center;
    justify-items: center;
  }
}

.down-button {
  width: 100%;
  height: 44px;
}
</style>