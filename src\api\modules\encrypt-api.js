/*
 * @Description: 边聊边办接口
 * @Author: 吕志伟 <EMAIL>
 * @Date: 2025-06-24 17:00:00
 */

import request from "../useAxios"
const baseUrl = "/ylzins-modelengine"

/**
 * 获取 token
 * @param {*} params 
 * @param {*} options 
 * @returns 
 */
export function ssoWxLogin(params, options = {}) {
  return request.post(`${baseUrl}/api/v1/sso-auth/sso-wx-login`, { ...params }, options)
}

/**
 * 获取对话历史列表
 * @param {*} params 
 * @param {*} options 加解密配置
 * @returns 
 */
export function aiBotMessageList(params, options = {}) {
  return request.get(`${baseUrl}/api/v1/aiBotMessage/list`, {
    params: params,
    options
  })
}

/**
 * 获取对话详情
 * @param {*} params 
 * @param {*} options 
 * @returns 
 */
export function getMessageList(params, options = {}) {
  return request.get(`${baseUrl}/api/v1/aiBotMessage/getMessageList`, {
    params: params,
    options
  })
}

/**
 * 获取对话详情
 * @param {*} params 
 * @param {*} options 
 * @returns 
 */
export function encryptConfig(params, options = {}) {
  return request.get(`${baseUrl}/api/v1/encrypt/config`, {
    params: params,
    options
  })
}

/**
 * 获取对话详情
 * @param {*} params 
 * @param {*} options 
 * @returns 
 */
export function aiBotDetail(params, options = {}) {
  return request.get(`${baseUrl}/api/v1/aiBot/detail`, {
    params: params,
    options
  })
}

/**
 * 猜你想问
 * @param {*} params 
 * @param {*} options 
 * @returns 
 */
export function guessQuestion(params, options = {}) {
  return request.post(`${baseUrl}/api/v1/aiBot/guessQuestion`, { ...params }, options)
}