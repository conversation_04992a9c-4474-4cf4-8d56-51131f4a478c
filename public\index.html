<!--
 * @Description: 
 * @Version: 0.1
 * @Autor: chenyt
 * @Date: 2020-03-20 10:55:10
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-04-09 17:21:24
 -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <link rel="icon" href="data:image/ico;base64,aWNv" />
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, minimal-ui, viewport-fit=cover"
    />
    <meta name="no-title" content="0" />
    <title><%= htmlWebpackPlugin.options.title %></title>
    <van-number-keyboard safe-area-inset-bottom />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
  </head>

  <body>
    <script src="//map.qq.com/api/js?v=2.exp&key=<%= VUE_APP_TMAP_KEY %>"></script>
    <script src="https://map.qq.com/api/gljs?v=1.exp&key=<%= VUE_APP_TMAP_KEY %>"></script>
    <noscript>
      <strong
        >We're sorry but vue-cli4-tem doesn't work properly without JavaScript
        enabled. Please enable it to continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
