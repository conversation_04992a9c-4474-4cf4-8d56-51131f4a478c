<!--
 * @Description: 保存为常用联系人
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <van-field name="radio" label="保存为常用联系人" label-width="160">
    <template #input>
      <van-radio-group v-model="radioGroup" direction="horizontal" @change="handleChange">
        <van-radio v-for="item in dictData" :key="item.aaa102" :name="item.aaa102">{{ item.aaa103 }}</van-radio>
      </van-radio-group>
    </template>
  </van-field>
</template>
<script>

export default {
  name: "conect-radio",
  model: {
    prop: "val",
    event: "change"
  },
  props: {
    val: {
      type: String,
      default: "" 
    },
    dictData: {
      type: Array,
      default: () => []
    }
  },
  data() { 
    return {
      radioGroup: this.val
    }
  },

  watch: {
    val: {
      immediate: true,
      handler(val) {
        this.radioGroup = val
      }
    }
  },
  methods: {
    handleChange(name) {
      this.$emit("change", name)
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.van-radio__icon--checked .van-icon {
  background-color: @ylb_color;
  border-color: @ylb_color;
}
.van-cell::after {
  display: none !important;
}
</style>
