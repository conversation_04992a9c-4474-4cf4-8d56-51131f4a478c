<!--
 * @Description: 岗位详情
 * @Version: 0.1
 * @Author: hwx
-->
<template>
  <div class="post-details">
    <div class="post-box">
      <van-row>
        <van-col class="postData-title text-ellipsis-2 ellipsis-2" span="12">{{ postData.gwmc00 }}</van-col>
        <van-col class="postData-price" span="12">{{ postData.salary }}</van-col>
      </van-row>
      <div class="label-list">
        <span class="label" v-if="postData.countyName">{{postData.countyName}}</span>
        <span class="label" v-if="postData.workExperienceName">{{postData.workExperienceName}}</span>
        <span class="label" v-if="postData.educationName">{{postData.educationName}}</span>
        <span class="label" v-if="postData.natureName">{{postData.natureName}}</span>
      </div>
      <div v-show="postData.startDate" class='time-box'>
        <span>招聘起始时间：{{ dayFormatFn(postData.startDate, "date")}} 至 {{dayFormatFn(postData.endDate, "date")}}</span>
      </div>
      <van-row>
        <van-col class="inviter" span="24">            
          <img class="user-logo" src="@/assets/imgs/post-module/<EMAIL>" alt="">
          <span class="inviter-name">招聘者：{{ postData.contacts }}</span>            
        </van-col>
        <van-col class="inviter" span="24">            
          <van-icon class="user-logo" name="phone" />
          <span class="inviter-name">联系电话：{{ postData.phone }}</span>            
        </van-col>
      </van-row>
    </div>

    <div class='content-box'>
      <y-title content="岗位描述" fontWeight="700"/>
      <div class='post-introduce'>
        {{ postData.postDescribe || '暂无岗位描述' }}
      </div>

      <y-title content="公司信息" fontWeight="700"/>
      <div class='company-box' @click="handleViewComp">
        <div class='company-left'>
          <img src="@/assets/imgs/post-module/company-logo.png" alt="">
        </div>
        <div class='company-right'>
          <p class="company-name">{{postData.aab004}}</p>
          <div class="label-list">
            <span class="label" v-if="companyInfo.aab022">{{companyInfo.aab022}}</span>
            <span class="label" v-if="companyInfo.aab020">{{companyInfo.aab021}}</span>
            <span class="label" v-if="companyInfo.dwgm00">{{companyInfo.dwgm01}}</span>
          </div>
          <van-icon name="arrow" />
        </div>
      </div>

      <y-title content="工作地点" fontWeight="700"/>
      <div class='adress-box'>
        <div v-if="companyInfo.address" class='location-line'>
          <van-icon name="location-o" />
          <p class="adress-name">{{companyInfo.address}}</p>
        </div>
        <p class="adress-name" v-else>暂无</p>
        <div id="map" class='map-container'>
          
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable no-undef */
import {commonApi} from "@/api"

export default {
  components: {
    
  },
  data() {
    return {
      postData: {}, //岗位信息
      companyInfo: {}, //公司信息
      postType: "daily", // 当查看模式 日常用工/灵活用工
      aab001: "" //单位编号
    }
  },
  created() {
    const {id, postType, aab001} = this.$route.query
    this.aab001 = aab001
    this.postType = postType
    this.searchPostDetails(id, postType) // 查询岗位详情
    this.searchCompDetails(aab001) // 查询公司详情
  },
  methods: {
    // 查询岗位详情
    searchPostDetails(id, postType) {
      const serviceName = postType === "daily" ? "xytQzzp_findRcygByPage" : "xytQzzp_findLhygByPage"
      const params = {
        serviceName,
        id
      }
      this.loading = true
      commonApi.proxyApi(params).then(res => {
        console.log(res, "岗位详情")
        const {rows} = res.map?.data || {}
        this.postData = rows?.[0] || {}

        this.$nextTick(() => {
          const { latitude, longitude } = this.postData || {}
          latitude && longitude && this.reserMap(latitude, longitude)
        })        
      })
    },
    // 查询公司详情
    searchCompDetails(aab001) {      
      const params = {
        serviceName: "xytQzzp_getQzzpCompanyById",
        aab001
      }
      commonApi.proxyApi(params).then(res => {
        console.log(res, "查询公司详情")
        this.companyInfo = res.map?.data || {}
      })
    },
    //地图显示
    reserMap(lat, lng, zoom=13) {
      const center = new qq.maps.LatLng(lat, lng)
      const mapContainer = document.getElementById("map")
      this.map = new qq.maps.Map(mapContainer, {
        center,
        zoom, 
        draggable: true,
        mapSign: false // 隐藏右下角图标
      })

      const iconUrl = `${process.env.VUE_APP_PUBLIC_PATH}map-marker/map-marker.svg`      
      const markerIcon = new qq.maps.MarkerImage( //标记点图标及样式
        iconUrl, // 图片的URL
        new qq.maps.Size(27, 33), // 图标的大小
        new qq.maps.Point(0, 0), // 图标的坐标
        new qq.maps.Point(15, 30) // 图标的锚点
      )
      new qq.maps.Marker({
        id: 1,
        position: new qq.maps.LatLng(lat, lng),
        map: this.map,
        icon: markerIcon
      }) 
    },
    // 查看企业详情
    handleViewComp() {      
      const {aab001, postType} = this
      this.$router.push({path: "/company-details", query: {aab001, postType}})
    }
  }
}
</script>

<style lang="less" scoped>
.post-details {
  .post-box {
    padding: 20px 16px;
    background-color: #fff;
    .label-list {
      margin-top: 16px;
    }
    .postData-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: #222327;
      text-align: left;
      font-style: normal;
    }
    .postData-price {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: #FF3B37;
      line-height: 22px;
      text-align: right;
      font-style: normal;
    }    
    .time-box {
      margin-top: 16px;
      font-size: 12px;
      color: #BD1A2D;
      line-height: 14px;
      text-align: left;
      padding-bottom: 18px;
      border-bottom: 1px solid #E5E5E5;
      & > span {
        display: inline-block;
        padding: 2px 8px;
        height: 16px;
        border-radius: 9px;
        background-color: #faedef;
      }
    }
    .inviter {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      display: flex;
      align-items: center;
      margin-top: 11px;
      .user-logo {
        width: 12px;
        height: 15px;
        color: #b2b2b2;
      }
      .inviter-name {
        margin-left: 10px;
      }
      .icon {
        margin-right: 9px;
        width: 12px;
        height: 15px;
        border: 1px solid;
      }
    }
  }
  .content-box {
    padding: 0 16px 16px;
    .post-introduce {
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      padding-bottom: 20px;
    }
    .company-box {
      display: flex;
      position: relative;
      padding-bottom: 20px;
      .company-left {
        width: 40px;
        height: 40px;
        & > img {
          width: 100%;
          height: 100%;
        }
      }
      .company-right {
        margin-left: 10px;        
        .company-name {
          font-size: 16px;
          color: #3C3D40;
          line-height: 20px;
          font-weight: bold;
        }
        .van-icon-arrow {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translate(0,-50%);
          color: #3C3D40;
          font-size: 18px;
        }
      }
    }
    .adress-box {   
      .adress-name {
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-left: 10px;
      }   
      .location-line {
        display: flex;
        .van-icon-location-o {
          font-size: 18px;
        }        
      }
      .map-container {
        width: 100%;
        height: 120px;
        margin-top: 16px;
      }
      /deep/.map-container {
        & > div > div:nth-child(2) {
          display: none;
        }
        .smnoprint {
          display: none;
        }
      }
    }
  }
  .label-list {
    display: flex;
    flex-wrap: wrap;
    .label {
      padding: 5px;
      background: #F6F6F6;
      border-radius: 2px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      margin-right: 16px;
      margin-top: 8px;
    }
  }
}

</style>