<!--
 * @Description: 订单确认
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="ylb-page-wrapper" :style="{paddingBottom: `${paddingBottom}px`}">
    <div class="header-bg"></div>
    <div class="page-container pb40">
      <div class="card-wrapper">
        <div class="title">投保人信息</div>
        <div class="row">
          <div class="label">姓名</div>
          <div class="value">{{ formData_getter.baseInfo.aac003 }}</div>
        </div>
        <div class="row">
          <div class="label">证件类型</div>
          <div class="value">{{ getCCG981(formData_getter.baseInfo.ccg981) }}</div>
        </div>
        <div class="row">
          <div class="label">身份证号</div>
          <div class="value">{{ formData_getter.baseInfo.aac002 }}</div>
        </div>
        <div class="row">
          <div class="label">手机号码</div>
          <div class="value">{{ formData_getter.baseInfo.aae005 }}</div>
        </div>
        <div class="row">
          <div class="label">电子邮箱</div>
          <div class="value">{{ formData_getter.baseInfo.aae006 }}</div>
        </div>
      </div>
    </div>
    
    <div class="page-container pb20">
      <div class="box-wrapper pd-8">
        <div class="title">被保人信息</div>
        <div class="row">
          <div class="label">姓名</div>
          <div class="value">{{ insurePerson.aac003 }}</div>
        </div>
        <div class="row">
          <div class="label">证件类型</div>
          <div class="value">{{ getCCG981(insurePerson.ccg981) }}</div>
        </div>
        <div class="row">
          <div class="label">身份证号</div>
          <div class="value">{{ insurePerson.aac002 }}</div>
        </div>
        <div class="row">
          <div class="label">手机号码</div>
          <div class="value">{{ insurePerson.aae005 }}</div>
        </div>
        <div class="row">
          <div class="label">保险期限</div>
          <div class="value"> {{ insurePerson.bxqsrq }} 至 {{ insurePerson.bxjzrq }}</div>
        </div>
        <!-- <div class="title clear-border">保单信息</div>
        <div class="row">
          <div class="label">保单期限</div>
          <div class="value"></div>
        </div>
        <div class="row">
          <div class="label">保费</div>
          <div class="value"></div>
        </div> -->
      </div>
    </div>
    <div class="page-container pb20">
      <div class="box-wrapper">
        <y-title content="个人投保承诺书" :background-color="ylb_color"  />
      <p class="commit">本人承诺在本次投保“益鹭保”所选择的保险期限内从事灵活就业相关工作，如因情况不属实，导致出险后无法理赔的情况，后果自负！</p>
      </div>
    </div>
    <div class="page-container mb-8 pb16" v-if="false">
      <y-title content="个人投保承诺书" :background-color="ylb_color"  />
      <p class="commit">本人承诺在本次投保“益鹭保”所选择的保险期限内从事灵活就业相关工作，如因情况不属实，导致出险后无法理赔的情况，后果自负！</p>
      <y-signature v-if="false" :theme-color="ylb_color" class="mb-8" @confirm="handleSignconfirm" />
    </div>
    <div class="page-container pb20">
      <div class="box-wrapper">
        <y-title content="保障内容" :background-color="ylb_color"  />
        <div class="content">
          <div class="row">
            <div class="label">意外伤害</div>
            <div class="value">40万元</div>
          </div>
          <div class="row">
            <div class="label">意外医疗费用</div>
            <div class="value">3万元</div>
          </div>
          <div class="row">
            <div class="label">意外住院津贴</div>
            <div class="value">60元/日</div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="page-container pb16">
      <y-title content="保障内容" :background-color="ylb_color"  />
      <div class="content">
        <div class="row">
          <div class="label">意外伤害</div>
          <div class="value">40万元</div>
        </div>
        <div class="row">
          <div class="label">意外医疗费用</div>
          <div class="value">3万元</div>
        </div>
        <div class="row">
          <div class="label">意外住院津贴</div>
          <div class="value">60元/日</div>
        </div>
      </div>
    </div> -->

    <div class="botton-btn ylb-btn" ref="bottomBtn">
      <div class="row">
        <div class="check-box">
          <van-checkbox v-model="checkbox" shape="square" />
        </div>
        <div class="escape-clause">
          我已阅读<span class="link">投保须知及声明、保险条款、理赔指引、免责声明  </span>并同意所述内容
        </div>
      </div>
      <div class="row">
        <div class="cost-box">
          <span>￥{{ money }}</span>
          <van-icon name="arrow-up" color="#C0C4CC" />
        </div>
        <van-button round block type="primary" @click="handleInsure">保费支付</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ylb_color } from "@/styles/theme/theme-params.less"
import YSignature from "@/components/plugins/y-signature"
import { mapGetters } from "vuex"
import { commonApi } from "@/api"
const DATA_SOURCE_PERSON = "2" // 数据来源(0 经办1单位2个人3主管部门)

export default {
  components: { YSignature },
  data() {
    return {
      ylb_color,
      checkbox: false,
      paddingBottom: 0
    }
  },
  computed: {
    ...mapGetters(["formData_getter", "dictionaryCodeList"]),
    money() {
      return this.$route.query.money
    },
    insurePerson() {
      return this.formData_getter?.da02List?.[0] || {}
    }
  },
  
  mounted() {
    
    this.$nextTick(() => {
      this.paddingBottom = 16 + this.$refs.bottomBtn.getBoundingClientRect().height
    })
  },
  methods: {
    getCCG981(code) {
      return this.dictionaryCodeList.CCG981.find(item => item.value === code)?.label
    },
    handleSignconfirm() {},
    handleInsure() {
      if (!this.checkbox) {
        this.$toast("请先同意并勾选协议")
        return
      }

      const { origin, pathname } = window.location
      const href = `${origin}${pathname}#/?success=true`
      const params = JSON.parse(JSON.stringify(this.formData_getter))
      delete params.baseInfo
      params.serviceName = "xytDa01Web_submitDa01Web"
      const { aab301, gzpt00, ccd032, ygsd00, ygmc00, ywsb00, bxjzrq, bxqsrq, aae100, bxzq00, tbtc00, ccg981 } = params?.da02List[0] || {}
      const { rygh00, daz005 } = params?.bxywy || {}
      params.da02List[0].daa005 = ccg981?.replace(/0/g, "")
      params.dac001 = params.dac001?.replace(/0/g, "") // 身份证号码去除0
      params.aab301 = aab301 // 工作地址
      params.gzpt00 = gzpt00 // 工作平台
      params.ccd032 = ccd032 // 工作详细地址
      params.ygsd00 = ygsd00 // 用工时段
      params.ygmc00 = ygmc00 // 用工名称
      params.ywsb00 = ywsb00 // 有无社保
      params.bxjzrq = bxjzrq // 保险结束日期
      params.bxqsrq = bxqsrq // 保险起始日期
      params.aae100 = aae100 // 是否为常用联系人
      params.bxzq00 = bxzq00 // 投保天数
      params.tbtc00 = tbtc00 // 投保套餐
      params.rygh00 = rygh00 // 人员编号
      params.daz005 = daz005 // 保险业务员编号
      params.aaa028 = DATA_SOURCE_PERSON 
      params.successUrl = href
      commonApi.proxyApi(params).then(res => {
        const { data } = res?.map || {}   
        if (data?.payforURL) {
          location.href = data?.payforURL
        } else {
          this.$toast("此保单查无支付信息")
        }
        
      })
    }
  }
}
</script>

<style lang="less" scoped>
// 重置公共样式
.ylb-page-wrapper .y-title {
  padding-left: 0!important;
}
.header-bg {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  height: 69px;
  background: linear-gradient(180deg, #2DD8B8 0%, rgba(45,216,184,0) 100%);
}
.pb20 {
  padding-bottom: 20px;
}
.pb40 {
  padding-bottom: 40px;
}
.pb16 {
  padding-bottom: 16px;
}

.box-wrapper,.card-wrapper {
  padding: 11px 16px 20px;
  box-shadow: 0px 4px 8px 0px #E0E1E6;
  border-radius: 12px;
  font-family: PingFangSC, PingFang SC;
  background-color: #fff;
  padding-bottom: 20px;
  .pd-8 {
    padding-left: 8px;
    padding-right: 8px;
  }
  .title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #333333;
  }
  .row {
    padding: 0 16px;
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    .label {
      color: #909399;
      width: 60px;
      text-align-last: justify;
    }
    .value { 
      text-align: right;
    }
  }
}
.box-wrapper {
  box-shadow: 0px 0px 8px 0px #E0E1E6;
  border-radius: 12px;
  .title {
    padding: 20px 16px 16px 16px;
    border-bottom: 1px solid #EEEEEE;
    margin-bottom: 12px;
  }
  .clear-border {
    border: none;
    padding: 0 0 0 16px;
  }
  
}
.box-wrapper.pd-8 {
  padding-left: 8px;
  padding-right: 8px;
}
.card-wrapper {
  position: relative;
  padding-left: 8px;
  padding-right: 8px;
  top: 20px;
  left: 0;
  
  .title {
    padding: 20px 16px 18px;
    background: url("~@pic/yilubao/order/<EMAIL>") no-repeat;
    background-size: contain;
  }
}

.commit {
  font-size: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #000000;
  line-height: 20px;
}
// 底部按钮
.botton-btn {
  position: fixed;
  left: 0;
  bottom: 0;
  padding: 8px 16px 16px;
  width: 100%;
  background: #fff;
  
  box-shadow: 0px -1px 8px 0px rgba(186,186,186,0.32);
  .row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
  }
  .van-button {
    flex: 1;
    &:last-child {
      margin-left: 12px;
    }
  }
  .cost-box {
    font-size: 24px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #FF3B30;
  }
  .escape-clause {
    padding-left: 8px;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #333333;
    margin-bottom: 16px;
    line-height: 1.6;
    .link {
      color: @ylb_color;
    }
  }
}
.content {
  .row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 15px;
    font-family: PingFangSC, PingFang SC;
    &:last-child {
      margin-bottom: 0;
    }
    .label {
      font-size: 14px;
      font-weight: 400;
      color: #303133;
      line-height: 16px;
      width: 90px;
    }
    .value {
      font-size: 14px;
      font-weight: 500;
      color: #4077F4;
    }
  }
}

.van-checkbox {
  top: -16px;
}
/deep/.van-checkbox__icon--checked .van-icon {
  color: #fff;
  background-color: @ylb_color;
  border-color: @ylb_color;
}
</style>