
//模块导入
import AvatarPlatform, {
  PlayerEvents,
  SDKEvents
} from "@/vm-sdk/avatar-sdk-web_3.1.1.1011/index.js"

//动态虚拟人调节透明度
document.addEventListener("DOMContentLoaded", function() {
  const div = document.getElementById("wrapper")
  const range = document.getElementById("opacityRange")

  range.addEventListener("input", function() {
    div.style.opacity = this.value
  })
})

let avatarPlatform2 = null
// eslint-disable-next-line no-unused-vars
let recorder = null
export default {
  name: "avatarComponent",
  data() {
    return {
      SetApiInfodialog: false,
      SetGlobalParamsdialog: false,
      form: {
        // appid: "13b2c0b9", //到交互平台-接口服务中获取
        // apikey: "f62e4d4e1f03f168d9e98818ca770ff2", //到交互平台-接口服务中获取
        // apisecret: "ZjgyYTViZjlhODU5NjcyNDFjOGRlZDBm", //到交互平台-接口服务中获取
        // sceneid: "185954060585799680", //到交互平台-接口服务中获取，即"接口服务ID"
        appid: "eeaadd62", //到交互平台-接口服务中获取
        apikey: "a8a368e2b1d0a8fabfa75dae872d2b49", //到交互平台-接口服务中获取
        apisecret: "Yzk1OTdkM2JhNzI5MWUwNmZjNjdhZWI5", //到交互平台-接口服务中获取
        sceneid: "186820779793584128", //到交互平台-接口服务中获取，即"接口服务ID"
        serverurl: "wss://avatar.cn-huadong-1.xf-yun.com/v1/interact"//接口地址，无需更改
      },

      setglobalparamsform: {
        stream: {
          protocol: "xrtc", //（必传）实时视频协议，支持webrtc/xrtc/rtmp，其中只有xrtc支持透明背景，需参数alpha传1
          fps: 25, //（非必传）视频刷新率,值越大，越流畅，取值范围0-25，默认25即可
          bitrate: 1000000, //（非必传）视频码率，值越大，越清晰，对网络要求越高，默认1000000即可
          alpha: true //（非必传）是否开启透明背景，0关闭1开始，需配合protocol=xrtc使用
        },
        avatar: {
          avatar_id: "110005011", //（必传）授权的形象资源id，请到交互平台-接口服务-形象列表中获取
          width: 1080, //（非必传）视频分辨率宽（不是画布的宽，调整画布大小需调整名为wrapper的div宽）
          height: 1920, //（非必传）视频分辨率高（不是画布的高，调整画布大小需调整名为wrapper的div高）
          mask_region: "[0,0,1080,1920]", //（非必传）形象裁剪参数，[从左到右，从上到下，从右到左，从下到上]
          scale: 0.7, //（非必传）形象缩放比例，取值范围0.1-1
          move_h: -20, //（非必传）形象左右移动
          move_v: 250, //（非必传）形象上下移动
          audio_format: 1//（非必传）音频采样率，传1即可
        },
        tts: {
          vcn: "x4_lingxiaoying_assist", //（必传）授权的声音资源id，请到交互平台-接口服务-声音列表中获取
          speed: 60, //（非必传）语速
          pitch: 50, //（非必传）语调
          volume: 100, //（非必传）音量
          emotion: 13//（非必传）情感系数，仅带有情感能力的超拟人音色支持该能力，普通音色不支持
        },
        avatar_dispatch: {
          interactive_mode: 1//（非必传）0追加模式，1打断模式
        },
        subtitle: {
          subtitle: 0, //（非必传）开启字幕，2D形象支持字幕，透明背景不支持字幕，3D形象不支持字幕（3D形象多为卡通形象，2D多为真人形象）
          font_color: "#FFFFFF", //（非必传）字体颜色
          font_name: "Sanji.Suxian.Simple", //（非必传）不支持自定义字体，若不想使用默认提供的
          //字体，那么可以设置asr和nlp监听事件，去获取语音识别和语义理解的文本，自己前端贴字体。
          //支持一下字体：'Sanji.Suxian.Simple','Honglei.Runninghand.Sim','Hunyuan.Gothic.Bold',
          //'Huayuan.Gothic.Regular','mainTitle'
          position_x: 500, //（非必传）设置字幕水平位置，必须配置width、height一起使用，否则字幕不显示
          position_y: 1200, //（非必传）设置字幕竖向位置，必须配置width、height一起使用，否则字幕不显示
          font_size: 5, //（非必传）设置字幕字体大小，取值范围：1-10
          width: 100, //（非必传）设置字幕宽
          height: 100//（非必传）设置字幕高
        },
        enable: true, //demo中用来控制是否开启背景的参数，与虚拟人参数无关
        background: {
          type: "res_key", //（非必传）上传图片的类型，支持url以及res_key。（res_key请到交互平台-素材管理-背景中上传获取)
          data: "22SLM2teIw+aqR6Xsm2JbH6Ng310kDam2NiCY/RQ9n6s3nYJXloZWW1l64/g32vrn7d2lJQR7m9xD5EHYkVs14yBnvom5y9AoG/2iC1c/5Yuc/uDn8HsLAigE+rmEOEf2AQ8ET93anPEAiCeogF0aN02yYT55gCjRpKQZojzSXbzJYvzC+QxF6Lbfl1D00B3u5Ob8W/yBsQ0hn0IfBFEpJ6MFPHglM1VzhvCueW0C7yBg5trbgfhQ5tHQRACIngq/A+DaByP/AR2l0TmoNmV8H8L6ikjIxYnwQmXn9X3lhcixkmTpGuF8nR/AxYzlsgkAzA5uePn8CPZdhsJXbP9U2D5tqvbe1jTxRX9/SVmmQU="
          //（非必传）图片的值，当type='url'时,data='http://xxx/xxx.png'，当type='res_key'时，data='res_key值'（res_key请到交互平台-素材管理-背景中上传获取)
        },
        air: {
          air: 1,
          add_nonsemantic: 1
        }
      },
      formLabelWidth: "120px",
      // textarea: "",
      vc: "",
      recorderbutton: false,
      nlp: true,
      emotion: 0,
      action: "A_RH_hello_O",
      volume: 100,

      // 新增字段
      nlpText: "",
      asrText: "",

      // 存储播放器实体以及绑定的方法
      player: null
    }
  },
  async created() {
    // 1. 实例化SDK
    this.initSDK()

    // 2. 创建录音器
    this.createRecoder()

    // 3. 设置SDK监听事件
    this.setSDKEvenet()

    // 4. 设置播放器监听事件
    this.setPlayerEvenet()

    // 5. 设置API信息
    const params = {
      appId: this.form.appid,
      apiKey: this.form.apikey,
      apiSecret: this.form.apisecret,
      serverUrl: this.form.serverurl,
      sceneId: this.form.sceneid
    }
    avatarPlatform2.setApiInfo(params)

    // 6. 设置全局参数
    const globalParams = Object.assign({}, this.setglobalparamsform)
    if (this.setglobalparamsform.enable == false) {
      delete globalParams.background
      delete globalParams.enable
    }
    if (this.setglobalparamsform.stream.alpha == true) {
      globalParams.stream.alpha = 1
    } else {
      globalParams.stream.alpha = 0
    }
    avatarPlatform2.setGlobalParams(globalParams)

    // 7. 开始
    setTimeout(() => {
      this.start()
    }, 1000)
    setTimeout(() => {
      this.starInit()
    }, 3000)

    // // 5. SetApiInfo2 设置API信息
    // this.SetApiInfo2()

    // // 6. SetGlobalParams 设置全局参数
    // this.SetGlobalParams()
  },
  methods: {
    initSDK() {
      //必须先实例化SDK，再去调用其挂载的方法
      avatarPlatform2 = new AvatarPlatform()
      if (avatarPlatform2 != null) {
        this.open2("实例化SDK成功")
      }
    },
    createRecoder() {
      if (avatarPlatform2 != null) {
        recorder = avatarPlatform2.createRecorder()
        this.open2("创建录音器成功")
      } else {
        alert("请实例化SDK实例")
      }
    },
    setSDKEvenet() {
      //绑定SDK事件
      if (avatarPlatform2 != null) {
        avatarPlatform2
          .on(SDKEvents.connected, function(initResp) {
            console.log("SDKEvent.connect:initResp:", initResp)
          })
          .on(SDKEvents.stream_start, function() {
            setTimeout(() => {
              avatarPlatform2.writeText("欢迎使用厦门人社智慧小新 ，我是你的专属 AI 智能助手，有什么问题可以随时问我～", {
                nlp: false
              })
              console.log("3秒后发送消息")
            }, 3000) // 3000毫秒 = 3秒

            console.log("stream_start")
          })
          .on(SDKEvents.disconnected, function(err) {
            console.log("SDKEvent.disconnected:", err)
            if (err) {
              // 因为异常 而导致的断开！ 此处可以进行 提示通知等
              console.error("ws link disconnected because of Error")
              console.error(err.code, err.message, err.name, err.stack)
            }
          })
          .on(SDKEvents.nlp, (nlpData) => {
            this.nlpText = nlpData.content
            typeof this?.nlpCallBackFn === "function" && this.nlpCallBackFn(nlpData)
          })
          .on(SDKEvents.frame_start, function(frame_start) {
            console.log(
              "推流开始（可以看作一段文本开始播报时间点）frame_start:",
              frame_start
            )
          })
          .on(SDKEvents.frame_stop, function(frame_stop) {
            console.log(
              "推流结束（可以看作一段文本结束播报时间点）frame_stop:",
              frame_stop
            )
          })
          .on(SDKEvents.error, function(error) {
            console.log("错误信息error:", error)
          })
          .on(SDKEvents.connected, function() {
            console.log("connected")
          })
          .on(SDKEvents.asr, (asrData) => {
            console.log("语音识别数据asr:", asrData)
            this.asrText = asrData.text
          })
          .on(SDKEvents.tts_duration, function(ttsData) {
            console.log("语音合成用时tts：", ttsData)
          })
          .on(SDKEvents.subtitle_info, function(subtitleData) {
            console.log("subtitleData：", subtitleData)
          })
          .on(SDKEvents.action_start, function(action_start) {
            console.log(
              "动作推流开始（可以看作动作开始时间节点）action_start:",
              action_start
            )
          })
          .on(SDKEvents.action_stop, function(action_stop) {
            console.log(
              "动作推流结束（可以看作动作结束时间点）action_stop：",
              action_stop
            )
          })
        this.open2("监听SDK事件成功")
      } else {
        alert("请先实例化SDK")
      }
    },
    setPlayerEvenet() {
      if (avatarPlatform2 != null) {
        //绑定播放器事件
        const player = avatarPlatform2.createPlayer()
        player
          .on(PlayerEvents.play, function() {
            console.log("paly")
          })
          .on(PlayerEvents.playing, function() {
            console.log("playing")
          })
          .on(PlayerEvents.waiting, function() {
            console.log("waiting")
          })
          .on(PlayerEvents.stop, function() {
            console.log("stop")
          })
          .on(PlayerEvents.playNotAllowed, () => {
            this.isPlay = true
            console.log(
              "playNotAllowed：触发了游览器限制自动播放策略，播放前必须与游览器产生交互（例如点击页面或者dom组件），触发该事件后调用avatarPlatform2.player.resume()方法来接触限制"
            )
            player.resume()
          })
        this.player = player
        this.open2("监听播放器事件成功")
      } else {
        alert("请先实例化SDK")
      }
    },
    SetApiInfo2() {
      if (avatarPlatform2 == null) {
        alert("请先实例化SDK")
      } else {
        console.log("设置setApiInfo")
        const params = {
          appId: this.form.appid,
          apiKey: this.form.apikey,
          apiSecret: this.form.apisecret,
          serverUrl: this.form.serverurl,
          sceneId: this.form.sceneid
        }
        console.log("初始化SDK信息：", params)
        //初始化SDK
        avatarPlatform2.setApiInfo(params)
        this.open2("初始化SDK成功")
      }
    },
    SetGlobalParams() {
      if (avatarPlatform2 != null) {
        const params = Object.assign({}, this.setglobalparamsform)
        console.log("this.setglobalparamsform.stream.alpha", this.setglobalparamsform.stream.alpha)
        if (this.setglobalparamsform.enable == false) {
          delete params.background
          delete params.enable
        }
        console.log("this.setglobalparamsform", this.setglobalparamsform)
        if (this.setglobalparamsform.stream.alpha == true) {
          console.log("设置alpha=1")
          params.stream.alpha = 1
        } else {
          console.log("设置alpha=0")
          params.stream.alpha = 0
        }
        console.log("设置的全局变量为：", params)
        avatarPlatform2.setGlobalParams(params)
        this.open2("设置全局变量成功")
      } else {
        alert("请先实例化SDK")
      }
    },
    start() {
      if (avatarPlatform2 != null) {
        avatarPlatform2
          .start({ wrapper: document.querySelector("#wrapper") })
          .then(() => {
            console.log("connected &&  stream play successfully")
            // 暂时做为初始化事件的起点

          // 注意这里仅是流可以播放， 如果是进页面在用户未交互网页时，代码自动连，
          // 第三步骤 player实例 可能收到PlayerEvents.playNotAllowed事件。
          // 您需要交互层面再次用户点击网页 并主动调用 player.resume() 恢复播放！！
          // 原因：受限于浏览器的自动播放策略
          })
          .catch((e) => {
            console.error(e.code, e.message, e.name, e.stack)
          })
      } else {
        alert("请先实例化SDK")
      }

    },
    writeText() {
      if (avatarPlatform2 != null) {
        const text = this.textarea
        if (text != "" && this.vc == "") {
          avatarPlatform2.writeText(text, {
            nlp: this.nlp //是否开启语义理解
          })
        } else if (text != "" && this.vc != "") {
          avatarPlatform2.writeText(text, {
            nlp: this.nlp //是否开启语义理解
          })
        } else {
          alert("内容不许为空")
        }
      } else {
        alert("请先实例化SDK")
      }

    },
    writeCmd() {
      avatarPlatform2.writeCmd("action", this.action)
    },
    interrupt() {
      if (avatarPlatform2 != null) {
        avatarPlatform2.interrupt()
      } else {
        alert("请先实例化SDK")
      }
    },
    startRecord() {
      if (avatarPlatform2 != null) {
        avatarPlatform2.recorder.startRecord(0, () => {
          console.warn("STOPED RECORDER")
        }, {
          nlp: true,
          avatar_dispatch: {
            interactive_mode: 0//交互模式（追加或打断）
          }
        })
        //关闭录音按钮显示
        this.recorderbutton = true
      } else {
        alert("请先实例化SDK")
      }
    },
    stopRecord() {
      if (avatarPlatform2 != null) {
        avatarPlatform2.recorder.stopRecord()
        //开启录音按钮显示
        this.recorderbutton = false
      } else {
        alert("请先实例化SDK")
      }
    },
    stop() {
      if (avatarPlatform2 != null) {
        avatarPlatform2.stop()
      } else {
        alert("请先实例化SDK")
      }
    },
    destroy() {
      if (avatarPlatform2 != null) {
        //销毁SDK示例，内部包含stop协议，重启需重新示例化avatarPlatform实例
        avatarPlatform2.destroy()
        avatarPlatform2 = null
      } else {
        console.error("请先实例化SDK")
      }
    },
    open2(text) {
      // this.$dialog.alert({
      //   message: text,
      //   theme: "round-button"
      // })
    }
  },

  beforeDestroy() {
    //关闭页面时调用stop协议，确保链接断开，释放资源
    if (avatarPlatform2) {
      avatarPlatform2.stop()
    }
  }
}
