<!--
 * @Description: 工伤保险-首页
 * @Author: hwx
 * @date: 2024/5/23 11:29
 * @LastEditors: hwx
-->
<template>
  <div class="injury-insure-module">
    <div class="header-box">
      <img :src="require('@pic/injury-insure-module/<EMAIL>')" alt="">
    </div>
    <div class="injury-insure-box">
      <div class="business-container">
        <y-title content="全部" fontWeight="bold" font-cont-size="14" />
        <div class="business-box flex-c-s mt16">
          <div class="business-item flex-c-c-c mb16" v-for="(item,index) in businessList" :key="index" @click="handleJump(item)">
            <img :src="item.imgUrl" alt="">
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import {isValidUrl} from "@/utils/str-util"

export default {
  name: "injury-insure",
  data() {
    return {
      businessList: [
        {
          imgUrl: require("@pic/injury-insure-module/<EMAIL>"),
          title: "工伤保险待遇查看",
          path: "https://app.hrss.xm.gov.cn/SBServer/gsbx/dyxx/list"
        },
        {
          imgUrl: require("@pic/injury-insure-module/<EMAIL>"),
          title: "工伤保险缴费情况",
          path: "https://app.hrss.xm.gov.cn/SBServer/info/index"
        }
      ]
    }
  },
  methods: {
    handleJump(item) {
      if (!item.path){
        this.$toast("功能建设中，敬请期待!")
        return
      }

      if (isValidUrl(item.path)){
        window.open(item.path)
      } else {
        this.$router.push(item.path)
      }
    }
  }
}
</script>

<style scoped lang="less">
.injury-insure-module{
  background-color: #F6F6F6;
  .header-box {
    width: 100%;
    & > img {
      width: 100%;
    }
  }
  .injury-insure-box {
    padding: 24px 16px;
    min-height: calc(100vh - 202px);
    .business-container {
      background-color: @white_bg_color;
      padding: 8px 16px 6px;
      border-radius: 4px;
      .business-box .business-item {
        min-width: 33%;
        max-width: 34%;
        flex: 1;
        & > img {
          width: 44px;
        }
        .item-title {
          height: 40px;
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
          margin-top: 12px;
          text-align: center;
        }
      }
    }
  }
}
</style>