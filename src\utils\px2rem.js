const SCALE = "SCALE"

/**
 * 获取页面字体大小
 * @returns {number} html字体大小 fontSize px
 */
export const getHTMLFontSize = () => {
  return Number(document.documentElement.style.fontSize.split("px")[0])
}

/**
 * 将px转为rem
 * @param {string|number} value 被转换的值
 * @returns {string} 转换后的值 带单位 rem
 */
export const px2rem = (value, ratio) => {
  // tostring
  let _value = String(value)

  // 判断是否存在「px」
  if (_value.indexOf("px") > -1) {
    _value = _value.split("px")[0]
  }

  // 比例调整 转为number
  _value = Number(_value) * ratio

  // 判断当前的缩放比例是否有调整过
  // 这里的调整应该是要按照原来的缩放比例进行处理
  return parseFloat(_value / getHTMLFontSize() * getScale()).toFixed(5) + "rem"
}

/**
 * 获取当前的缩放比例
 * @returns {number} 当前比例
 */
export const getScale = () => {
  return Number(localStorage.getItem(SCALE)) || 1
}

/**
 * 设置当前的缩放比例
 * @param {number} scale 设置的比例
 */
 export const setScale = (scale) => {
  try {
    const oriScale = getScale()
    if (scale !== oriScale) {
      localStorage.setItem(SCALE, scale)
      // 无刷新改变
      const bodyFontSize = getHTMLFontSize() // 获取body的fontsize大小
      // 改变body的字体大小
      document.documentElement.style.fontSize =
        parseFloat((bodyFontSize / oriScale) * scale).toFixed(3) + "px"
      // window.location.reload() // 去除页面重载
    }
  } catch (error) {
    console.log(error)
  }
}
