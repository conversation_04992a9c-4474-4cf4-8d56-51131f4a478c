/*
 * @Description: 公共工具函数
 * @Version: 0.1
 * @Autor: Chenyt
 */
import { commonApi } from "@/api"
import SessionUtil from "@/utils/session-storage"

/**
 * 数据格式转换
 * @param {Object} data json数据
 * @param {String} key 键名
 * @param {String} value 键值
 * 
 * 数据示例：
 * {
 *     "10":"北京",
 *     "11":"上海",
 *     "12":"广州",
 *     "13":"深圳",
 * }
 * 
 * 转换数据格式：
 * [
 *     {
 *         "label":"北京",
 *         "value":"10"
 *     },
 *     {
 *         "label":"上海",
 *         "value":"10"
 *     },
 *     {
 *         "label":"广州",
 *         "value":"10"
 *     },
 *     {
 *         "label":"深圳",
 *         "value":"10"
 *     },
 * ]
 */

export function jsonToArray(data, value = "label", key = "value") {
  const arr = []

  if (Object.prototype.toString(data) === "[object Object]") {
    const keys = Object.keys(data)
    const isNumbeKeys = keys.some(item => item.startsWith(0))
    // 如果把数字编码作为json数据的键名，则需要重新排序一下数据
    if (isNumbeKeys) {
      keys.sort() // 降序排序
      keys.forEach(item => {
        arr.push({ [key]: item, [value]: data[item] })
      })

      return arr
    }
    // 常规json数据转换
    Object.keys(data).forEach(item => {
      arr.push({ [key]: item, [value]: data[item] })
    })
  }

  return arr
}
/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' +
    decodeURIComponent(search)
      .replace(/"/g, '\\"')
      .replace(/&/g, '","')
      .replace(/=/g, '":"')
      .replace(/\+/g, ' ') +
    '"}'
  )
}
/**
* 设置页面标题
* @param {String} title 标题
* 
* **/
export default function setTitle(title) {
  document.title = title
  document.head.querySelector('title').innerText = title
}


/**
* 
* 返回顶部
* 
**/
export function scrollToTop(x = 0, y = 0, type = "smooth") {
  if (!!window.ActiveXObject || "ActiveXObject" in window) {
    window.scrollTop = 0;
    return
  }

  window.scrollTo({
    top: x,
    left: y,
    behavior: type // 滚动行为：smooth平滑滚动，instant瞬间滚动，默认值auto，等同于instant
  })
}

/**
* 
* 查看字典对应名称
* @param {Array} dictList 字典列表
* @param {String} value 字典值
* 
**/
export function getDictName(dictList, value) {
  if (!value) {
    return ""
  }
  const data = dictList.filter(item => item.value === value)

  return data[0]?.label || ""
}

/**
* 
* 根据身份证获取出生日期
* @param {String} idCard 身份证号码
* @param {String} separate 分隔符
* 
**/
export function getBirthdate(idCard, separate = '') {
  const year = idCard.substring(6, 10);
  const month = idCard.substring(10, 12);
  const day = idCard.substring(12, 14);

  return year + separate + month + separate + day;
}

/**
* 
* 根据身份证获取 性别、出生日期、户籍
* @param {String} 身份证号码
* 
**/
export function getInfoByIdCard(idCard) {
  const data = {
    sex: '',
    birthdate: '',
    area: '',
  }

  // 性别
  if (parseInt(idCard.slice(-2, -1)) % 2 == 1) {
    data.sex = "1"
  } else {
    data.sex = "2"
  }

  // 出生日期
  data.birthdate = getBirthdate(idCard)

  // 户籍
  data.area = idCard.slice(0, 6)

  return data
}

/**
* 
* 根据身份证号或者出生日期，计算年龄
* @param {String} value 身份证号码 或 出生日期(YYYY-mm-dd)
* 
**/
export function getAge(value) {
  if (!value) {
    return ''
  }

  let newYear = value //出生日期
  if (value.length > 10) { //身份证号
    const re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/;
    const arr_data = value.match(re_eighteen);
    const year = arr_data[2];
    const month = arr_data[3];
    const day = arr_data[4];
    newYear = `${year}-${month}-${day}`
  }

  // 当前时间
  const today = new Date();
  // 出生日期
  const birthDate = new Date(newYear);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  // 如果当前月份小于出生月份，或者两个月份相同但当前日期小于出生日期，则年龄减一
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }

  return age
}


/**
* 
* 移除数组中所有长度为零的children属性
* @param {Array} arr 
* 
**/
export function removeEmptyChildren(arr) {
  if (arr?.length) {
    for (let i in arr) {
      if (arr[i].children?.length) {
        removeEmptyChildren(arr[i].children)
      } else {
        delete arr[i].children;
      }
    }
  }
  return arr
}

/**
* 
* 获取数据字典
* @param {Array} aa10List 字典列表 
* 
**/
export async function commonGetPlatformList(aa10List) {
  const params = {
    serviceName: "xytCommon_getAa10ByAaa100s",
    aa10List
  }
  const res = await commonApi.proxyApi(params) //获取字典
  const { data } = res.map
  const dictInfo = {}

  const codeList = SessionUtil.getItem("codeList") || {}
  aa10List.forEach(item => {
    dictInfo[item] = data[item].map((item) => {
      return { label: item.aaa103, value: item.aaa102 }
    })

    if (!codeList?.[item]?.length) {
      codeList[item] = dictInfo[item]
    }
  })

  SessionUtil.setItem("codeList", codeList) //存储字典

  return dictInfo //返回字典
}


