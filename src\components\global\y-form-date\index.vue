<!--
 * @Description: 日期
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <div class="input-text-box">
      <div :class="['input-text', rules.length > 0 ? 'input-require' : '', disabled ? 'input-disabled' : '']">{{ titleName }}</div>
      <van-field
        readonly
        clickable
        name="picker"
        :value="newValue"
        :rules="rules"
        :placeholder="placeholder"
        @click="dateShow = true"
      />
    </div>
    <!-- 日期组件 -->
    <van-calendar :min-date="minDate" :max-date="maxDate" v-model="dateShow" @confirm="onConfirm" />
  </div>    
</template>

<script>
export default {
  name: "y-form-date",
  props: {
    placeholder: String, //提示
    titleName: String, //标题
    value: String, //值
    typeNumber: [String, Number], //父组件显示回显值顺序数字
    rules: { // 校验规则
      type: Array,
      default: () => ([])
    },
    disabled: {
      type: Boolean,
      default: false
    },
    minDateValue: {
      type: String,
      default: "1980-01-01"
    },
    maxDateValue: {
      type: String,
      default: "2080-01-01"
    }
  },
  data() {
    return {
      newValue: "",
      dateShow: false,
      minDate: new Date("1980-01-01"),
      maxDate: new Date("2023-12-06")
    }
  },
  watch: {
    value: {
      handler(val) {
        this.newValue = val
      },
      immediate: true
    },
    minDateValue: {
      handler(val) {
        this.minDate = new Date(val)
      },
      immediate: true
    },
    maxDateValue: {
      handler(val) {
        this.maxDate = new Date(val)
      },
      immediate: true
    }
  },
  methods: {
    //日期确认
    onConfirm(date) {
      this.dateShow = false
      this.$emit("changeDate", this.formatDate(date), this.typeNumber)//传递父级
    },
    formatDate(date, separate="") {
      console.log(date)
      const Y = date.getFullYear()
      const M = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1
      const D = date.getDate() < 10 ? "0" + date.getDate() : date.getDate()
      return Y + separate + M + separate + D
    }
  }
}
</script>

<style lang="less" scoped>
.input-text-box {
  display: flex;
  flex-wrap: nowrap;
  align-items: flex-start;
  padding: 9px 16px;
  min-height: 48px;
  position: relative;
  &::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: 16px;
    bottom: 0;
    left: 16px;
    transform: scaleY(0.5);
    border-bottom: 0.5px solid @four_text_color !important;
  }
  .input-text {
    font-size: 14px;
    font-weight: 400;
    color: @main_text_color;
    line-height: 30px;
    min-width: 86px;    
  }
  .input-require {
    position: relative;
      &::before {
        position: absolute;
        left: -8px;
        color: @danger_color;
        font-size: 0.37333rem;
        content: '*';
        top: -2px;
      }
  }

  .input-disabled {
    color: @third_text_color;
  }
  .van-cell {
    padding: 0;
  }
}
</style>