<!--
 * @Description: 
 * @Version: 0.1
 * @Autor: lzx
 * @Date: 2020-07-27 16:54:04
 * @LastEditors: yjm
 * @LastEditTime: 2020-08-10 11:17:58
--> 

# Pdf展示组件

主要功能包含pdf数据流展示在canvas中，以及将canvas预览文件转成图片放大预览(当前pdfjs-dist的版本为`v2.3.200`)

## 如何使用



### 引入

```js
import Vue from 'vue';
import { YPdf } from '@ylz/vant';

Vue.use(YPdf);
```

## 代码演示

### 基础用法

```html
<y-pdf eid="pdf" :base64Pdf="base64Pdf" />
```

## API

### Props

| name | 描述     | 默认值 |
| ---- | -------- | ------ |
| base64Pdf  | pdf base64数据流 | ''     |

### Events

|  方法   | 描述  |
|  ----  | ----  |
| savePreviewImageFn  | 向外传递pdf预览图片 |
