const dayjs = require("dayjs")
const fs = require("fs")
const path = require("path")
const binUtil = {
  getVersion: (versionObj = {}, objName) => {
    const nowDate = dayjs().format("YYYYMMDD")
    const nowTime = dayjs().format("HHmmss")
    versionObj.history[objName + "_list"].unshift({ ...versionObj[objName] })
    const versionObj_prod = versionObj[objName]
    if (dayjs(versionObj_prod.date).format("YYYYMMDD") === nowDate){
      // 同一天发布的版本+1
      versionObj_prod.version += 1
    } else {
      versionObj_prod.version = 0
      versionObj_prod.date = nowDate
    }

    versionObj_prod.time = nowTime
    // fs.writeFile(`${path.resolve()}/bin/version.json`, JSON.stringify(versionObj, null, 2), () => {
    //   console.log("更改版本号成功")
    // })
    return versionObj_prod.date + "_" + versionObj_prod.version
  }
}
module.exports = binUtil
