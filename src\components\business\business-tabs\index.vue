<!--
 * @Description: 业务tabs
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="business-tabs">
    <div class="tabs-container">
      <div class="tabs-box flex-c-s">
        <div 
          :class="['tab', tabList.length>2?'mr8':'mr30', activeIndex === index ? 'active' : '']"
          v-for="(item,index) in tabList" :key="index"
          @click="handleChangeTabs(index)"
          >
          {{ item.title + (item.number > 0 ? '（' + item.number + '）' : '') }}
        </div>
      </div>

      <van-button class="add-button flex-c-c" type="primary" @click="handleAdd">
        <van-icon v-if="showIcon" name="plus" size="12px" />
        <span class="ml2">{{handleName}}</span>
      </van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "business-tabs",
  props: {
    handleName: {
      type: String,
      default: "新增"
    },
    showIcon: {
      type: Boolean,
      default: true
    },
    tabList: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {      
      activeIndex: 0
    }
  },
  methods: {
    handleChangeTabs(index) {
      this.activeIndex = index
      this.$emit("handleChangeTabs", index)
    },
    handleAdd() {
      this.$emit("handleAdd")
    }
  }
}
</script>

<style lang="less" scoped>
.tabs-container {   
  width: 100%;
  height: 44px;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;  
  .add-button {
    width: 68px;
    height: 28px;
    font-size: 14px;
    font-weight: 400;
    color: @white_text_color;
    line-height: 28px;
    padding: 0;
    border-radius: 14px;
  }
  .tabs-box {
    flex: 1;
    height: 100%;
    .tab {
      font-size: 14px;
      font-weight: 500;
      color: @main_text_color;
      line-height: 20px;
      position: relative;
      &.active {
        color: @main_color;
        &::after {
          position: absolute;
          content: '';
          width: 44px;
          bottom: -8px;
          left: 50%;
          height: 2px;
          transform: translate(-50%);
          border-bottom: 2px solid @main_color;
        }
      } 
      
    }
  }
  
}
</style>