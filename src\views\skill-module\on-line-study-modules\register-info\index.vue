<!--
 * @Description: 意愿弹窗
 * @Version: 0.1
 * @Author: T
-->
<template>
  <van-popup v-model="visible" :close-on-click-overlay="false" round position="bottom">
    <div class="popup-body">
      <van-form
        class="base-form"
        label-width="120"
        @failed="onFailed"
        @submit="handleSave"
      >
        <van-cell-group>
          <van-field
            v-model="userInfo.xm0000"
            name="xm0000"
            label="姓名"
            placeholder="请输入"
            disabled
          />
          <van-field
            v-model="userInfo.zjhm00"
            name="zjhm00"
            label="证件号码"
            placeholder="请输入"
            disabled
          />
          <y-select-dict
            v-model="formData.pxgz00"
            label="您对哪种新就业形态培训工种感兴趣"
            :rules="formRules.pxgz00"
            dict-type="PXGZ00"
            :filterabled="true"
            :required="true"
            is-link
            :getCodeApi="getCodeApi"
          />
          <y-select-dict
            v-model="formData.jjwt00"
            label="您希望通过培训解决什么问题"
            :required="true"
            :rules="formRules.jjwt00"
            dict-type="JNPX_JJWT00"
            :filterabled="false"
            is-link
          />
          <van-field
            v-if="formData.jjwt00 === '005'"
            v-model="formData.jjwt01"
            name="jjwt01"
            label="其他"
            placeholder="请输入"
            :required="true"
            :rules="formRules.jjwt01"
          />
          <van-field name="pxyq00" label="您希望培训方式是（可多选）" required :rules="formRules.pxyq00">
            <template #input>
              <van-checkbox-group v-model="formData.pxyq00" direction="horizontal">
                <van-checkbox v-for="item in JNPX_PXFS00" :name="item.aaa102" :key="item.aaa102" shape="square">{{ item.aaa103 }}</van-checkbox>
              </van-checkbox-group>
            </template>
          </van-field>
          <van-field
            v-if="formData.pxyq00.includes('005')"
            v-model="formData.pxyq01"
            name="pxyq01"
            label="其他"
            placeholder="请输入"
            :required="true"
            :rules="formRules.pxyq01"
          />
        </van-cell-group>
        <div class="button-box-more">
          <van-button round block type="primary" native-type="submit">
            提交
          </van-button>
        </div>
      </van-form>
    </div>
  </van-popup>
</template>

<script>
import { commonApi } from "@/api"

export default {
  name: "register-info",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      formData: {
        aac003: "", // 姓名
        aac002: "", // 身份证
        pxgz00: "", // 感兴趣工种
        jjwt00: "", // 解决问题问题
        jjwt01: "", // 解决问题（其他）
        pxyq00: [], // 培训方式
        pxyq01: "" // 培训方式（其他）
      },
      formRules: {
        pxgz00: [{ required: true, message: "请选择" }],
        jjwt00: [{ required: true, message: "请选择" }],
        jjwt01: [{ required: true, message: "请输入" }],
        pxyq00: [{ required: true, message: "请选择" }],
        pxyq01: [{ required: true, message: "请输入" }]
      },
      JNPX_PXFS00: []
    }
  },
  computed: {
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    }
  },
  created() {
    this.getDicData()
  },
  methods: {
    getDicData() {
      commonApi.proxyApi({
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["JNPX_PXFS00"]
      }).then(res => {
        const { JNPX_PXFS00 } = res?.map?.data
        const filterRes = JNPX_PXFS00.filter(item => {
          const { aaa102, aaa103} = item
          if (aaa102 !== "999") {
            item.text = aaa103
            item.value = aaa102
            return item
          }
        })
        this.JNPX_PXFS00 = filterRes
      })
    },
    getCodeApi() {
      return this.findXytgzxx()
    },
    // 表单校验失败
    onFailed() {
      this.$toast("请完善表单信息！")
    },
    findXytgzxx(){
      return new Promise((resolve) => {
        commonApi.proxyApi({
          serviceName: "xytJnpx_findXytgzxx",
          "bzxx00": "",
          "gzbm00": "",
          "gzmc00": "",
          "id": "",
          "num000": 0,
          "sfjqgz": "",
          "sfzdgz": "",
          "zslx00": ""
        }).then(res => {
          const list = res?.map?.data?.map(item => {
            const { gzmc00: aaa103, gzbm00: aaa102 } = item
            item.aaa102 = aaa102
            item.aaa103 = aaa103
            return item
          }) || []
          
          resolve({
            appcode: 0,
            map: {
              data: {
                PXGZ00: list
              }
            }
          })
        }).catch((err) => {
          console.error("findXytgzxx", err)
        })
      })
    
    },
    async handleSave() {
      const {xm0000: aac003, zjhm00: aac002 } = this.userInfo
      return await commonApi.proxyApi({
        serviceName: "xytJnpx_savePxyyxx",
        ...{
          ...this.formData,
          aac002,
          aac003,
          pxyq00: this.formData.pxyq00.join(",")
        }
      }).then(() => {
        location.reload()
      })
    }
  }
}
</script>
<style lang="less">
.popup-body {
  // height: 80vh;
  overflow: auto;
  padding: 16px;
}
</style>