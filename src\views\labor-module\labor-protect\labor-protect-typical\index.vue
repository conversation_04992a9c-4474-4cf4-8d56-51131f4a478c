<!--
 * @Description: 资讯中心
 * @Author: wujh
 * @date: 2024/1/31 16:12
 * @LastEditors: wujh
-->
<template>
  <div class="information-center-container">
<!--    <div class="seek-box">-->
<!--      <span class="seek-title">资讯中心</span>-->
<!--    </div>-->

    <div class="tabs-box">
<!--      <van-sticky>-->
<!--        <van-tabs v-model="selectd" @click="tabsChangeFn">-->
<!--          <van-tab-->
<!--              v-for="(item, key) in bannerLabel"-->
<!--              :key="key"-->
<!--              :title="item.label"-->
<!--              :name="item.key"-->
<!--          >-->
<!--          </van-tab>-->
<!--        </van-tabs>-->
<!--      </van-sticky>-->
      <van-list
          ref="vanList"
          :finished="finished"
          finished-text="没有更多了"
          :immediate-check="false"
          @load="onLoad"
      >
        <div
            class="tabs-item"
            v-for="(item, key) in infoList"
            :key="key + 'dxal'"
            @click="openPageByNew(item)"
        >
          <div class="item-left">
            <div class="item-top text-ellipsis-2 ellipsis-2">{{ item.title }}</div>
            <div class="item-bottom">
              <p class="item-tip">{{ item.source }}</p>
              <p class="item-date">{{ item.publishDate }}</p>
            </div>
          </div>
          <div class="item-right">
            <img :src="item.logo" alt="">
          </div>
        </div>
      </van-list>
    </div>
  </div>
</template>

<script>
import {commonApi} from "@api"

const SITE_ID = "502746213777477" //网站id(默认值:502746213777477)
// const ANCESTORS = "502747731423301" //父节点id(默认值:502747731423301)

export default {
  name: "information-center",
  data(){
    return {
      pageForm: {
        pageNumber: 1,
        pageSize: 10,
        totalNumber: 0
      },
      bannerLabel: [],
      selectd: "dxal",
      news: {},
      banners: [],
      finished: false,
      catalogIdMap: {},
      infoList: []
    }
  },
  created() {
    // this.getCatalogFn() // 获取栏目列表
    // 典型案例 单独新增
    // this.getContentListFn(item, {
    //   "pageNumber": 1, //页码
    //   "pageSize": 10 //每页数量
    // })
    const dxalItem = {
      label: "典型案例",
      key: "dxal"
    }

    this.bannerLabel.push(dxalItem)
    this.selectd = "dxal"
    // this.fetchTypicalCaseData({pageNumber: 1, pageSize: 10}) // 查询典型案例列表
    this.onLoad()
    // this.tabsChangeFn()
  },
  methods: {

    /**
     * @description 获取栏目列表
     *
     * */
    // getCatalogFn(){
    //   commonApi.proxyApi({
    //     serviceName: "getCatalogTreeNode",
    //     siteId: SITE_ID,
    //     ancestors: ANCESTORS //父节点id(默认值:502747731423301)
    //   }).then(async res => {
    //     const result = res?.data || [] // 获取通知公告信息
    //     const resultList = result[0]?.children
    //     this.bannerLabel = []
    //     for (const item of resultList){
    //       this.catalogIdMap[item.alias] = item.catalogId
    //       if (item.alias === "twzx" || item.alias === "twzxapp" || item.alias === "Floatingwindow"){
    //         // 轮播 -- 图文资讯 单独处理
    //         continue
    //       }
    //
    //       await this.getContentListFn(item, {
    //         "pageNumber": 1, //页码
    //         "pageSize": 10 //每页数量
    //       })
    //       this.bannerLabel.push({
    //         ...item,
    //         label: item.name,
    //         key: item.alias
    //       })
    //     }
    //     this.selectd = "tzgg" // 默认选中 -- 通知公告
    //     this.tabsChangeFn()
    //   }).catch(err => {
    //     console.error(err, "err getCatalogFn")
    //   })
    // },
    async getContentListFn(item, pageForm = {}){
      await commonApi.proxyApi({
        serviceName: "getContentList",
        siteId: SITE_ID, //网站id(默认值:502746213777477)
        catalogId: item.catalogId, //栏目id
        "pipe": "PC", //发布渠道
        pageNumber: pageForm?.pageNumber || 1,
        pageSize: pageForm?.pageSize || 10
      }).then(res => {
        const contentList = res?.data?.contentList
        const totalNumber = Number(res?.data?.pageInfo?.totalNumber)
        if (item.alias === "twzx"){
          // 轮播 -- 图文资讯 单独处理
          this.banners = contentList
          return
        }

        if (!this.news[item.alias]?.contentList){
          this.news[item.alias] = {
            contentList: [],
            finished: false
          }
        }

        this.news[item.alias].contentList = this.news[item.alias].contentList.concat(contentList)
        this.news[item.alias].pageForm = {
          ...pageForm,
          totalNumber: totalNumber
        }

        this.news[item.alias].finished = this.news[item.alias].contentList.length >= totalNumber // 根据结果修改当前的结束状态
      }).catch(err => {
        console.error(err, "err getContentListFn")
      })
    },
    // 查询典型案例列表
    async fetchTypicalCaseData(pageForm) {
      const {pageNumber: page, pageSize: size} = pageForm
      const params={
        serviceName: "xytDxal_findBc07ByPage",
        source: " 002",
        page,
        size
      }
      const res = await commonApi.proxyApi(params)
      const {rows=[], total} = res?.map?.data || {}
      console.log(res, "查询典型案例列表")
      const list = rows.map(item => {
        return {
          ...item,
          showType: "1",
          catalogName: "典型案例",
          bcz007: item.bcz007,
          title: item.btxx01,
          publishDate: item.fbsj00,
          source: "厦门新就业形态"
        }
      })
      if (!this.news["dxal"]?.contentList){
        this.news["dxal"] = {
          contentList: [],
          finished: false
        }
      }
      const contentList = this.news["dxal"].contentList.concat(list)
      this.pageForm.totalNumber = Number(total)
      const finished = this.news["dxal"].contentList.length >= total
      this.news["dxal"] = {
        pageForm: { ...this.pageForm },
        contentList,
        finished
      }
    },
    openPageByNew(item){
      if (item.linkFlag === "Y"){
        window.location.href = item.redirectUrl
      } else {
        const { catalogName, bcz007 } = item
        const specialList = ["典型案例"] // 跳转典型案例 特殊处理
        const query = specialList.includes(catalogName) ? { showType: "1", bcz007} : {
          showType: "0",
          catalogId: item.catalogId,
          contentId: item.contentId,
          contentType: item.contentType
        } //showType 0富文本展示 1查询字段展示

        this.$router.push({
          path: "/information-center/detail",
          query
        })
      }
    },
    async onLoad(){
      this.finished = true

      console.log(this.news[this.selectd]?.pageForm, "this.news[this.selectd]?.pageForm")
      let { pageNumber = 0 } = this.news[this.selectd]?.pageForm || {}
      const page = pageNumber += 1

      if (this.selectd === "dxal") { //典型案例 单独调用
        await this.fetchTypicalCaseData({
          pageNumber: page,
          pageSize: 10
        })
      } else {
        await this.getContentListFn({
          catalogId: this.catalogIdMap[this.selectd],
          alias: this.selectd
        }, {
          pageNumber: page,
          pageSize: 10
        })
      }

      this.finished = this.news[this.selectd].finished
      this.infoList = this.news[this.selectd].contentList
    },
    tabsChangeFn(){
      console.log(this.news, "this.news")
      console.log(this.selectd, "this.selectd")
      // this.$refs.vanList.scrollToTop()
      this.finished = this.news[this.selectd].finished
      this.infoList = this.news[this.selectd].contentList
    }
  }
}
</script>

<style scoped lang="less">
.information-center-container{
  .seek-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    .seek-title {
      font-size: 18px;
      font-weight: bold;
      color: @main_text_color;
      line-height: 25px;
    }
    .more {
      font-size: 14px;
      font-weight: bold;
      color: @five_text_color;
      line-height: 20px;
    }
  }
  .tabs-box {
    .van-list{
      padding: 0 16px;
    }
    ::v-deep .van-tab {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }
    ::v-deep .van-tabs__content {
      padding: 0 0 0 14px;
    }
    .tabs-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      &:not(:last-child) {
        border-bottom: 1px solid #EEEEEE;
      }
      .item-left {
        .item-top {
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
        }
        .item-bottom {
          margin-top: 20px;
          .item-tip {
            font-size: 11px;
            font-weight: 400;
            color: @main_color;
            line-height: 16px;
          }
          .item-date {
            font-size: 12px;
            font-weight: 400;
            color: @five_text_color;
            line-height: 18px;
            //margin-left: 20px;
          }
        }
      }
      .item-right {
        width: 108px;
        margin-left: 18px;
        border-radius: 4px;
        & > img {
          width: 100%;
          max-width: 180px;
          object-fit: contain;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
