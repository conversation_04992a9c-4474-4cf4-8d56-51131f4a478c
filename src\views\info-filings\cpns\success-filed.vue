<!--
 * @Description: 备案成功--步骤
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="success-filed">
    <img :src="require('@pic/info-filings/<EMAIL>')" alt="">
    <div class="text">恭喜你，备案成功</div>
    <div class="text-tip">{{ second }}s后自动返回主页</div>
    <van-button class="back-btn" plain type="info" @click="handleBack">返回</van-button>
  </div>
</template>

<script>
export default {
  name: "success-filed",
  props: {
    active: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      second: 3,
      timer: null
    }
  },
  watch: {
    active(val) {
      val === 2 && this.setTimer()
    }
  },
  methods: {
    setTimer() {
      this.timer = setInterval(() => {
        this.second--
        
        if (this.second === 0) {
          clearTimeout(this.timer)
          this.handleBack()
        }
      }, 1 * 1000)

      this.$once("hook:beforeDestroy", () => {
        clearTimeout(this.timer)
      })
    },
    handleBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang='less' scoped>
.success-filed {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  & > img {
    width: 60px;
    height: 60px;
    margin-top: 52px;
  }
  .text {
    font-size: 16px;
    font-weight: bold;
    color: @main_text_color;
    line-height: 16px;
    margin-top: 24px;
  }
  .text-tip {
    font-size: 14px;
    color:  @main_text_color;
    line-height: 14px;
    margin-top: 16px;
  }
  .back-btn {
    width: 164px;
    height: 44px;
    border-radius: 22px;
    margin-top: 130px;
  }
}
</style>

