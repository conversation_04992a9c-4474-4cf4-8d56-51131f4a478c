/*
 * @Description: 公共路由，以下实例：请根据具体业务修改或无用删除
 * @Version: 0.1
 * @Autor: Chenyt
 */

export default [
  // {
  //   path: "/",
  //   name: "首页",
  //   meta: {
  //     keepAlive: true
  //   },
  //   component: () => import(/* webpackChunkName: "homeModule" */"@/views/home.vue")
  // },
  {
    path: "/",
    name: "首页",
    meta: {
      keepAlive: true
    },
    component: () => import(/* webpackChunkName: "homeModule" */"@/views/home-modules")
  },
  {
    path: "/digital-human",
    name: "厦门人社智慧小新",
    meta: {
      keepAlive: true
    },
    component: () => import(/* webpackChunkName: "homeModule" */"@/views/home-modules/digital-human/index.vue")
  },
  {
    path: "/digital-human2",
    name: "数字人2",
    meta: {
      keepAlive: true
    },
    component: () => import(/* webpackChunkName: "homeModule" */"@/views/home-modules/digital-human2/index.vue")
  },
  {
    path: "/virtual-human",
    name: "厦门人社智慧小新",
    meta: {
      keepAlive: true
    },
    component: () => import(/* webpackChunkName: "homeModule" */"@/views/home-modules/virtual-human/index.vue")
  },
  {
    path: "/new-digital-human",
    name: "厦门人社智慧小新",
    meta: {
      keepAlive: true
    },
    component: () => import(/* webpackChunkName: "homeModule" */"@/views/home-modules/virtual-human/new-digital-human.vue")
  },
  {
    path: "/service",
    name: "全部服务",
    meta: {
      keepAlive: true
    },
    component: () => import(/* webpackChunkName: "homeModule" */"@/views/home-modules/components/service-all")
  },
  {
    path: "*",
    name: "not-found",
    component: () => import(/* webpackChunkName: "notFoundModule" */"@/views/default.vue")
  },
  {
    path: "/dialogue",
    name: "边聊边办",
    meta: {
      keepAlive: true
    },
    component: () => import(/* webpackChunkName: "homeModule" */"@/views/home-modules/dialogue/index.vue")
  }
]

