<!--
 * @Description: 查看容器
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <van-field
      class="search-unit-field"
      v-model="formData.aab004"
      name="aab004"
      label="单位名称"
      placeholder="请点击搜索"
      type="textarea"      
      input-align="left"
      :autosize="{ maxHeight: 70, minHeight: 50 }" 
      :required="true"
      :readonly="true"
      @click="handleSelectUnit"
    />

    <!-- 单位名称查询弹窗 -->
    <van-popup v-model="showPicker" position="bottom">
      <van-search
        v-model.trim="unitName"
        show-action
        placeholder="请输入搜索关键词"
        @search="handleSearchUnit"
      >
        <template #action>
          <div @click="handleSearchUnit">搜索</div>
        </template>        
      </van-search>

      <van-picker
          show-toolbar
          :columns="unitList"
          @confirm="handleConfirmUnit"
          @cancel="showPicker = false"
        ></van-picker>
    </van-popup>
  </div>

</template>

<script>
import {commonApi} from "@/api"

export default {
  name: "y-search-unit",
  model: {
    prop: "formData"
  },
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      value: "",
      showAction: false,
      showPicker: false, //单位名称弹窗
      unitName: "", //单位名称
      unitList: [] //单位列表
    }
  },  
  created() {
    this.handleSearchUnit() // 查询单位名称
  },
  methods: {
    // 查询单位信息
    handleSearchUnit() {
      const params = {
        serviceName: "xytCommon_findAb01NewByName",
        aab004: this.unitName
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "res")
        const { rows } = res.map.data
        this.unitList = rows.map((item) => {
          return {
            text: item.aab004,
            value: item.aab001
          }
        })
      })
    },

    // 选择单位名称
    handleSelectUnit(type) { 
      this.showPicker = true
    },

    // 确认选择单位
    handleConfirmUnit(data) {
      this.showPicker = false
      const { text: aab004 } = data
      this.formData.aab004 = aab004
    }    
  }
}
</script>

<style lang="less" scoped>
/deep/.search-unit-field {
  position: relative;
  ::-webkit-input-placeholder {
    line-height: 30px;
  }  
  &::after {
    content: '';
    position: absolute;
    content: " ";
    right: 16px;
    bottom: 0;
    left: 16px;
    border-bottom: 0.5px solid #cecece;
    display: inline-block !important;
  }
}
</style>