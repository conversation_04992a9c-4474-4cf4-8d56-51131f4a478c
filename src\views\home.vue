<!--
 * @Description: 首页 --- 已废弃 --- ui重新设计
 * @Version: 0.1
 * @Autor: Chenyt
-->
<template>
  <div class="page-home">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">

      <div class="business-container">
        <div class="title">
          <img :src="require('@pic/home/<USER>')" alt="">
        </div>

        <div class="business-box">
          <div v-for="(item, index) in HEAD_NAV" class="business-item flex-c-c-c" :class="navClass(index)"
            :key="item.key" @click="handleClick(item)">
            <img :src="item.src" alt="">
            <span>{{ item.label }}</span>
          </div>
        </div>
      </div>

      <div class="content-box">
        <div class="banner-box">
          <van-swipe :autoplay="3000">
            <van-swipe-item v-for="(item, index) in banners" :key="index">
              <img v-lazy="item.logo" class="banner-img" @click="openPageByNew(item)" />
            </van-swipe-item>
          </van-swipe>
        </div>

        <div class="seek-box">
          <span class="seek-title">资讯中心</span>
          <span class="more" @click="showMoreFn">查看更多</span>
        </div>

        <div class="tabs-box">
          <van-tabs v-model="selectd">
            <van-tab v-for="(item, key) in bannerLabel" :key="key" :title="item.label" :name="item.key">
              <div class="tabs-item" v-for="(item, key) in news[selectd]" :key="key + selectd"
                @click="openPageByNew(item)">
                <div class="item-left">
                  <div class="item-top text-ellipsis-2 ellipsis-2">{{ item.title }}</div>
                  <div class="item-bottom">
                    <p class="item-tip">{{ item.source }}</p>
                    <p class="item-date">{{ item.publishDate }}</p>
                  </div>
                </div>
                <div class="item-right">
                  <img :src="item.logo" alt="">
                </div>
              </div>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script>
import { commonApi } from "@api"

const SITE_ID = "502746213777477" //网站id(默认值:502746213777477)
const ANCESTORS = "502747731423301" //父节点id(默认值:502747731423301)
const HEAD_NAV = Object.freeze([
  { label: "备案信息查看", url: "/info-filings", src: require("@pic/home/<USER>"), key: "infoFilings" },
  { label: "我要维权", url: "/labor-protect", src: require("@pic/home/<USER>"), key: "laborPortect" },
  { label: "我要学习", url: "/new-home", src: require("@pic/home/<USER>"), key: "newHome" },
  { label: "益鹭保", url: "/yilubao", src: require("@pic/home/<USER>"), key: "yilubao" }
])
const CATALOG_LIST = ["twzx", "twzxapp", "Floatingwindow"]
export default {
  name: "home",
  data() {
    return {
      refreshing: false,
      HEAD_NAV,
      select: "",
      active: 0,

      bannerLabel: [],
      selectd: "",
      news: {},
      banners: [],

      laborPortectUrl: "https://app.hrss.xm.gov.cn/SBServer/server/ldgx/enterXjyxt"
    }
  },
  computed: {
    navClass() {
      return (index) => {
        return `item${index + 1}`
      }
    }
  },
  created() {
    this.getCatalogFn()
    this.handleGotFaceResult()
  },
  methods: {
    handleGotFaceResult() {
      if (localStorage.getItem("faceVerifySuccess")) {
        const params = JSON.parse(localStorage.getItem("faceVerifySuccess"))
        commonApi.proxyApi(params).then((res) => {
          console.log("认证结果存储成功")
          localStorage.removeItem("faceVerifySuccess")
        })
      }
    },
    // 选择业务
    handleClick(item) {
      const { url, key } = item
      const businessList = ["none"]

      if (businessList.includes(key)) {
        this.$toast("功能建设中,敬请期待！")
        return
      }

      if (process.env.VUE_APP_ENV === "prod" && key === "laborPortect") { //我要维权
        window.location.href = this.laborPortectUrl
        return
      }

      this.$router.push(url)
    },
    // 获取栏目列表
    async getCatalogFn() {
      const params = {
        serviceName: "getCatalogTreeNode",
        siteId: SITE_ID,
        ancestors: ANCESTORS //父节点id(默认值:502747731423301)
      }
      const res = await commonApi.proxyApi(params)

      const result = res?.data || [] // 获取通知公告信息
      const resultList = result[0]?.children
      this.bannerLabel = []
      const spreadList = [] // 接口promise列表
      const bannerList = [] // tab列表

      resultList.forEach(item => {
        const params = {
          serviceName: "getContentList",
          siteId: SITE_ID, //网站id(默认值:502746213777477)
          catalogId: item.catalogId, //栏目id
          "pipe": "PC", //发布渠道
          "pageNumber": "1", //页码
          "pageSize": "10" //每页数量
        }

        const oneApi = commonApi.proxyApi(params).then(res => { //获取栏目内容列表
          const contentList = res?.data?.contentList

          if (item.alias === "twzxapp") { //轮播
            this.banners = contentList
          }

          if (!CATALOG_LIST.includes(item.alias)) { //资讯 tab列表
            bannerList.push({
              ...item,
              label: item.name,
              key: item.alias
            })
          }

          this.news[item.alias] = contentList //资讯 内容
        }).catch(err => {
          this.refreshing = false
        })

        spreadList.push(oneApi)
      })
      Promise.all(spreadList).then(() => {
        this.bannerLabel = bannerList.sort((prev, next) => {
          return prev.sortFlag - next.sortFlag
        })
      })

      this.selectd = "tzgg" // 默认选中 -- 通知公告
      this.refreshing = false
    },
    // 展示更多
    showMoreFn() {
      this.$router.push("/information-center")
    },
    openPageByNew(item) {
      if (item.linkFlag === "Y") {
        window.location.href = item.redirectUrl
        // window.open(item.redirectUrl)
      } else {
        this.$router.push({
          path: "/information-center/detail",
          query: {
            catalogId: item.catalogId,
            contentId: item.contentId,
            contentType: item.contentType
          }
        })
      }
    },
    onRefresh() {
      this.getCatalogFn()
    }
  }
}
</script>

<style lang='less' scoped>
.page-home {
  padding: 0 0 10px 0;
  background: @main_bg_color;
  box-sizing: border-box;

  .business-container {
    width: 100%;
    height: 460px;
    background: url("~@pic/home/<USER>") no-repeat center center/ 100% 100%;
    position: relative;

    .title {
      width: 330px;
      height: 78px;
      position: absolute;
      top: 24px;
      left: 42px;

      &>img {
        width: 100%;
        height: 100%;
      }
    }

    .business-box {
      width: 100%;
      height: 100%;

      .item1,
      .item2,
      .item3,
      .item4 {
        position: absolute;
        min-width: 84px;

        &>img {
          width: 45px;
        }

        &>span {
          font-size: 14px;
          color: @main_text_color;
          line-height: 20px;
        }
      }

      .item1 {
        top: 174px;
        left: 30px;
      }

      .item2 {
        top: 128px;
        left: 120px;
      }

      .item3 {
        top: 149px;
        left: 210px;
      }

      .item4 {
        top: 380px;
        left: 200px;
      }
    }
  }

  .content-box {
    padding: 0 16px 20px;
    background: @white_text_color;

    .banner-box {
      width: 100%;
      padding: 16px 0 0;

      .van-swipe-item {
        text-align: center;
      }

      .banner-img {
        max-width: 344px;
        max-height: 110px;
        object-fit: contain;
      }
    }

    .seek-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;

      .seek-title {
        font-size: 18px;
        font-weight: bold;
        color: @main_text_color;
        line-height: 25px;
      }

      .more {
        font-size: 14px;
        font-weight: bold;
        color: @five_text_color;
        line-height: 20px;
      }
    }

    .tabs-box {
      ::v-deep .van-tab {
        font-size: 16px;
        font-weight: 500;
        line-height: 22px;
      }

      ::v-deep .van-tabs__content {
        padding: 0 0 0 14px;
      }

      .tabs-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 0;

        &:not(:last-child) {
          border-bottom: 1px solid #EEEEEE;
        }

        .item-left {
          .item-top {
            font-size: 14px;
            font-weight: bold;
            color: @main_text_color;
            line-height: 20px;
          }

          .item-bottom {
            margin-top: 20px;

            .item-tip {
              font-size: 11px;
              font-weight: 400;
              color: @main_color;
              line-height: 16px;
            }

            .item-date {
              font-size: 12px;
              font-weight: 400;
              color: @five_text_color;
              line-height: 18px;
              //margin-left: 20px;
            }
          }
        }

        .item-right {
          width: 108px;
          margin-left: 18px;
          border-radius: 4px;

          &>img {
            width: 100%;
            max-width: 180px;
            object-fit: contain;
            border-radius: 4px;
          }
        }
      }
    }
  }
}
</style>
