<!--
 * @Description: 劳动维权--新增
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-protect-detail">
    <div class="info-container">
      <div class="info-box">
        <y-title :content="'编号：' + formData.bcz003" :colorMore="colorMore" fontWeight="bold">
          <template slot="right-box">
            <span class="right-box">
              <span class="point" style="background:white"></span>
              <span class="text" :style="{ 'color': STATUS[formData.abb300]['color'] }">{{
          STATUS[formData.abb300]['text']
        }}</span>
            </span>
          </template>
        </y-title>
        <van-cell-group :border="false">
          <van-cell title="纠纷类型" :value="getDictName(aba002List, formData.aba002)" :border="false" />
          <van-cell title="被申请人" :value="formData.aab004" :border="false" />
          <van-cell title="申请人" :value="formData.aac003" :border="false" />
          <van-cell title="调解员" :value="formData.tjyName" :border="false" />
          <van-cell title="开始调解时间" :value="dayFormatFn(formData.tjkssj, 'date')" :border="false" />
          <van-cell title="结束调解时间" :value="dayFormatFn(formData.tjjssj, 'date')" :border="false" />
        </van-cell-group>
      </div>
      <div class="business-container">
        <div class="business-box flex-c-sb mt10 pl20 pr20">
          <div class="business-item flex-c-c-c" v-for="(item, index) in businessList" :key="index"
            @click="handleJump(item)">
            <div class="imgdiv">
              <img :src="item.imgUrl" alt="">
              <span class="badge" v-if="item.number">{{ item.number }}</span>
            </div>
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>
      <div class="business-box">
        <van-steps direction="vertical" :active="stepList.length" active-color="#52C41A">
          <van-step v-for="(item, index) in stepList" :key="index">
            <template #active-icon>
              <div class="flag-icon"><img width="14" height="14" :src="require('@pic/labor-protect/<EMAIL>')" alt="">
              </div>
            </template>
            <template #inactive-icon>
              <div class="flag-icon"><img width="14" height="14" :src="require('@pic/labor-protect/<EMAIL>')" alt="">
              </div>
            </template>
            <template #finish-icon>
              <div class="flag-icon"><img width="14" height="14" :src="require('@pic/labor-protect/<EMAIL>')" alt="">
              </div>
            </template>
            <div v-html="item.msg" @click='handleJump(item)' class="step-text"></div>
            <div class="step-time">{{ item.time }}</div>
          </van-step>
        </van-steps>
      </div>
    </div>
  </div>
</template>

<script>

import { commonApi } from "@/api"
import {
  main_color
} from "@/styles/theme/theme-params.less"
import { getDictName } from "@utils/common"
const STATUS = {
  "000": {
    text: "启用",
    color: "#bd1a2d"
  },
  "001": {
    text: "启用",
    color: "#bd1a2d"
  },
  "002": {
    text: "停用",
    color: "#BD1A2D"
  }
}
export default {
  name: "labor-protect-handle",
  data() {
    return {
      active: 0,
      colorMore: main_color,
      businessList: [
        {
          businessIndex: "bc22Count",
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "证据",
          href: "/labor-protect-proof",
          number: 1
        },
        {
          businessIndex: "bc23Count",
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "文书/笔录",
          href: "/labor-protect-writ",
          number: 1
        },
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "更多",
          href: "/labor-protect-more"
        }
      ],
      stepList: [
        {
          msg: "正在进行视频调解，请加入！",
          time: "2024-03-10 16:30",
          href: ""
        },
        {
          msg: "申请人已上传证据材料，请调解员核实！",
          time: "2024-03-10 16:30",
          href: ""
        },
        {
          msg: "调解员已上传文书/笔录材料，请双方确认！",
          time: "2024-03-10 16:30",
          href: ""
        },
        {
          msg: "双方当事人已和解，并签写调解协议书！",
          time: "2024-03-10 16:30",
          href: ""
        },
        {
          msg: "本次调解已结束！",
          time: "2024-03-10 16:30",
          href: ""
        }
      ],
      formData: {
        bcz003: "",
        aba002: "",
        aab004: "",
        aac003: "",
        tjyName: "",
        tjkssj: "",
        tjjssj: "",
        abb300: "001"
      },
      aba002List: [],
      STATUS,
      proofList: [],
      timer: null,
      mainWord: ""
    }
  },
  watch: {
    active() {
      this.scrollToTop(0, 0, "auto")
    }
  },
  computed: {
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    },
    pageType() {
      return this.$route.query.pageType || ""
    },
    primaryKey() {
      return this.$route.query.bcz003 || ""
    }
  },
  created() {
    // 初始化表单数据
    // this.initFormData()
    this.getBc03Progress()
    this.$store.dispatch("meeting/activeMeeting")
    // 编辑和查看详情 查询已保存信息
    if (this.pageType === "add") {
      return
    }
    console.log(this.pageType, "this.pageType")

  },
  mounted() {
    this.getPlatformList() //查询字典列表
    // if (window.history && window.history.pushState) {
    //   if (window.history.length > 1) {
    //     const state = {
    //       key: Math.random() * new Date().getTime()
    //     }
    //     window.history.pushState(state, null, document.URL)
    //   }

    //   //给window添加一个监听事件popstate，拦截返回键，并执行方法 backFn
    //   window.addEventListener("popstate", this.backFn, false)
    // }
    // this.timer=setInterval(() => {

    // }, 3000)
    this.getBc03ById(this.primaryKey)
    this.getBc03ProgressCount(this.primaryKey)
  },
  methods: {
    // 查看字典对应名称
    getDictName,
    //查询字典列表
    getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["ABA002"]
      }
      commonApi.proxyApi(params).then((res) => {
        const { data } = res.map
        const dictInfo = {
          "ABA002": "aba002List"
        }
        for (const key in dictInfo) {
          this[dictInfo[key]] = data[key].map((item) => {
            return { label: item.aaa103, value: item.aaa102 }
          })
        }
      })
    },

    // 更新表单数据
    updateFormData(data) {
      this.formData = { ...this.formData, ...data }
    },

    // 拦截返回键
    backFn() {

      // 第一步或提交成功状态 返回上一页面
      this.$router.go(-1)
    },

    // 下一步
    handleNext(active) {
      this.active = active
      this.scrollToTop(0, 0, "auto")
    },

    // 保存
    handleSave(type) {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定保存信息为待提交状态",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        const abz200 = this.$refs.submitMaterial.materialNum
        const qmzp00Base64 = this.$refs.signConfirm.signature
        const mapObj = {
          "info": { ...this.formData },
          "material": { ...this.formData, abz200 },
          "sign": { ...this.formData, abz200, qmzp00Base64 }
        }

        const params = mapObj[type]
        delete params.createTime
        delete params.updateTime
        params.serviceName = "xytBc01_saveOrUpdateBc01"
        commonApi.proxyApi(params).then((res) => {
          this.$toast("保存成功！")
          setTimeout(() => {
            this.$router.go(-2)
          }, 300)
        })
      }).catch(() => { })
    },

    // 提交
    async handleSubmit() {
      this.successSubmit = true
      const abz200 = this.$refs.submitMaterial.materialNum
      console.log(abz200, "abz200")
      const qmzp00Base64 = this.$refs.signConfirm.signature

      const saveParams = {
        serviceName: "xytBc01_saveOrUpdateBc01",
        abz200,
        qmzp00Base64,
        ...this.formData
      }
      delete saveParams.createTime
      delete saveParams.updateTime

      const saveRes = await commonApi.proxyApi(saveParams) //保存接口
      console.log(saveRes, "saveRes")

      const { data } = saveRes.map
      const submitParams = {
        serviceName: "xytBc01_submitBc01",
        bcz001: data
      }

      const submitRes = await commonApi.proxyApi(submitParams) //提交接口
      console.log(submitRes, "submitRes")

      this.successText = "提交成功"
      this.isSuccess = true
    },

    // 详情页面返回
    handleRouterBack() {
      this.successSubmit = true
      this.$router.go(-1)
    },
    handleJump(item) {
      const { href } = item
      if (href == "labor-protect-video") {
        this.$store.dispatch("meeting/activeMeeting")
      } else {
        this.$router.push({ path: href, query: { bcz003: this.primaryKey } })
      }
    },
    // 查询在线调解信息详情
    getBc03ById(bcz003) {
      const params = {
        serviceName: "xytBc03Web_findBc03ById",
        bcz003
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "在线调解信息详情")
        const { data } = res.map
        for (const key in data) {
          data[key] === null && (data[key] = "")
        }

        this.formData = { ...this.formData, ...data }
      })
    },
    // 查询在线调解信息详情
    getBc03ProgressCount(bcz003) {
      const params = {
        serviceName: "xytBc03Web_getBc03ProgressCount",
        bcz003
      }
      commonApi.proxyApi(params).then((res) => {
        const { data } = res.map
        for (const key in data) {
          data[key] === null && (data[key] = "")
          this.businessList.find(item => item.businessIndex === key).number = data[key]
        }
      })
    },
    getBc03Progress() {
      const params = {
        serviceName: "xytBc03Web_getBc03Progress",
        bcz003: this.primaryKey
      }
      commonApi.proxyApi(params).then((res) => {
        const { data } = res.map
        console.log(data, "在线调解信息详情")
        for (const key in data) {
          data[key] === null && (data[key] = "")
          switch (data[key].type) {
          case 1:
            data[key].msg = data[key].msg.replace("请加入!", "<span  class='mainWord'>请加入!</span>")
            data[key].href = "labor-protect-video"
            break
          case 2:
            data[key].msg = data[key].msg.replace("证据材料", "<span  class='mainWord'>证据材料</span>")
            data[key].href = "labor-protect-proof"
            break
          case 3:
            data[key].msg = data[key].msg.replace("文书/笔录", "<span class='mainWord'>文书/笔录</span>")
            data[key].href = "labor-protect-writ"
            break
          case 4:
            data[key].msg = data[key].msg.replace("调解协议书", "<span class='mainWord'>调解协议书</span>")
            data[key].href = "labor-protect-writ"
            break
          default:
            break
          }
        }
        data.sort((a, b) => { return new Date(a.time) - new Date(b.time) })
        console.log(data, "在线调解信息详情处理结束")
        this.stepList = data
      })
    }
  },
  destroyed() {
    //销毁监听
    window.removeEventListener("popstate", this.backFn, false)
    clearInterval(this.timer)
  }
}
</script>

<style lang="less" scoped>
/deep/ .mainWord {
  color: #bd1a2d;
}

.info-box {
  margin-bottom: 16px;
  padding: 8px 8px 8px 8px;
  background: @white_text_color;

  /deep/.van-cell-group {
    .van-cell {
      padding: 0;

      .van-cell__title {
        color: @six_text_color;
      }

      .van-cell__value {
        color: @main_text_color;
      }
    }
  }

  /deep/.y-title {
    border-bottom: 0.5px solid #EEEEEE !important;

    .content {
      position: relative;
    }

    .right-box {
      position: absolute;
      right: 0;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;

      &>.point {
        width: 8px;
        height: 8px;
        border-radius: 4px;
        display: inline-block;
        margin-bottom: 1px;
      }

      &>.text {
        margin-left: 6px;
      }
    }
  }
}

// .labor-protect-detail {
//   .info-container {
//     padding: 24px 16px;

//     .business-container {
//       background-color: @white_bg_color;
//       padding: 8px 18px 12px;
//       border-radius: 4px;
//       margin-bottom: 16px;

//       .business-box .business-item {
//         .imgdiv {
//           position: relative;
//           height: 42px;

//           &>img {
//             width: 44px;
//           }

//         }

//         .item-title {
//           overflow-wrap: break-word;
//           color: rgba(51, 51, 51, 1);
//           font-size: 14px;
//           font-weight: normal;
//           text-align: center;
//           white-space: nowrap;
//           line-height: 20px;
//           margin: 4px 7px 0 9px
//         }
//       }
//     }
//   }
// }

.labor-protect-detail {
  .info-container {
    padding: 24px 16px;

    .business-container {
      background-color: @white_bg_color;
      padding: 8px 18px 12px;
      border-radius: 4px;
      margin-bottom: 16px;

      .business-box .business-item {
        .imgdiv {
          position: relative;

          &>img {
            width: 44px;
          }

          .badge {
            position: absolute;
            top: -5px;
            right: -8px;
            /* 可以调整数字大小和颜色 */
            font-size: 12px;
            background-color: #52C41A;
            color: white;
            border-radius: 50%;
            padding: 1px 5px;
            /* 设置为行内块元素以便支持居中对齐 */
            display: inline-block;
          }
        }

        .item-title {
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 14px;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 20px;
          margin: 4px 7px 0 9px
        }
      }
    }
  }
}

.step-text {
  overflow-wrap: break-word;
  color: rgba(48, 49, 51, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.step-time {
  overflow-wrap: break-word;
  color: rgba(153, 153, 153, 1);
  font-size: 14px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 8px 56px 0 0;
}

/deep/ [class*='van-hairline']::after {
  border: none;
}
</style>