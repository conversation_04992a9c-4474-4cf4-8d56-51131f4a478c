<!--
 * @Description: 
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div>
    <van-cell-group inset >
        <van-field
          v-model="formData.a"
          name="a"
          label="姓名"
          placeholder="请输入"
          :required="required"
          :rules="formRules.a"
        />

        <van-field
          v-model="formData.b"
          name="b"
          label="证件号码"
          placeholder="请输入"
          :required="required"
          :rules="formRules.b"
        />
    </van-cell-group>
  </div>
</template>

<script>
export default {
  model: {
    prop: "formData"
  },
  props: {
    formData: {
      type: Object,
      require: true
    }
  }, data() {
    return {
      formRules: {
        a: [{ required: true, message: "请输入1" }],
        b: [{ required: true, message: "请选1择" }]
      }
    }
  }

}
</script>

<style>

</style>