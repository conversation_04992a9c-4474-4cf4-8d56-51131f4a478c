/*
 * @Description: 基础接口
 * @Version: 0.1
 * @Autor: hwx
 */

import request from "../axios"
const baseUrl = process.env.VUE_APP_BASE_API_URL

/**
 * 获取网关token
 *
 * @param {*} data
 * @param {*} options
 * @returns
 */
export function getAuth(data = {}, options = {}) {
  return request.post(`${baseUrl}/h5Service/obtainToken`, data, { responseType: "getAuth" })
}

/**
 * 统一输出接口
 *
 * @param {*} data
 * @param {*} options
 * @returns
 */
export function proxyApi(params, options = { timeout: 20 * 1000 }) {
  return request.post(`${baseUrl}/business/proxy`, { ...params }, options)
}

/******************* TODO 以下为上线前对接用户体系接口，暂时先放着 *******************/

/**
 * @description: 解析token获取网厅用户信息
 * @return {*}
 */
export function parseToken(data, options = {}) {
  return request.post(`${baseUrl}/parse/parseToken`, data, options)
}

/**
 * 获取登录token
 *
 * @param {*} data.token
 * @param {*} options
 * @returns
 */
export function login(data) {
  return new Promise((resolve, reject) => {
    const params = {
      serviceName: "login",
      ...data
    }
    proxyApi(params, { headers: { Authorization: "Basic MjpYbXh5dEAyMDIz" } }).then(res => {
      resolve(res)
    }).catch(() => {
      reject()
    })
  })
}

/**
 * 根据aab001获取用户信息
 *
 * @param {*} params
 * @param {*} options
 * @returns
 */
export function setWwUserByAab001Temp(params = {}, options = {}) {
  return request.get(`/api/user/setWwUserByAab001Temp`, {
    params: params,
    ...options
  })
}

/**
 * 设置外网用户信息-需要先登录网关，参数为网关用户信息
 *
 * @param {*} params 网关用户信息
 * @param {*} options
 * @returns
 */
export function setWwUser(params = {}, options = {}) {
  return request.post(`/api/user/setWwUser`, {
    ...params
  }, options)
}

/**
 * 外部接口
 *
 * @param {*} params
 * @param {*} options
 * @returns
 */
export function externalApi(params = {}, options = {}) {
  return request.post(`/SBServer/wechat/third/createJsapiSignature`, {
    ...params
  }, options)
}

