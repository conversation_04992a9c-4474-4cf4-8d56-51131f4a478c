<!--
 * @Description: 产品特色
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="product-presentation" :class="{'pb-90': btnVisible}">
    <!-- <a href="#btn" class="direct">直达底部</a> -->
    <svg t="1735290429019" v-if="isTop" class="icon" @click="handleArrowClick" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2446" width="32" height="32"><path d="M512 42.666667c259.2 0 469.333333 210.133333 469.333333 469.333333s-210.133333 469.333333-469.333333 469.333333S42.666667 771.2 42.666667 512 252.8 42.666667 512 42.666667z m0 64C288.149333 106.666667 106.666667 288.149333 106.666667 512s181.482667 405.333333 405.333333 405.333333 405.333333-181.482667 405.333333-405.333333S735.850667 106.666667 512 106.666667z m162.133333 294.592c4.693333 0 8.533333 3.818667 8.533334 8.533333v66.346667a8.533333 8.533333 0 0 1-2.496 6.037333l-153.088 153.088-2.005334 1.770667a21.248 21.248 0 0 1-11.818666 4.437333h-2.517334a21.248 21.248 0 0 1-13.824-6.186667l-153.088-153.088a8.533333 8.533333 0 0 1-2.496-6.016v-66.389333a8.533333 8.533333 0 0 1 14.570667-6.037333l156.074667 156.074666 156.117333-156.074666a8.533333 8.533333 0 0 1 6.037333-2.496z" fill="#333333" p-id="2447"></path></svg>
    <svg t="1735290429019" v-else class="icon top"  @click="handleArrowClick" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2446" width="32" height="32"><path d="M512 42.666667c259.2 0 469.333333 210.133333 469.333333 469.333333s-210.133333 469.333333-469.333333 469.333333S42.666667 771.2 42.666667 512 252.8 42.666667 512 42.666667z m0 64C288.149333 106.666667 106.666667 288.149333 106.666667 512s181.482667 405.333333 405.333333 405.333333 405.333333-181.482667 405.333333-405.333333S735.850667 106.666667 512 106.666667z m162.133333 294.592c4.693333 0 8.533333 3.818667 8.533334 8.533333v66.346667a8.533333 8.533333 0 0 1-2.496 6.037333l-153.088 153.088-2.005334 1.770667a21.248 21.248 0 0 1-11.818666 4.437333h-2.517334a21.248 21.248 0 0 1-13.824-6.186667l-153.088-153.088a8.533333 8.533333 0 0 1-2.496-6.016v-66.389333a8.533333 8.533333 0 0 1 14.570667-6.037333l156.074667 156.074666 156.117333-156.074666a8.533333 8.533333 0 0 1 6.037333-2.496z" fill="#333333" p-id="2447"></path></svg>
    <div class="banner" id="top">
      <img src="@pic/yilubao/home/<USER>" alt="">
    </div>
    <div class="box-wrapper">
        <div class="box-container">
          <y-title content="保障详情" color-cont="#303133" font-weight="500" font-cont-size="16" :background-color="ylb_color" />
          <div class="line"></div>
          <div class="content">
            <div class="row">
              <div class="label">意外伤害</div>
              <div class="value">40万元</div>
            </div>
            <div class="row">
              <div class="label">意外医疗费用</div>
              <div class="value">3万元</div>
            </div>
            <div class="row">
              <div class="label">意外住院津贴</div>
              <div class="value">60元/日</div>
            </div>
          </div>
        </div>
    </div>
    <div class="ylb-page-wrapper">
      <y-title content="产品特色" :color-cont="ylb_color" font-weight="500" font-cont-size="16" :background-color="ylb_color"  />
    </div>
    <div class="protect-content">
      <img src="@pic/yilubao/home/<USER>" alt="">
    </div>
    
    <div v-if="btnVisible" class="bottom-btn" id="bottom">
      <div class="primary-wrapper">
        <div class="check-box">
          <van-checkbox v-model="checkbox" shape="square" />
        </div>
        <div class="outside-link">
          <span class="link" @click="handleLink">中国太平洋财产保险股份有限公司隐私政策</span>
        </div>
      </div>
      <van-button @click="handleClick" size="large" round block :class="{'btn-disabled': !checkbox}">立即投保</van-button>
      
    </div>
    <warn-dialog v-if="dialogVisible" :visible.sync="dialogVisible"></warn-dialog>
  </div>
</template>

<script>
import {PTIVACY_POLICY_URL} from "@/assets/data/url-config"
import { commonApi } from "@/api"

import {ylb_color} from "@/styles/theme/theme-params.less"
import WarnDialog from "./warn-dialog"

export default {
  name: "product-presentation",
  props: {
    btnVisible: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isTop: true,
      checkbox: false, // 同意协议复选框
      ylb_color,
      dialogVisible: false
    }
  },
  components: {
    WarnDialog
  },
  mounted() {
    this.dialogVisible = false
  },
  methods: {   
    handleArrowClick() {
      if (this.isTop) {
        const element = document.getElementById("bottom")
        const distanceToTop = element.offsetTop
        this.scrollToTop(distanceToTop, 0, "auto")
      } else {
        this.scrollToTop(0, 0, "auto")
      }

      this.isTop = !this.isTop
    }, 
    async handleClick() {
      this.isTop = !this.isTop
      if (!this.checkbox) {
        this.$toast("请先阅读并同意中国太平洋财产保险股份有限公司隐私政策")
        return
      }
      
      // 校验是否允许投保 （灵活就业人员等允许）
      const {zjhm00: aac002} = this.$sessionUtil.getItem("userInfo")
      const params = {
        serviceName: "xytDa01Web_checkApplyAvailableByAac002",
        aac002
      }
      const res = await commonApi.proxyApi(params)
      console.log(res, "res 校验是否允许投保")
      const {flag} = res.map.data
      if (!flag) {
        const message = "若为本人投保，则本人需先进行个人灵活从业信息备案，若为他人投保，请继续操作！"
        this.$dialog.confirm({
          title: "提示",
          message,
          showCancelButton: true,
          confirmButtonText: "去备案",
          cancelButtonText: "继续投保"
        }).then(async() => {
          this.$router.push("/info-filings")
        }).catch(async() => {
          this.handleNext()
        })  
      } else {
        this.handleNext()
      }
    },
    async handleNext() {      
      await this.$store.dispatch("insure/updateForm", null)
      this.dialogVisible = true
    },
    handleLink() {
      window.location.href = PTIVACY_POLICY_URL
    }
  }
}
</script>

<style lang="less" scoped>
.icon {
  position: fixed;
  top: 80%;
  z-index: 9;
  right: 16px;
  path{
    fill: #2cd8b8;
  }
}
.icon.top {
  transform: rotate(180deg);
}
.pb-90 {
  padding-bottom: 90px;
}
.btn-disabled {
  opacity: 0.5;
}
.product-presentation {
  background-color: #F5F6F6;
  .direct {
    position: fixed;
    font-size: 16px;
    color: red;
    top: 25%;
    right: 16px;
    box-shadow: 10px 10px 10px solid red;
  }
  .banner {
    height: 175px;
    width: 100%;
    display: flex;
    img {
      flex: 1;
    }
  }
  .box-wrapper {
    background-color: #F5F7FA;
    padding: 16px;
    .box-container {
      .line {
        height: 0;
        border-bottom: 1px dashed #CECECE;
      }
      padding: 20px 16px;
      box-shadow: 0px 6px 12px 0px rgba(164,174,185,0.16);
      border-radius: 12px;
      background-color: #fff;
      .content {
        margin-top: 21px;
      }
      .row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-bottom: 15px;
        &:last-child {
          margin-bottom: 0;
        }
        .label {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #303133;
          line-height: 16px;
        }
        .value {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #4077F4;
        }
      }
    }
  }
  .protect-content {
    display: flex;
    width: 100%;
    img {
      width: 100%;
    }
  }
  .bottom-btn {
    // height: 64px;
    padding: 0 16px 26px;
    // display: flex;
    background-color: #D6FBF3;
    .van-button {
      margin-top: 18px;
      flex: 1;
      background-color: @ylb_color;
      color: #fff;
    }
    .primary-wrapper {
      padding: 16px;
      display: flex;
      align-items: center;
      font-size: 14px;
      height: 20px;
      line-height: 20px;
      /deep/.van-checkbox__icon--checked .van-icon {
        color: #fff;
        background-color: @ylb_color;
        border-color: @ylb_color;
      }
      .outside-link {
        margin-left: 16px;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #333333;
        
        line-height: 1.6;
        .link {
          color: @ylb_color;
        }
      }
    }
  }
}
</style>