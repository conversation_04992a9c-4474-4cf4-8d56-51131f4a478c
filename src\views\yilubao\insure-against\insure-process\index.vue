<!--
 * @Description: 投保流程
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="ylb-container">
    <van-sticky>
      <van-tabs v-model="activeType" disabled>
        <van-tab v-for="(item, key) in tabList" :title="item.title" :name="item.type" :key="key"></van-tab>
      </van-tabs>
    </van-sticky>
    <component ref="component" v-model="current" :is="activeComponent" :btn-visible="false"></component>
  </div>
</template>

<script>
const TYPE_QUESION = "4" // 常见问题
const TYPE_PRESENTATION = "2" // 理赔指引
import ProductPresentation from "../product-presentation"
import AddPerson from "../add-person/index"
import AgreementContent from "../../agreement-content"
export default {
  name: "insurance-process",
  components: {
    ProductPresentation,
    AddPerson,
    AgreementContent
  },
  data() {
    return {
      activeType: "insure",
      tabList: [
        {
          title: "产品特色",
          type: "feature",
          component: "ProductPresentation"
        },
        {
          title: "我要投保",
          type: "insure",
          component: "AddPerson"
        },
        {
          title: "理赔指引",
          type: "guide",
          component: "AgreementContent"
        },
        {
          title: "常见问题",
          type: "question",
          component: "AgreementContent"
        }
      ]
    }
  },
  computed: {
    activeComponent() {
      return this.tabList.find(item => item.type === this.activeType)?.component || "AddPerson"
    },
    current() {
      return this.activeType === "question" ? TYPE_QUESION : TYPE_PRESENTATION
    }
  },
  watch: {
    activeType() {
      this.$refs.component.scrollToTop()
    }
  },
  mounted() {
    window.addEventListener("resize", this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize)
  },
  methods: {
    isIos() {
      var m = navigator.userAgent
      var isIos = !!m.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
      const result = isIos ? true : false
      return result
    },
    handleResize() {
      const pageHeight = document.getElementById("timePicker").clientHeight
      if (pageHeight < this.pageHeight) {
        //当软键盘弹起，在此处操作
        // if (!this.isIos()) {
          
        // }
      } else {
        //当软键盘收起，在此处操作
        if (!this.isIos()) {
          document?.getElementById("dayBox")?.onClick()
          console.log(document, "document")
        }
      }

    }
  }
}
</script>

<style>

</style>