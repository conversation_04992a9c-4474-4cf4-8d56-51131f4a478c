<template>
  <div>
    <div v-show="active" class="video-conference">
      <!-- 加载中的提示 -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner">
          <div class="spinner"></div>
          <span>加载中...</span>
        </div>
      </div>

      <div class="iframe-container">
        <iframe id="iframe" class="my-iframe" :src="iframeSrc" frameborder="0" @load="onIframeLoad"></iframe>
      </div>
    </div>

    <!-- 悬浮小点和提示文字 -->
    <div class="floating-dot" :class="{ minimized: !active }"
      :style="{ left: floatingDotPosition.x + 'px', top: floatingDotPosition.y + 'px' }" @mousedown="startDrag"
      @touchstart="startDrag" @click="onClickFloatingDot">
      <van-icon v-if="active" name="shrink" />
      <van-icon v-else name="tv-o" />
    </div>
    <div class="floating-dot-tooltip" :style="{ left: tooltipPosition.x + 'px', top: tooltipPosition.y + 'px' }">
      <div class="tooltip-content">
        {{ active ? '点击最小化会议' : '点击回到会议' }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "YVideoConference",
  data() {
    return {
      active: true,
      dragging: false,
      loading: true,
      iframeSrc: "https://app.hrss.xm.gov.cn/brt-webrtc", // iframe URL
      offset: { x: 0, y: 0 },
      floatingDotPosition: { x: 100, y: 100 }, // 初始位置
      tooltipPosition: { x: 160, y: 100 }, // 初始提示位置
      windowWidth: window.innerWidth,
      windowHeight: window.innerHeight,
      dotSize: 50, // 悬浮小点的尺寸
      dragThreshold: 5, // 区分点击和拖动的阈值
      startPosition: { x: 0, y: 0 }
    }
  },
  methods: {
    toggleFloatingDot() {
      this.active = !this.active
    },
    startDrag(event) {
      event.preventDefault() // 防止默认事件
      this.dragging = true
      const clientX = event.clientX || event.touches[0].clientX
      const clientY = event.clientY || event.touches[0].clientY
      this.offset.x = clientX - this.floatingDotPosition.x
      this.offset.y = clientY - this.floatingDotPosition.y
      this.startPosition = { x: clientX, y: clientY }
      document.addEventListener("mousemove", this.onDrag)
      document.addEventListener("touchmove", this.onDrag, { passive: false })
      document.addEventListener("mouseup", this.stopDrag)
      document.addEventListener("touchend", this.stopDrag)
    },
    onDrag(event) {
      if (this.dragging) {
        event.preventDefault()
        const clientX = event.clientX || event.touches[0].clientX
        const clientY = event.clientY || event.touches[0].clientY
        let newX = clientX - this.offset.x
        let newY = clientY - this.offset.y
        // 限制小点在屏幕范围内
        newX = Math.max(0, Math.min(newX, this.windowWidth - this.dotSize))
        newY = Math.max(0, Math.min(newY, this.windowHeight - this.dotSize))
        this.floatingDotPosition.x = newX
        this.floatingDotPosition.y = newY
        this.tooltipPosition.x = newX + this.dotSize + 10
        this.tooltipPosition.y = newY
      }
    },
    stopDrag(event) {
      this.dragging = false
      document.removeEventListener("mousemove", this.onDrag)
      document.removeEventListener("touchmove", this.onDrag)
      document.removeEventListener("mouseup", this.stopDrag)
      document.removeEventListener("touchend", this.stopDrag)

      // 判断是否为点击操作
      const endX = event.clientX || (event.changedTouches && event.changedTouches[0].clientX)
      const endY = event.clientY || (event.changedTouches && event.changedTouches[0].clientY)
      const movedDistance = Math.sqrt(Math.pow(endX - this.startPosition.x, 2) + Math.pow(endY - this.startPosition.y, 2))

      if (movedDistance < this.dragThreshold) {
        this.toggleFloatingDot()
      }

      // 不需要设置 showTooltip = true 因为工具提示始终可见
    },
    onClickFloatingDot(event) {
      // 防止在拖动时触发点击事件
      event.preventDefault()
    },
    onIframeLoad() {
      this.loading = false // 当 iframe 加载完成后隐藏加载提示
    }
  },
  destroyed() {
    // 清除事件监听器
    document.removeEventListener("mousemove", this.onDrag)
    document.removeEventListener("touchmove", this.onDrag)
    document.removeEventListener("mouseup", this.stopDrag)
    document.removeEventListener("touchend", this.stopDrag)
  }
}
</script>

<style scoped>
.video-conference {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 100;
  background-color: rgba(255, 255, 255, 1);
}

.iframe-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.my-iframe {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 101;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
  font-size: 14px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top-color: #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.floating-dot {
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  border-radius: 50%;
  position: fixed;
  z-index: 102;
  border: 2px solid #007bff;
  font-size: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.floating-dot.minimized {
  background-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.2);
}

.floating-dot:not(.minimized):hover {
  transform: scale(1.2);
  background-color: rgba(255, 255, 255, 1);
}

.floating-dot-tooltip {
  position: fixed;
  z-index: 103;
  background-color: #007bff;
  color: #fff;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
  white-space: nowrap;
  transition: opacity 0.3s ease;
  opacity: 1;
  /* 始终显示提示 */
}

.tooltip-content::before {
  content: '';
  position: absolute;
  left: -10px;
  /* 调整箭头位置 */
  top: 50%;
  transform: translateY(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: transparent #007bff transparent transparent;
  /* 箭头颜色 */
}
</style>
