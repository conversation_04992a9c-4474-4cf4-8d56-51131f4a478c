<template>
  <div class="disclaimer">
    <div class="disclaimer-box">
      <img src="@pic/home/<USER>/robot-hi.png" class="logo" />
      <div class="disclaimers-text-box">
        <img src="@pic/home/<USER>/disclaimers.png" class="disclaimers-text-bg" />
        <span class="disclaimers-title">温馨提示</span>
        <span class="disclaimers-text">{{ errorTip }}</span>
      </div>
    </div>
    <div class="disclaimer-countdown">
      <template>
        <img src="@pic/home/<USER>/countdown-bg.png" class="countdown-bg" />
        <img
          class="countdown-time"
          :src="countdownTime === 1 ? require('@pic/home/<USER>/countdown-1.png') : require('@pic/home/<USER>/countdown-2.png')"
          :class="{'countdown-time-1': countdownTime === 1}"
        />
        <span class="countdown-text">秒后返回首页</span>
      </template>
    </div>
  </div>  
</template>

<script>
export default {
  props: {
    errorTip: {
      type: String,
      default: "当前应用正在调试中，暂不支持访问。"
    },
    errorPage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      countdownTime: 2
    }
  },
  watch: {
    errorPage: {
      immediate: true,
      handler(nval) {
        if (nval) {
          setTimeout(() => {
            this.startCountdown()
          }, 1000)
        }
      }
    }
  },
  methods: {
    startCountdown() {
      this.countdownTimer = setInterval(() => {
        if (this.countdownTime > 1) {
          this.countdownTime--
        } else {
          this.$emit("toHome")
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      }, 1000)
    }
  }
}
</script>

<style lang="less" scoped>
.disclaimer {
  width: 100%;
  height: 100%;
  background: transparent url("~@pic/home/<USER>/disclaimer.png") no-repeat center / 100% 100%;
  position: relative;

  .disclaimer-box {
    position: relative;
    top: 20%;
    display: flex;
    flex-direction: column;

    .logo {
      width: 150px;
      position: relative;
      left: 138px;
    }

    .disclaimers-text-box {
      margin: 30px auto 0;
      position: relative;
      width: 100%;

      .disclaimers-text-bg {
        width: 100%;
      }

      .disclaimers-title {
        display: inline-block;
        position: absolute;
        left: calc(50% - 40px);
        top: 20px;
        background: linear-gradient(180deg,#ffffff, #ffeebb 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 18px;
        font-weight: 900;
      }

      .disclaimers-text {
        display: inline-block;
        max-width: calc(100% - 80px);
        position: absolute;
        left: 40px;
        top: 80px;
        text-indent: 2em;
      }
    }
  }

  .disclaimer-countdown {
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: right;

    .countdown-bg {
      width: 162px;
      position: relative;
    }

    .countdown-time {
      height: 30px;
      position: absolute;
      right: 130px;
      bottom: 55px;
    }
    .countdown-time-1 {
      margin-right: 5px;
      bottom: 54px;
    }

    .countdown-text {
      color: rgba(73,144,176,1);
      position: relative;
      right: 40px;
      bottom: 52px;
    }
  }
}
</style>