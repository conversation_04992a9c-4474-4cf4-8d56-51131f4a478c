<!--
 * @Description:
 * @Author: wujh
 * @date: 2024/5/11 17:38
 * @LastEditors: Please set LastEditors
-->
<template>
  <div class="person-center-container">
    <!-- 头部 -->
    <div class="head">
      <img :src="headerImg" class="img" />
      <div class="info">
        <p>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：{{ userInfo.xm0000 | desensitization("name") }}</p>
        <p>证件号码：{{ userInfo.zjhm00 | desensitization("idCard") }}</p>
        <p>联系方式：{{ userInfo.mobile | desensitization("mobile") }}</p>
      </div>
    </div>
    <van-collapse class="collapse" v-model="activeNames" accordion>
      <div class="readonly">
        <van-collapse-item title="就业登记信息" name="2" :value="activeNames === '2' ? '收起' : '展开'">
          <div class="card-body">
            <template v-if="form0.aab004">
              <y-title
                content="工作时间段"
                fontWeight="bold"
                pleft="18"
                font-cont-size="14"
                m-bottom="0"
                more-text="查看更多"
                color-more="#909399"
                @onMoreCilck="onMoreClick('/person-info/find-cd01')"
              />
              <van-divider />
              <van-field v-model="form0.aae036" name="就业时间" label="就业时间" label-width="70px" readonly/>
              <van-divider />
              <van-field v-model="form0.ccd017" name="失业时间" label="失业时间" label-width="70px" readonly/>
              <van-divider />
              <van-field class="long-van-field" v-model="form0.aab004" name="单位名称" label="单位名称" label-width="70px" readonly/>
            </template>
            <div v-else class="empty-style">
              <img src="@/assets/imgs/common/<EMAIL>" alt="">
              <p class="tips">暂无数据</p>
            </div>
          </div>
        </van-collapse-item>
      </div>
      <van-divider />
      <div class="readonly" @click.stop="openPageFnByType('3')">
        <van-collapse-item title="参保信息" name="3" :value="activeNames === '3' ? '收起' : '展开'" disabled />
      </div>
      <van-divider />
      <div class="readonly" @click.stop="openPageFnByType('8')">
        <van-collapse-item title="工伤保险" name="8" :value="activeNames === '8' ? '收起' : '展开'" disabled />
      </div>
      <van-divider />
      <van-collapse-item title="从业登记信息" name="1" :value="activeNames === '1' ? '收起' : '展开'">
        <div class="card-body">
          <template v-if="form.aab004">
            <y-title
              content="工作时间段"
              fontWeight="bold"
              pleft="18"
              font-cont-size="14"
              m-bottom="0"
              more-text="查看更多"
              color-more="#909399"
              @onMoreCilck="onMoreClick('/person-info/find-ce10')"
            />
            <van-divider />
            <van-field v-model="form.ccd006" name="从业时间" label="从业时间" label-width="140px" readonly/>
            <van-divider />
            <van-field v-model="form.ccd007" name="解除劳动从业时间" label="解除劳动从业时间" label-width="140px" readonly/>
            <van-divider />
            <van-field v-model="form.aab004" name="从业单位" label="从业单位" label-width="140px" readonly type="textarea" autosize/>
          </template>
          <div v-else class="empty-style">
            <img src="@/assets/imgs/common/<EMAIL>" alt="">
            <p class="tips">暂无数据</p>
          </div>
        </div>
      </van-collapse-item>
      <van-divider />
      <van-collapse-item title="益鹭保投保" name="4" :value="activeNames === '4' ? '收起' : '展开'">
        <div class="card-body">
          <template v-if="form1.dac003">
            <y-title
              content="投保时间段"
              fontWeight="bold"
              pleft="18"
              font-cont-size="14"
              m-bottom="0"
              more-text="查看更多"
              color-more="#909399"
              @onMoreCilck="onMoreClick('/person-info/find-da10')"
            />
            <van-divider />
            <van-field v-model="form1.dac003" name="益鹭保投保人" label="益鹭保投保人" label-width="140px" readonly/>
            <van-divider />
            <van-field v-model="form1.bxqsrq" name="投保起始时间" label="投保起始时间" label-width="140px" readonly/>
            <van-divider />
            <van-field v-model="form1.bxjzrq" name="投保截止时间" label="投保截止时间" label-width="140px" readonly/>
            <van-divider />
            <van-field v-model="form1.bfje00" name="投保总金额(元)" label="投保总金额(元)" label-width="140px" readonly/>
          </template>
          <div v-else class="empty-style">
            <img src="@/assets/imgs/common/<EMAIL>" alt="">
            <p class="tips">暂无数据</p>
          </div>
        </div>
      </van-collapse-item>
      <van-divider />
      <van-collapse-item title="维权信息" name="5" :value="activeNames === '5' ? '收起' : '展开'">
        <div class="card-body">

          <template v-if="form2.aac003">
            <y-title
              content="申请时间"
              fontWeight="bold"
              pleft="18"
              font-cont-size="14"
              m-bottom="0"
              more-text="查看更多"
              color-more="#909399"
              @onMoreCilck="onMoreClick('/person-info/find-bc01')"
            />
            <van-divider />
            <van-field v-model="form2.abb013" name="案件编号" label="案件编号" label-width="140px" readonly/>
            <van-divider />
            <van-field v-model="form2.aac003" name="申请人" label="申请人" label-width="140px" readonly/>
            <van-divider />
            <van-field v-model="form2.tjsj00" name="申请日期" label="申请日期" label-width="140px" readonly/>
            <van-divider />
            <y-select-dict v-model="form2.abb292" label="业务状态" dict-type="ABB292" label-width="140px" disabled />
          </template>
          <div v-else class="empty-style">
            <img src="@/assets/imgs/common/<EMAIL>" alt="">
            <p class="tips">暂无数据</p>
          </div>
        </div>
      </van-collapse-item>
      <van-divider />
      <van-collapse-item title="培训补贴" name="6" :value="activeNames === '6' ? '收起' : '展开'">
        <div class="card-body">
          <template v-if="form3.zsbh00">
            <y-title
              content="培训补贴信息"
              fontWeight="bold"
              pleft="18"
              font-cont-size="14"
              m-bottom="0"
              more-text="查看更多"
              color-more="#909399"
              @onMoreCilck="onMoreClick('/person-info/query-list')"
            />
            <van-divider />
            <van-field v-model="form3.zsbh00" name="证书编号" label="证书编号" label-width="140px" readonly/>
            <van-divider />
            <y-select-dict v-model="form3.jzbtlx" label="补贴申报类型" dict-type="JZBT_BTLX00" label-width="140px" disabled />
            <!--          <van-field v-model="form3.jzbtlx" name="补贴申报类型" label="补贴申报类型" label-width="140px" readonly/>-->
            <van-divider />
            <van-field v-model="form3.btzje0" name="补贴金额(元)" label="补贴金额(元)" label-width="140px" readonly/>
          </template>
          <div v-else class="empty-style">
            <img src="@/assets/imgs/common/<EMAIL>" alt="">
            <p class="tips">暂无数据</p>
          </div>
        </div>
      </van-collapse-item>
      <van-divider />
      <van-collapse-item title="职业伤害保险" name="7" :value="activeNames === '7' ? '收起' : '展开'">
        <div class="card-body">
            <template v-if="form4.ptqy00">
              <y-title
                content="职业伤害保险信息"
                fontWeight="bold"
                pleft="18"
                font-cont-size="14"
                m-bottom="0"
                more-text="查看更多"
                color-more="#909399"
                @onMoreCilck="onMoreClick('/person-info/find-zhbx')"
              />
              <van-divider />
              <van-field class="long-van-field" v-model="form4.ptqy00" name="平台企业" label="平台企业" label-width="140px" readonly/>
              <van-divider />
              <van-field v-model="form4.cbzt00" name="参保状态" label="参保状态" label-width="140px" readonly/>
              <van-divider />
              <van-field v-model="form4.grcbrq" name="个人参保日期" label="个人参保日期" label-width="140px" readonly/>
              <van-divider />
              <van-field v-model="form4.cbd000" name="参保地" label="参保地" label-width="140px" readonly/>
            </template>
            <div v-else class="empty-style">
              <img src="@/assets/imgs/common/<EMAIL>" alt="">
              <p class="tips">暂无数据</p>
            </div>
          </div>
      </van-collapse-item>
    </van-collapse>
  </div>
</template>

<script>
import {commonApi} from "@/api"
import { validateIdCard } from "@/utils/check"
export default {
  name: "person-center",
  data(){
    return {
      userInfo: {
        xm0000: "", // 姓名
        zjhm00: "", // 证件号码
        mobile: "" // 联系方式
      },
      activeNames: "1",
      // 就业登记信息
      form0: {
        aae036: "", // 就业时间
        ccd017: "", // 失业时间
        aab004: "" // 单位名称
      },
      form1: {
        time: "",
        company: ""
      },
      form2: {
        abb013: "",
        aac003: "",
        tjsj00: "",
        ywsb00: ""
      },
      form3: {
        zsbh00: "",
        jzbtlx: "",
        btzje0: ""
      },
      // 职业伤害保险
      form4: {
        ptqy00: "", // 平台企业
        cbzt00: "", // 参保状态
        grcbrq: "", // 个人参保日期
        cbd000: "" // 参保地
      },
      form: {},
      urlMap: {
        "2": "https://app.hrss.xm.gov.cn/SBServer/server/myJob",
        "3": "https://app.hrss.xm.gov.cn/SBServer/info/index",
        "8": "https://app.hrss.xm.gov.cn/SBServer/gsbx/dyxx/list"
      }
    }
  },
  computed: {
    headerImg() {
      const idNumber = this.userInfo.zjhm00
      const idCardLegal = validateIdCard(idNumber)
      if (!idCardLegal) {
        return require("@pic/home/<USER>/head-man.png")
      }
      if (idNumber.length === 15 && (idNumber[idNumber.length-1])%2===0 ||
        idNumber.length === 18 && +(idNumber[idNumber.length-2])%2===0
      ) {
        return require("@pic/home/<USER>/head-woman.png")
      }
      return require("@pic/home/<USER>/head-man.png")
    }
  },
  mounted() {
    console.log(this.userInfo)
    this.userInfo = {...this.$sessionUtil.getItem("userInfo")}
    this.init()
  },
  methods: {
    init(){
      const { zjhm00 } = this.userInfo
      // 就业登记信息
      this.queryJydjxxFn({ aac002: zjhm00 })
      // 从业登记信息
      this.findCe10ByPageFn({ aac002: zjhm00 })
      // 益鹭保投保
      this.findDa01WebByPageFn({ dac002: zjhm00 })
      // 维权信息
      this.findBc01ByPageFn({ aac002: zjhm00, page: 1, size: 1 })
      // 培训补贴s
      this.queryListFn({ aac002: zjhm00 })
      // 职业伤害保险
      this.queryZhBxxxFn({ aac002: zjhm00 })
    },

    findCe10ByPageFn(queryInfo){
      commonApi.proxyApi({
        serviceName: "xytPerson_findPersonCeByPage",
        ...queryInfo
      }).then((res) => {
        console.log("从业登记信息", { ...res })
        const item = res?.map?.data?.rows[0]
        if (!item){
          return
        }
        this.form = item || {}
      }).catch((err) => {
        console.error(err)
      })
    },

    // 查询就业登记信息
    queryJydjxxFn(queryInfo){
      commonApi.proxyApi({
        serviceName: "xytCd01_queryJydjxx",
        ...queryInfo
      }).then((res) => {
        console.log("查询就业登记信息", res)
        const item = res?.map?.data?.rows[0]
        if (!item){
          return
        }
        this.form0 = item || {}
      }).catch((err) => {
        console.error(err)
      })
    },

    // 职业伤害保险
    queryZhBxxxFn(queryInfo){
      commonApi.proxyApi({
        serviceName: "xytZh_queryZhBxxx",
        ...queryInfo
      }).then((res) => {
        console.log("职业伤害保险", res)
        const item = res?.map?.data?.rows[0]
        if (!item){
          return
        }
        this.form4 = item || {}
      }).catch((err) => {
        console.error(err)
      })
    },

    findDa01WebByPageFn(queryInfo){
      commonApi.proxyApi({
        serviceName: "xytDa01Web_findDa01WebByPage",
        ...queryInfo
      }).then((res) => {
        console.log("益鹭保投保", res)
        const item = res?.map?.data?.rows[0]
        if (!item){
          return
        }
        this.form1 = item || {}
      }).catch((err) => {
        console.error(err)
      })
    },

    findBc01ByPageFn(queryInfo){
      commonApi.proxyApi({
        serviceName: "xytBc01_findBc01ByPage",
        ...queryInfo
      }).then((res) => {
        const item = res?.map?.data?.rows[0]
        if (!item){
          return
        }
        this.form2 = item || {}
      }).catch((err) => {
        console.error(err)
      })
    },

    queryListFn(queryInfo){
      commonApi.proxyApi({
        serviceName: "xytjzbt_queryList",
        ...queryInfo
      }).then((res) => {
        console.log("培训补贴", res)
        const item = res?.data[0]
        if (!item){
          return
        }
        this.form3 = item || {}
      }).catch((err) => {
        console.error(err)
      })
    },
    onMoreClick(path){
      this.$router.push(path)
    },
    openPageFnByType(type){
      this.urlMap[type] && (window.open(this.urlMap[type]))
    }
  }
}
</script>

<style scoped lang="less">
.person-center-container{
  min-height: 100vh;
  background-image: url("~@pic/home/<USER>/person-bg.png");
  background-repeat: no-repeat;
  background-size: contain;
  padding: 20px 0 80px;
  .head {
    background-size: 100% 100%;
    color: #fff;
    padding: 20px 21px 50px;
    display: flex;
    align-items: center;
    line-height: 22px;
    font-size: 16px;
    .info{
      & > p{
        margin-bottom: 2px;
      }
    }
    .img {
      width: 58px;
      margin-right: 22px;
    }
  }
  /deep/ .collapse{
    border-top-left-radius: 14px;
    border-top-right-radius: 14px;
    overflow: hidden;
    padding-bottom: 80px;
    .readonly{
      .van-cell__title{
        cursor: pointer;
        color: #333;
      }
    }
    .van-cell{
      &::after{
        display: none;
      }
    }
    .van-collapse-item{
      &__wrapper{
        background-color: #F6F6F6;
        .van-collapse-item__content{
          background-color: transparent;
        }
      }
      &::after{
        display: none;
      }
    }
    .card-body{
      display: flex;
      flex-flow: column;
      justify-content: space-between;
      padding: 6px 10px 6px;
      border-radius: 8px;
      background-color: #fff;
      .menu-item{
        flex: 1;
        width: 0;
        max-width: calc(50% - 13px);
        min-width: 40%;
        margin-top: 12px;
        //width: 50%;
        //height: 34px;
        background: #F8E8EA;
        border-radius: 4px;
        color: #BD1A2D;
        text-align: center;
        line-height: 34px;
        font-size: 14px;
        &:nth-child(2n+1){
          margin-right: 26px;
        }
      }
      .empty-style{
        width: 100%;
        height: 220px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-flow: column;
        &>img{
          width: 156px;
          height: 108px;
        }
        .tips{
          font-size: 14px;
          font-weight: 400;
          color: #909399;
          text-align: center;
        }
      }
    }
    &::after{
      display: none;
    }

    .van-divider{
      margin: 0;
      border-color: #EEEEEE;
      background-color: #EEEEEE;
    }
  }
  /deep/ .long-van-field {
    .van-field__control {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>