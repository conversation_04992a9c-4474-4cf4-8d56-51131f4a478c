/*
 * @Description: 路由拦截
 * @Version: 0.1
 * @Autor: Chenyt
 */

import { parseUrl } from "@/utils/str-util"
import { setZhrsToken, getZhrsToken, setAuthToken, setToken } from "@/utils/cookie"
import SessionUtil from "@/utils/session-storage"
import { commonApi } from "@/api"

import * as businessApi from "@/api/modules/common-api"

const whiteList = ["/distributor-info", "/new-home", "/service", "/on-line-study", "/digital-human", "/dialogue", "/virtual-human", "/new-digital-human"] // 路由白名单
// TODO 删除这两个路由
whiteList.push("/meeting-demo-step1", "/meeting-demo-step2", "/labor-protect-verify-success")

// 页面路由拦截
async function beforeEachHandler(to, from, next) {

  // 路由白名单
  if (to.path !== "/" && whiteList.toString().toLowerCase().indexOf(to.path.toString().toLowerCase()) > -1) {
    next()
    return
  }

  //1、解析获取，智慧人社携带的token
  const { token } = parseUrl().search
  if (!token) {
    window.location.replace(process.env.VUE_APP_LOGIN_URL + window.location.href)
    return
  }

  if (token && (token === getZhrsToken())) {
    await checkDa05(to)
    next()
    return
  }

  setZhrsToken(token) //存储智慧人社token

  try {
    //2、通过智慧人社携带的token，获取用户信息及网关token
    const params = { serviceName: "parseToken", token: getZhrsToken(), type: "1" }
    const gatewayRes = await businessApi.proxyApi(params)
    const gatewayToken = gatewayRes.map.token //网关token
    setAuthToken(gatewayToken)
    SessionUtil.setItem("userInfo", { ...gatewayRes.map, token: "" })

    //3、调用登录接口，获取业务接口token
    const loginRes = await businessApi.login({ token: gatewayToken })
    const businessToken = loginRes.map["Access-Token"]
    setToken(businessToken) //业务接口token

    //4、设置用户信息
    if (businessToken) {
      const userInfo = SessionUtil.getItem("userInfo")
      await businessApi.proxyApi({ serviceName: "xytUser_setWwUser", ...userInfo })
    }
    await checkDa05(to)
    next()
  } catch (err) {
    console.log(err, "页面路由拦截接口err")
  }
}

// 演示替换到维修升级页面
function beforeEachToExample(to, from, next) {
  const pathMap = {
    "/home": "/exception?page=1&noheader=true"
  }
  if (pathMap[to.path]) {
    next(pathMap[to.path])
  } else {
    next()
  }
}

// 业务员信息校验
async function checkDa05(to) {
  const { rygh } = to.query
  if (!rygh) {
    return false
  }

  const params = {
    rygh00: rygh,
    serviceName: "xytDa05_checkDa05"
  }

  return await commonApi.proxyApi(params).then(res => {
    console.log(res, "res")
    const { data } = res.map
    SessionUtil.setItem("insuranceInfo", data)
    return data
  }).catch(() => false)
}

// 记录访问日志的函数
async function saveAccessLogs(type) {
  // 避免重复记录，检查会话存储中是否已记录
  const visitedPages = SessionUtil.getItem("visitedPages") || {}

  // 如果已经记录过且在当前会话中，则不再记录
  if (visitedPages[type]) {
    return
  }

  const { xm0000: username, zjhm00 } = SessionUtil.getItem("userInfo") || {}
  const params = {
    serviceName: "xytCommon_saveFw01",
    fwmk00: type, // 访问模块
    fwqd00: "2", // 访问渠道(1 pc端 2 微信端）
    usertype: "2", // 用户类型 1企业、2个人
    username, // 用户名称
    zjhm00 // 证件号码        
  }

  try {
    await commonApi.proxyApi(params)
    // 记录已访问，避免重复记录
    visitedPages[type] = true
    SessionUtil.setItem("visitedPages", visitedPages)
  } catch (error) {
    console.error("记录访问日志失败:", error)
  }
}

// 页面访问日志记录映射表 （001 新业态微信端首页 002 新业态pc端网站首页、003 人员信息备案登记 004 劳动维权 005 益鹭保投保 006 暖新地图）
const accessLogMap = {
  "/info-filings": "003", // 人员信息备案
  "/labor-protect": "004", // 劳动维权
  "/yilubao": "005", // 益鹭保投保
  "/trade-union-service": "006", // 暖新地图
  "/": "001" // 首页
}

// 访问日志记录处理
function accessLogHandler(to, from, next) {
  // 检查当前路由是否需要记录访问日志
  const logType = accessLogMap[to.path]
  if (logType) {
    saveAccessLogs(logType)
  }
  next()
}

export {
  beforeEachHandler,
  beforeEachToExample,
  accessLogHandler
}
