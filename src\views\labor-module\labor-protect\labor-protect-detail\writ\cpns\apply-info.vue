<!--
 * @Description: 文书/笔录
 * @Version: 0.1
 * @Autor: yc
-->

<template>
  <div class="apply-info">
    <div class="top-div">
      <span class="right-box">
        <van-button class="add-button flex-c-c" type="primary" @click="handleOpenAdd">
          <span class="ml2">新增</span>
        </van-button>
      </span>
    </div>
    <div v-if="writList?.length > 0 || wordsList?.length > 0">
      <van-form class="base-form" @failed="onFailed">
        <div v-for="(item, index) in writList" :key="index">
          <y-title :content="'调解协议书' + (index + 1)" moreText :colorMore="colorMore" :moreType="1">
          </y-title>
          <van-cell-group inset>
            <y-select-dict v-model="writList[index].wscllx" :rules="formRules.wscllx" disabled :required="required"
              dict-type="WSCLLX" label="类型" is-link />
            <!-- <van-field v-model="writList[index].zjcl00" name="zjcl00" label="编号" placeholder="请输入" :required="required"
              :rules="formRules.zjcl00" disabled /> -->
            <y-select-dict v-model="writList[index].qzqr01" :rules="formRules.qzqr01" dict-type="YES_NO"
              placeholder="请签字" :required="required" @click="openSign('签名1', index)" label="申请人签章确认" is-link />
            <y-select-dict v-model="writList[index].qzqr02" :rules="formRules.qzqr02" dict-type="YES_NO"
              placeholder="请签字" :required="required" @click="openSign('签名2', index)" label="被申请人签章确认" is-link />
            <y-select-dict v-model="writList[index].qzqr03" :rules="formRules.qzqr03" dict-type="YES_NO"
              placeholder="请签字" :required="required" @click="openSign('签名3', index)" label="调解员签章确认" is-link />
            <!-- <van-field v-show="false" v-model="wordsList[index].cjsj00" name="cjsj00" label="申请时间" placeholder="请输入"
               :rules="formRules.cjsj00" />    -->

            <van-field v-if="writList[index].cjsj00" v-model="writList[index].cjsj00" name="cjsj00" label="申请时间"
              placeholder="请输入" :formatter="formatterTime" :rules="formRules.cjsj00" disabled />

          </van-cell-group>
          <div class="btn-box flex-c-e border-bottom-wide">
            <van-button v-if="writList[index].qzqr01 == '1' && writList[index].qzqr02 == '1' && writList[index].qzqr03 == '1'"
              type="warning" class="info-button" @click="handlePrint(index)">打印</van-button>
            <!-- <van-button v-if="!(writList[index].qzqr01=='1'||writList[index].qzqr02=='1'||writList[index].qzqr03=='1')"  type="warning" class="info-button" @click="handleUpload(index)">文书上传</van-button> -->
          </div>
        </div>
        <div v-for="(item, index) in wordsList" :key="index">
          <y-title :content="'调解笔录' + (index + 1)" moreText :colorMore="colorMore" :moreType="1" />
          <van-cell-group inset>
            <van-field v-model="wordsList[index].zjmc00" name="zjmc00" label="笔录名称" placeholder="请输入"
              :disabled="wordsList[index].wsck00" :required="required" :rules="formRules.zjmc00" />
            <y-select-dict v-model="wordsList[index].zjlx00" :rules="formRules.zjlx00" :required="required"
              dict-type="ZJLX00" label="类型" is-link :disabled="wordsList[index].wsck00" />

            <van-field v-model="wordsList[index].slcl00" name="slcl00" label="数量" placeholder="请输入" :required="required"
              :rules="formRules.slcl00" :disabled="wordsList[index].wsck00" />

            <van-field v-model="wordsList[index].dwcl00" name="dwcl00" label="单位" placeholder="请输入" :required="required"
              :rules="formRules.dwcl00" :disabled="wordsList[index].wsck00" />
            <!-- <y-select-dict v-model="wordsList[index].dwcl00" :rules="formRules.dwcl00" :disabled="pageType === 'detail'"
              dict-type="dwcl00" label="单位" is-link /> -->

            <van-field v-if="wordsList[index].cjsj00" v-model="wordsList[index].cjsj00" name="cjsj00" label="上传时间"
              placeholder="请输入" :formatter="formatterTime" :rules="formRules.cjsj00" disabled />

          </van-cell-group>
          <div class="btn-box flex-c-e border-bottom-wide ">
            <van-button type="primary" @click="handleMaterials(index)">笔录材料</van-button>
          </div>
        </div>
      </van-form>
    </div>
    <y-empty v-else></y-empty>
    <van-popup class="evaluate-popup" v-model="isShowAddWrit" position="center" closeable
      :close-on-click-overlay="false">
      <div class="evaluate-box">
        <div class="evaluate-title">新增</div>
        <y-title content="类型" moreText :colorMore="colorMore" :moreType="1"></y-title>
        <van-radio-group v-model="wscllx">
          <van-radio name="001">文书</van-radio>
          <van-radio name="002">笔录</van-radio>
        </van-radio-group>
        <div class="evaluate-button flex-c-c">
          <van-button plain type="info" round @click="isShowAddWrit = false" native-type="button">
            关闭
          </van-button>
          <van-button @click="handleAdd" round block type="primary">
            确定
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  five_text_color
} from "@/styles/theme/theme-params.less"
import { validateIdCard, checkMobile } from "@utils/check"
import { debounce } from "lodash"

// import { commonApi } from "@/api"

export default {
  name: "apply-info",
  model: {
    prop: "writList"
  },
  props: {
    writList: {
      type: Array,
      require: true
    },
    wordsList: {
      type: Array,
      require: true
    },
    pageType: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      // 标题
      colorMore: five_text_color,
      showBaseInfo: true, //申请人基本信息
      showUnitInfo: true, //被申请人人单位信息
      showRequestInfo: true, //申请人请求

      // 表单
      formRules: {
        aac003: [{ required: true, message: "请输入" }],
        ccg981: [{ required: true, message: "请选择" }],
        aac002: [
          { required: true, message: "请输入" },
          {
            validator: validateIdCard,
            message: "请输入正确身份证号码",
            trigger: "onBlur"
          }
        ],
        aae005: [
          { required: true, message: "请输入" },
          {
            validator: checkMobile,
            message: "请输入正确的手机号",
            trigger: "onBlur"
          }
        ]
      },
      required: true,
      wscllx: "",
      isShowAddWrit: false,
      isLoading: false
    }
  },
  watch: {
    searchValue: {
      handler(val) {
        if (val) {
          this.filterData(val)
        }
      },
      immediate: true
    }
  },
  computed: {
  },
  async created() {
  },
  methods: {
    formatterTime(time) {
      return this.dayFormatFn(String(time), "YYYY-MM-DD HH:mm")
    },
    // 表单校验失败
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },

    // 保存 存储数据 返回
    handleSave() {
      this.$emit("handleSave", "info", this.materialNum)
    },

    // 取消
    handleCancle() {
      this.$router.go(-1)
    },

    // 下一步
    // handleNext() {
    //   this.$emit("handleNext", 1)
    // },

    handleAdd: debounce(function() {
      console.log("debounce")
      this.$emit("handleAdd", this.wscllx)
    }, 1000, {
      leading: true,
      trailing: false
    }),
    // handleAdd() {
    //   this.isLoading = true

    // },
    handleOpenAdd() {
      this.isShowAddWrit = true
    },
    handlePrint(item) {
      this.$emit("handlePrint", item)
    },
    handleUpload(item) {
      this.$emit("handleUpload", item)
    },
    handleMaterials(item) {
      this.$emit("handleMaterials", item)
    },
    openSign(item, index) {
      this.$emit("openSign", item, index)
    }
  }
}
</script>

<style lang="less" scoped>
.search-field {
  /deep/.van-field__control {
    text-align: left;
  }
}

.btn-box {
  // height: 46px;
  padding: 9px 14px 9px 0px;
  box-sizing: border-box;

  .van-button {
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 28px;
    width: 80px;
    height: 28px;
    padding: 0;
    border-radius: 14px;
    margin-left: 12px;
  }
}

.top-div {
  height: 30px;

  .right-box {
    position: absolute;
    right: 0;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    .van-button {
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 28px;
      width: 80px;
      height: 28px;
      padding: 0;
      border-radius: 14px;
      margin-left: 12px;
    }
  }
}

.base-form {
  .right-box {
    position: absolute;
    right: 0;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    &>.point {
      width: 8px;
      height: 8px;
      border-radius: 4px;
      display: inline-block;
      margin-bottom: 1px;
    }

    &>.text {
      margin-left: 6px;
    }
  }
}

.evaluate-popup {
  width: 80% !important;
  border-radius: 8px;

  .evaluate-box {
    .evaluate-title {
      text-align: left;
      font-size: 16px;
      margin: 16px 0;
      padding-left: 16px;
      font-weight: 600;
    }

    /deep/.y-title {
      margin-bottom: 0px !important;
    }

    .evaluate-button {
      padding: 12px 0;

      .van-button--primary {
        margin-left: 8px;
      }
    }

    .online-revoke-icon {
      display: flex;
      justify-content: center;
      color: #32ae57;
      font-size: 65px;
    }

    .online-revoke-icon {
      display: flex;
      justify-content: center;
      color: #32ae57;
      font-size: 65px;
    }

    .online-revoke-text {
      font-size: 12px;
      text-align: center;
      margin-bottom: 16px;
      margin-top: 4px;
    }

    /deep/.van-radio-group {
      padding: 4px 16px;
      font-size: 14px;
    }

    /deep/.van-field__control {
      text-align: right;
    }
  }
}
</style>