<!--
 * @Description: 招聘岗位
 * @Version: 0.1
 * @Author: hwx
-->
<template>
  <div class="post-feature">
    <!-- 搜索 -->
    <van-search
      v-model="input"
      show-action
      shape="round"
      placeholder="请输入岗位名称或单位名称关键字"
      @search="onSearch"
    >
      <template #action>
        <div class="search-btn" @click="onSearch" >搜索</div>
      </template>
    </van-search>
    <!-- banner区域 -->
    <div class="banner"></div>
    <!-- tab -->
    <div class="tabs-box">
      <van-sticky>
        <van-tabs v-model="postType" @click="tabsChangeFn">
          <van-tab
            v-for="(item, key) in tabList"
            :key="key"
            :title="item.label"
            :name="item.key"
          >
          </van-tab>
        </van-tabs>
        <!-- 筛选区域 -->
        <div class="filter-wrapper">
          <ConditionSelect v-for="(item, index) in filters" class="filter-item mr12" :key="`filter-${index}`" :data="item" @onActiveList="onActiveList"></ConditionSelect>
        </div>
        <van-cell v-if="postType==='flexible'" center title="零工地图" is-link value="" label="进入地图可查看附近零工信息" @click="$router.push('/flexible-map')">
          <template #icon>
            <img src="@pic/post-module/<EMAIL>" alt="零工地图">
          </template>
        </van-cell>
      </van-sticky>
      <van-list
        ref="vanList"
        v-model="loading"
        :finished="finished"
        :finished-text="list?.length > 0 ? '没有更多了' : ''"
        :immediate-check="false"
        @load="onLoad"
      >

      <div v-if="list?.length > 0">
        <div
          class="tabs-item"
          v-for="(item, key) in list"
          :key="key + postType"
        >
        <van-row>
          <van-col class="item-title text-ellipsis-2 ellipsis-2" span="12" @click="handleClickPost(item)">{{ item.gwmc00 }}</van-col>
          <van-col class="item-price" span="12">{{ item.salary }}</van-col>
        </van-row>
        <div class="label-list">
          <span class="label" v-if="item.countyName">{{item.countyName}}</span>
          <span class="label" v-if="item.workExperienceName">{{item.workExperienceName}}</span>
          <span class="label" v-if="item.educationName">{{item.educationName}}</span>
          <span class="label" v-if="item.natureName">{{item.natureName}}</span>
        </div>
        <van-row>
          <van-col class="inviter" span="24">            
            <img class="user-logo" src="@/assets/imgs/post-module/<EMAIL>" alt="">
            <span class="inviter-name">招聘者：{{ item.contacts }}</span>            
          </van-col>
        </van-row>
        <van-row class="item-bottom">
          <van-col span="16 text-ellipsis-2 ellipsis-2">
            {{ item.aab004 }}
          </van-col>
          <van-col class="text-align-right text-ellipsis-2 ellipsis-2" span="8">
            {{ item.countyName }}
          </van-col>
        </van-row>
        </div>
      </div>

      <y-empty v-else></y-empty>
        
      </van-list>
    </div>
  </div>
</template>

<script>
import ConditionSelect from "../components/condition-select"
import { commonApi } from "@/api"
import difference from "lodash/difference"
export default {
  components: {
    ConditionSelect
  },
  data() {
    return {
      input: "", // 搜索关键字
      postType: "daily", // 当查看模式 日常用工/灵活用工
      // tab列表
      tabList: [
        {
          label: "日常用工",
          key: "daily"
        },
        {
          label: "灵活用工",
          key: "flexible"
        }
      ],
      filters: [
        {
          title: "工作地点", // 筛选项目
          key: "county_codes",
          list: [ // 筛选子区域
            {
              type: "county_codes",
              title: "工作地点",
              list: [
                {
                  label: "集美区",
                  value: "350221"
                }, 
                {
                  label: "海沧区",
                  value: "350524"
                }
              ]
            }
          ]
        },
        {
          title: "单位性质",
          type: "company_nature_code",
          list: [
            {
              title: "工作经验", // 筛选项目
              key: "work_experience_code",
              list: []
            }
          ]
        },
        {
          title: "工作经验", // 筛选项目
          key: "work_experience_code",
          list: [ // 筛选子区域
            {
              type: "work_experience_code",
              title: "工作经验",
              list: [
                {
                  label: "在校生",
                  value: "350221"
                }, 
                {
                  label: "应届生",
                  value: "350524"
                }
              ]
            }
          ]
        },
        {
          title: "更多", // 筛选项目
          list: [ // 筛选子区域
            {
              type: "salary_range",
              title: "薪资要求",
              list: [
                {
                  label: "3k以下",
                  value: "0"
                }, 
                {
                  label: "3k-5k",
                  value: "1"
                },
                {
                  label: "5k-8k",
                  value: "2"
                }, 
                {
                  label: "8k-10k",
                  value: "3"
                },
                {
                  label: "11-12k",
                  value: "4"
                }, 
                {
                  label: "12k以上",
                  value: "5"
                }
              ]
            },
            {
              type: "salary",
              title: "学历要求",
              list: [
                {
                  label: "博士研究生",
                  value: "0"
                }, 
                {
                  label: "硕士研究生",
                  value: "1"
                }
              ]
            }
          ]
        }

      ],

      list1: [
        {
          title: "Java开发工程师",
          number: 10,
          price: "60/小时",
          label: ["全职", "不限经验", "不限学历"],
          inviter: "老王",
          company: "易联众民生科技有限公司",
          address: "集美区"

        }
      ],
      list: [],
      loading: false,
      finished: false,
      searchParams: {
        page: 1,
        size: 10
      }
    }
  },
  created() {
    this.getDicData()
    this.onSearch()
  },
  methods: {
    onActiveList(selectedInfo) {
      const conditions = {}
      for (const key in selectedInfo) {
        conditions[key] = selectedInfo[key].map(item => item.DICT_KEY)
      }
      this.searchParams = {
        ...this.searchParams,
        ...conditions
      }
      this.onSearch()
    },
    // 点击搜索
    onSearch() {
      this.list = []
      this.searchParams.page = 1
      this.finished = false
      this.findRcygByPage()
    },
    // 切换用工类型
    tabsChangeFn() {
      this.onSearch()
    },
    onLoad() {
      console.log("onLoad")
      this.searchParams.page++
      this.findRcygByPage()
    },
    // 查询列表信息
    findRcygByPage() {
      const serviceName = this.postType === "daily" ? "xytQzzp_findRcygByPage" : "xytQzzp_findLhygByPage"
      const params = {
        serviceName,
        gwmc00: this.input,
        gwdwmc: this.input,
        ...this.searchParams
      }
      this.loading = true
      commonApi.proxyApi(params).then((res) => {
        const {rows=[], total=0} = res.map.data
        this.list = [...this.list, ...rows]

        this.loading = false

        if (this.list.length >= total) {
          this.finished = true
        }
      }).catch(() => {
        this.loading = false
        this.finished = true
      })
    },
    // 查看岗位信息
    handleClickPost(item) {
      const {id, aab001} = item
      const {postType} = this
      this.$router.push({path: "/post-details", query: {id, aab001, postType}})
    },
    /**
     * @description: 获取字典数据
     * @param {*}
     * @return {*}
     * @author: T
     */
    getDicData() {
      commonApi.proxyApi({
        serviceName: "xytQzzp_getQzzpBmxx"
      }).then(res => {
        /* const {
          company_nature_code,
          county_codes,
          salary_range,
          education_code,
          welfare_code,
          work_experience_code
        } = res.map?.data || {} */
        const filterCondition = []
        const frontTitleMap = {
          county_codes: "工作地点",
          company_nature_code: "单位性质",
          work_experience_code: "工作经验"
        }
        
        for (const key in frontTitleMap) {
          res.map.data[key][0].DICT_VALUE = frontTitleMap[key]
        }
        const resKeyList = Object.keys(res.map.data)
        const frontKeyList = Object.keys(frontTitleMap)
        const otherKey = difference(resKeyList, frontKeyList)
        // 主要展示的筛选条件
        frontKeyList.map(key => {
          const { DICT_VALUE } = res.map.data[key][0]
          filterCondition.push({
            title: DICT_VALUE,
            list: [
              {
                type: key,
                title: DICT_VALUE,
                list: res.map.data[key].filter(item => item.DICT_KEY&&item.DICT_KEY !== "-1")
              }
            ]
          })
        })
        const otherFilters = {
          title: "更多",
          list: []
        }
        // “更多”展示的条件
        otherKey.map(key => {
          const { DICT_VALUE } = res.map.data[key][0]
          otherFilters.list.push(
            {
              type: key,
              title: DICT_VALUE,
              list: res.map.data[key].filter(item => item.DICT_KEY&&item.DICT_KEY !== "-1")
            }
          )
        })
        filterCondition.push(otherFilters)
        this.filters = filterCondition
      })
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep.van-cell {
  &__title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 700;
    font-size: 16px;
    color: #303133;
  }
  &__label {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #303133;
  }
  img {
    width: 62px;
    height: 62px;
    margin-right: 12px;
  }
}

.post-feature {
  height: 100%;  
  .search-btn {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #BD1A2D;
    line-height: 20px;
    text-align: right;
    font-style: normal;
  }
  .banner {
    margin: 16px;
    height: 110px;
    background: url("~@pic/post-module/<EMAIL>") no-repeat;
    background-size: 100%;
  }

  .tabs-box {
    background-color: #F6F6F6;
    min-height: calc(100vh - 186px);
    ::v-deep .van-tab {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      flex: unset;
      padding: 0 16px;
    }
    ::v-deep .van-tabs__content {
      padding: 0 0 0 14px;
    }
    .tabs-item {
      padding: 20px 16px;
      background-color: #fff;
      margin-bottom: 16px;
      .item-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #222327;
        text-align: left;
        font-style: normal;
      }
      .item-price {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #FF3B37;
        line-height: 22px;
        text-align: right;
        font-style: normal;
      }
      .label-list {
        padding-bottom: 18px;
        border-bottom: 1px solid #E5E5E5;
        margin-top: 16px;
        .label {
          padding: 5px;
          background: #F6F6F6;
          border-radius: 2px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          margin-right: 4px;
        }
      }
      .inviter {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        display: flex;
        align-items: center;
        margin-top: 11px;
        .user-logo {
          width: 12px;
          height: 15px;
        }
        .inviter-name {
          margin-left: 10px;
        }
        .icon {
          margin-right: 9px;
          width: 12px;
          height: 15px;
          border: 1px solid;
        }
      }
      .item-bottom {
        margin-top: 6px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        .text-align-right {
          text-align: right         ;
        }
      }
    }
    .filter-wrapper {
      background-color: #fff;
      display: flex;
      padding: 16px;
      .filter-item {
        width: 0;
        flex: 1;
        display: flex;
        span {
          flex: 1;
        }
        &:last-child {
          flex: none;
          width: 50px;
        }
      }
      
    }
  }
}

</style>