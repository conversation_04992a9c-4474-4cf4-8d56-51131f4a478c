<!--
 * @Description: 条件选择
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="selecte-condition-box">
    <van-tabs
      v-model="activeTab"
      ref="myTabs"
      @click="onTabClick"
      :class="{ 'init-tabs': activeTab === '' }"
    >
      <van-tab class="van-one222" title="" name=""></van-tab>
      <van-tab
        v-for="item in tabList"
        :key="item.name"
        :title="item.title"
        :name="item.name"
      ></van-tab>
    </van-tabs>

    <!-- 服务内容模态框 -->
    <van-popup v-model="showModal" position="top" :style="popupStyle">
      <div class="modal-content">
        <div class="content-box">
          <div v-show="activeTab === 'service'" class="flex-c-s">
            <div
              class="service-item"
              v-for="(item, index) in serviceList"
              :key="index"
              :style="{
                background: item.background,
                border: selectServices.includes(item.value)
                  ? `1px solid ${item.activeColor}`
                  : '',
              }"
              @click="handleClickService(item, 'selectServices')"
            >
              <img
                :src="
                  require(`@/assets/imgs/trade-union-service/service${item.value}.svg`)
                "
                alt=""
              />
              <span class="text">{{ item.name }}</span>
            </div>
          </div>
          <div v-show="activeTab === 'department'" class="flex-c-s">
            <div
              class="service-item"
              v-for="(item, index) in departmentList"
              :key="index"
              :style="{
                background: item.background,
                border: getDepartmentActive(item.value)
                  ? `1px solid ${item.activeColor}`
                  : '',
              }"
              @click="handleClickService(item, 'selectDepartments')"
            >
              <img
                :src="
                  require(`@/assets/imgs/trade-union-service/department${
                    index + 1
                  }.svg`)
                "
                alt=""
              />
              <span class="text">{{ item.name }}</span>
            </div>
          </div>
          <div v-show="activeTab === 'area'" class="flex-c-s">
            <div
              class="area-item flex-c-c"
              v-for="(item, index) in areaList"
              :key="index"
              :style="{
                background: selectAreas.includes(item.value)
                  ? '#F8E8EA'
                  : '',
                color: selectAreas.includes(item.value) ? '#BD1A2D' : '#333333'
              }"
              @click="handleClickService(item, 'selectAreas')"
            > 
              <img v-if="(index + 1) === areaList.length" src="@/assets/imgs/trade-union-service/coordinate.svg" alt="">
              {{ item.name }}
            </div>
          </div>
        </div>
        <div
          class="modal-close flex-c-c"
          @click="(showModal = false), (activeTab = '')"
        >
          <span>收起</span>
          <img class="arrow" src="@/assets/imgs/common/arrow.svg" alt="" />
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { commonApi } from "@/api"

export default {
  name: "trade-union-selecte-condition",
  data() {
    return {
      // tab
      activeTab: "", // 默认激活的tab
      tabList: [
        {
          title: "服务内容",
          name: "service"
        },
        {
          title: "主管部门",
          name: "department"
        },
        {
          title: "归属区",
          name: "area"
        }
      ],

      // 服务内容模态框
      showModal: false, // 控制服务内容模态框显示隐藏
      tabsHeight: 0, // Tabs组件的高度
      tabsTopOffset: 0, // 新增一个数据属性来存储距离顶部的距离

      // 服务内容列表
      serviceList: [
        {
          value: "01",
          name: "休息",
          background: "#FEF3E8",
          activeColor: "#FD8C1B"
        },
        {
          value: "02",
          name: "饮水",
          background: "#F0F4FE",
          activeColor: "#7FA4F5"
        },
        {
          value: "03",
          name: "如厕",
          background: "#F9F5FF",
          activeColor: "#9461E2"
        },
        {
          value: "04",
          name: "充电",
          background: "#EBFDFF",
          activeColor: "#28BFC9"
        },
        {
          value: "05",
          name: "急救药箱",
          background: "#FFF3F2",
          activeColor: "#FF7068"
        },        
        {
          value: "07",
          name: "求职招聘",
          background: "#EBFDFF",
          activeColor: "#087BC6"
        },
        {
          value: "08",
          name: "法律援助",
          background: "#FEF3E8",
          activeColor: "#FD8C1B"
        },
        {
          value: "09",
          name: "劳动仲裁",
          background: "#E9FEF5",
          activeColor: "#0AC673"
        },
        {
          value: "10",
          name: "阅读",
          background: "#EBFDFF",
          activeColor: "#28BFC9"
        },
        {
          value: "11",
          name: "纠纷调解",
          background: "#FFF3F2",
          activeColor: "#FF7068"
        },
        {
          value: "06",
          name: "其他",
          background: "#EAFBF4",
          activeColor: "#0AC673"
        },        
        {
          value: "view",
          name: "去地图看",
          background: "#F5F5F5",
          activeColor: ""
        }                
      ],
      selectServices: [], // 选中的服务内容

      // 主管部门列表
      departmentList: [
        {
          value: "004",
          name: "司法局",
          background: "#FEF3E8",
          activeColor: "#FD8C1B"
        },
        {
          value: "001",
          name: "市政园林",
          background: "#EAFBF4",
          activeColor: "#28BFC9"
        },
        {
          value: "002",
          name: "文旅局",
          background: "#F9F5FF",
          activeColor: "#9461E2"
        },
        {
          value: "006",
          name: "社工部",
          background: "#EBFDFF",
          activeColor: "#28BFC9"
        },
        {
          value: "rsj",
          name: "人社局",
          background: "#EFF9FF",
          activeColor: "#087BC6"
        },
        {
          value: "007",
          name: "总工会",
          background: "#FFF3F2",
          activeColor: "#FF7068"
        },
        {
          value: "008",
          name: "商务局",
          background: "#EBF1FF",
          activeColor: "#7FA4F5"
        },
        {
          value: "view",
          name: "去地图看",
          background: "#F5F5F5",
          activeColor: ""
        }
      ],
      selectDepartments: [], // 选中的主管部门
      multList: ["rsj"],
      multMap: {
        rsj: ["003", "005", "009", "010"]
      },

      // 归属区列表
      areaList: [],
      selectAreas: [] // 选中的归属区
    }
  },
  computed: {
    popupStyle() {
      return {
        top: `${this.tabsHeight + this.tabsHeight + 16}px`
      }
    }
  },
  created() {
    this.getDictList()
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.myTabs && this.$refs.myTabs.$el) {
        this.tabsHeight = this.$refs.myTabs.$el.offsetHeight // 获取Tabs组件的高度
        this.tabsTopOffset = this.$refs.myTabs.$el.offsetTop // 获取距离页面顶部的距离        
      }
    })
  },
  methods: {
    //查询字典列表
    async getDictList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["CAE026"]
      }
      const res = await commonApi.proxyApi(params)
      console.log(res, "88889999")
      const { CAE026 } = res.map.data

      // 归属区列表
      this.areaList = CAE026.map(item => ({
        value: item.aaa102,
        name: item.aaa103
      }))
      this.areaList.push({
        value: "view",
        name: "去地图看"
      })
    },
    async onTabClick(name) {
      this.showModal = true
      // 动态设置van-overlay的top样式
      await this.$nextTick()
      const overlay = document.querySelector(".van-overlay")
      overlay.style.top = `${this.tabsHeight * 2 + 20}px`
      overlay.style.backgroundColor = "rgba(0, 0, 0, 0.1)"
    },
    // 选择服务内容
    handleClickService(item, name) {
      const { value } = item

      if (value === "view") {
        // 去地图看
        this.$router.push({ path: "/trade-union-service-map" })
        return
      }
      
      if (this.multList.includes(value)) { // value为多个
        if (this[name].includes(this.multMap[value][0])) {        
          this[name] = this[name].filter((val) => !this.multMap[value].includes(val))
        } else {
          this[name] = [...this[name], ...this.multMap[value]]
        }
      } else { // value为单个
        if (this[name].includes(value)) {        
          const index = this[name].findIndex((val) => val === value)
          this[name].splice(index, 1)
        } else {
          this[name].push(value)   
        }
      }

      const {selectServices, selectDepartments, selectAreas} = this
      this.$emit("select", {selectServices, selectDepartments, selectAreas})
      console.log(this[name], "this.selectServices666")
    },
    getDepartmentActive(value) {      
      return this.multList.includes(value) ? this.selectDepartments.includes(this.multMap[value][0]) : this.selectDepartments.includes(value)
    }
  }
}
</script>

<style lang="less" scoped>
.selecte-condition-box {
  margin-top: 14px;
  padding: 0 14px;

  .van-tabs {
    .arrow {
      width: 10px;
      height: 6px;
      margin-left: 8px;
    }

    /deep/ .van-tabs__nav .van-tab {
      &:first-child {
        display: none;
      }
    }

    &.init-tabs {
      /deep/ .van-tabs__line {
        display: none;
      }
    }
  }

  .modal-content {
    width: 100%;
    position: relative;
    padding: 14px 14px 40px;

    .modal-close {
      width: calc(100% - 28px);
      font-size: 12px;
      color: #303133;
      line-height: 18px;
      position: absolute;
      bottom: 10px;

      & > .arrow {
        margin-left: 8px;
        width: 8px;
        height: 4px;
      }
    }

    .content-box {
      width: 100%;

      .service-item {
        width: 78px;
        height: 78px;
        background: #fef3e8;
        border-radius: 4px;
        margin: 0 10px 10px 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        &:nth-child(4n) {
          margin-right: 0;
        }

        & > img {
          width: 30px;
          height: 30px;
        }

        .text {
          font-size: 14px;
          color: #303133;
          line-height: 20px;
          margin-top: 4px;
        }
      }
      .area-item {
        width: 78px;
        height: 32px;
        background: #F5F5F5;
        border-radius: 4px;
        font-size: 14px;
        color: #333333;
        line-height: 32px;
        text-align: center;
        margin: 0 10px 10px 0;
        &:nth-child(4n) {
          margin-right: 0;
        }
        & > img {
          width: 14px;
          height: 14px;
          margin-right: 2px;
        }
      }
    }
  }
}
</style>
