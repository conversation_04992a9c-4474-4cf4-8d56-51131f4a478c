<!--
 * @Description: 添加仲裁诉求
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div>
    <van-form ref="baseForm" label-width="160" class="base-form" :disabled="pageType === 'details'" @failed="onFailed" @submit="handleSave">      

      <van-field
          v-model="formData.abb012Name"
          name="abb012"
          label="项目类型"
          placeholder="请选择"
          :required="true"
          :readonly="true"
          :rules="formRules.abb012"
          @click="handleClickEventType('abb012')"
        />

      <van-field
          v-if="specificList.length > 0"
          v-model="formData.abb013Name"
          name="abb013"
          label="具体事项"
          placeholder="请选择"
          :required="true"
          :readonly="true"
          :rules="formRules.abb013"
          @click="handleClickEventType('abb013')"
        />

      <van-field
          v-if="eventTypeList.length > 0"
          v-model="formData.abb014Name"
          name="abb014"
          label="事项类型"
          placeholder="请选择"
          :required="true"
          :readonly="true"
          :rules="formRules.abb014"
          @click="handleClickEventType('abb014')"
        />
        
      <van-field
        v-model="formData.kssj00"
        name="kssj00"
        label="开始时间"
        placeholder="请选择"
        :readonly="true"
        :rules="formRules.kssj00"
        @click="handleSelectDate('kssj00')"
      />

      <van-field
        v-model="formData.jssj00"
        name="jssj00"
        label="结束时间"
        placeholder="请选择"
        :readonly="true"
        :rules="formRules.jssj00"
        @click="handleSelectDate('jssj00')"
      />

      <van-field
        v-model="formData.sjje00"
        name="sjje00"
        label="标的金额（元）"
        placeholder="请输入"
        :required="true" 
        :rules="formRules.sjje00"
      />
      
      <van-field
        class="van-field-textarea"
        v-model="formData.abb016"
        name="abb016"
        label="仲裁诉求理由"
        placeholder="请输入诉求理由，字数500字以内"
        type="textarea"
        :required="true"
        :rules="formRules.abb016"
        maxlength="500"
        show-word-limit
        >
      </van-field>
      
      <div class="button-box-more" v-if="pageType !== 'details'">
        <van-button round block type="primary" native-type="submit">
          保存
        </van-button>
      </div>
    </van-form>

    <!-- 日期选择弹出层 -->
    <van-popup v-model="pickerShow" round position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        title="选择日期"
        @confirm="handleConfirmPicker"
        @cancel="pickerShow = false"
        />
    </van-popup>

    <van-popup v-model="showEventType" position="bottom">
      <van-picker show-toolbar :columns="pickerList" @confirm="handleConfirmEventType" @cancel="showEventType=false"> </van-picker>
    </van-popup>
    
  </div>
</template>

<script>
import dayjs from "dayjs"
import {commonApi} from "@/api"
import { checkMoneyNumber } from "@utils/check"
export default {
  name: "add-arbitrate",
  data() {
    return {
      // 表单信息
      formData: {
        abb012: "", //项目类型     
        abb013: "", //具体事项     
        abb014: "", //事项类型     
        kssj00: "", //开始时间     
        jssj00: "", //结束时间     
        sjje00: "", //标的金额（元）     
        abb016: "" //仲裁诉求理由       
      },
      formRules: {
        abb012: [{ required: true, message: "请选择项目类型" }],
        abb013: [{ required: true, message: "请选择具体事项" }],
        abb014: [{ required: false, message: "请选择事项类型" }],
        sjje00: [
          { required: true, message: "请输入标的金额" },
          {
            validator: checkMoneyNumber,
            message: "请输入正确金额",
            trigger: "onBlur"
          }
        ],
        abb016: [{ required: true, message: "请输入仲裁诉求理由" }]
      },
      
      // 日期选择
      pickerShow: false,
      currentDate: new Date(),
      pickerType: "",
      minDate: new Date(new Date().getFullYear() - 100, 0, 1),
      maxDate: new Date(),

      // 类型选择
      pickerList: [
        {text: "text", value: "123"}
      ],
      itemTypeList: [], //项目类型
      specificList: [], //具体事项
      eventTypeList: [], //事项类型 
      
      showEventType: false,
      popupType: ""
    }
  },
  computed: {
    // itemTypeList
    zcy000(){ //仲裁委员会
      return this.$route.query.zcy000
    },
    pageType(){
      return this.$route.query.pageType
    },
    bczms0(){ //请求描述表主键
      return this.$route.query?.bczms0 || ""
    }
  },
  async created() {
    await this.getPlatformList()    

    if (this.pageType !== "add") {
      this.getBc05QqmsById() // 查询详情
    }
  },
  methods: {
    //查询字典列表
    async getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["ABB012", "ABB013", "ABB014"]
      }
      const res = await commonApi.proxyApi(params)
      const {data} = res.map
      const dictInfo = {
        "ABB012": "itemTypeList",
        "ABB013": "specificList",
        "ABB014": "eventTypeList"
      }
      for (const key in dictInfo) {
        this[dictInfo[key]] = data[key].map((item) => {
          return {text: item.aaa103, value: item.aaa102}
        })
      }
    },
    // 查询详情
    getBc05QqmsById() {
      const {bczms0} = this
      commonApi.proxyApi({
        serviceName: "xytBc05Qqms_getBc05QqmsById",
        bczms0
      }).then((res) => {
        console.log("查询详情", res)
        this.formData = res.map.data  
        
        // 回显项目类型、具体事项、事项类型
        const {abb012, abb013, abb014} = this.formData
        const abb012Info = this.itemTypeList.find(item => item.value === abb012)
        this.formData.abb012Name = abb012Info.text
        this.popupType = "abb012"
        this.handleConfirmEventType(abb012Info)

        const abb013Info = this.specificList.find(item => item.value === abb013)
        this.formData.abb013Name = abb013Info.text
        this.popupType = "abb013"
        this.handleConfirmEventType(abb013Info)

        if (abb014) {
          const abb014Info = this.eventTypeList.find(item => item.value === abb014)
          this.formData.abb014Name = abb014Info.text
        } else {
          this.eventTypeList = []
        }
      })
    },
    onFailed() {
      this.$toast("请完善表单信息！")
    },
    // 选择日期
    handleSelectDate(type) {
      if (this.pageType === "details") {
        return 
      }
      this.pickerShow = true
      this.pickerType = type
    },
    handleConfirmPicker(val) {   
      this.formData[this.pickerType] = this.dayFormatFn(val, "date")
      const {kssj00, jssj00} = this.formData
      console.log(kssj00, "kssj00")
      console.log(jssj00, "jssj00")

      if (kssj00 && jssj00 && dayjs(kssj00, "YYYY-MM-DD") > dayjs(jssj00, "YYYY-MM-DD")) {
        this.formData[this.pickerType] = ""
        this.$dialog.alert({
          title: "提示",
          message: "开始时间应早于截止时间！",
          theme: "round-button"
        })        
      } else {
        this.formData[this.pickerType] = this.dayFormatFn(val, "date")
      }     
      this.pickerShow = false
    },

    // 选择项目类型 查具体事项
    changeAbb012(val) {
      console.log(val, "changeAbb012 val")
      const params = {
        aaa100: "ABB013",
        aaa102: val
      }
      this.getAa10ByAaa100AndAaa052(params)      
    },
    
    handleClickEventType(popupType) {     
      if (this.pageType === "details") {
        return 
      }
      
      const mapObj = {
        "abb012": this.itemTypeList,
        "abb013": this.specificList,
        "abb014": this.eventTypeList
      } 
      this.pickerList = mapObj[popupType]
      this.popupType = popupType
      this.showEventType = true
    },
    async handleConfirmEventType(data) {
      console.log(data, "data data")
      const {text, value} = data
      this.formData[this.popupType] = value
      this.formData[`${this.popupType}Name`] = text
      this.showEventType = false

      if (this.popupType === "abb012") {
        const params = {
          aaa100: "ABB013",
          aaa102: value
        }
        await this.getAa10ByAaa100AndAaa052(params)
      } else if (this.popupType === "abb013") {
        const params = {
          aaa100: "ABB014",
          aaa102: value
        }
        await this.getAa10ByAaa100AndAaa052(params)
      }
    },
    async getAa10ByAaa100AndAaa052(params) {
      const {aaa100} = params
      const res = await commonApi.proxyApi({
        serviceName: "xytCommon_getAa10ByAaa100AndAaa052",
        ...params
      })
      const {data=[]} = res.map || {}
      if (aaa100 === "ABB013") { //具体事项
        this.specificList = data.map(item => ({text: item.aaa103, value: item.aaa102}))  
        console.log(this.specificList, "this.specificList")
      } else { //事项类型
        this.eventTypeList = data.map(item => ({text: item.aaa103, value: item.aaa102}))
        console.log(this.eventTypeList, "this.eventTypeList")  
      }  
    },
    
    // 保存
    handleSave() {
      this.$dialog.confirm({
        title: "提示",
        message: "您是否确定保存",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        const {zcy000} = this
        commonApi.proxyApi({
          serviceName: "xytBc05Qqms_saveOrUpdateBc05Qqms",
          ...this.formData,
          zcy000
        }).then((res) => {
          console.log("保存", res)
          this.$dialog.alert({
            title: "提示",
            message: "保存成功！",
            theme: "round-button"
          })
            .finally(() => {
              this.$router.go(-1)
            })          
        }).catch((err) => {
          console.error(err)
        })
      }) 
    }
  }
}
</script>
<style lang="less" scoped>
.button-box-more {
  background: #fff;
}
/deep/.van-field-textarea {
  &::after {
    border-bottom: none !important;
  }
}
</style>
