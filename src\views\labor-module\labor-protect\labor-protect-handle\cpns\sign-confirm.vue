<!--
 * @Description: 签字确认
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="submit-material">
    <!-- 重要提示 -->
    <y-tips class="border-bottom-wide" :tipsList="tipsList"></y-tips>

    <y-title content="申请人签名"/>
    <div class="sign-box" v-if="pageType === 'detail'">
      <img :src="signImgSrc" alt="">
    </div>
    <y-signature v-else @confirm="handleSignconfirm" :isNeedTip="false" />

    <div class="button-box-more">
      <template v-if="pageType === 'detail'">
        <van-button plain type="info" @click="handleBack" native-type="button">
          上一步
        </van-button>
        <van-button plain type="info" @click="handleRouterBack">
          返 回
        </van-button>
      </template>

      <template v-else>
        <van-button plain type="info" @click="handleSave">
          保 存
        </van-button>
        <van-button plain type="info" @click="handleBack" native-type="button">
          上一步
        </van-button>
        <van-button  round block type="primary" native-type="submit" @click="handleSubmit">
          提 交
        </van-button>
      </template>      
    </div>
  </div>
</template>

<script>
import {
  five_text_color
} from "@/styles/theme/theme-params.less"

import YSignature from "@/components/plugins/y-signature"

export default {
  name: "submit-material",
  components: {
    YSignature
  },
  props: {
    signatureData: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      colorMore: five_text_color,

      showBaseInfo: true, //申请人基本信息
      showUnitInfo: true, //被申请人人单位信息
      showRequestInfo: true, //申请人请求

      tipsList: ["请用手写板或鼠标进行签字确认。"],
      signature: "", //签名base64
      signImgSrc: ""
    }
  },
  watch: {
    signatureData: {
      handler(val) {
        this.signImgSrc = val
      },
      immediate: true
    }
  },
  computed: {
    pageType() {
      return this.$route.query.pageType || ""
    }
  },
  methods: {
    // 保存 存储数据 返回
    handleSave() {
      this.$emit("handleSave", "sign")
    },
    // 上一步
    handleBack() {
      this.$emit("handleNext", 1)
    },
    // 提交
    handleSubmit() {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定提交！",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        this.$emit("handleSubmit")
      })      
    },
    // 签字确认
    handleSignconfirm(val) {
      console.log("签名", val)
      this.signature = val
    },
    // 返回
    handleRouterBack() {
      this.$emit("handleRouterBack")
    }
  }
}
</script>

<style lang="less" scoped>
.sign-box {
  width: 344PX;
  height: 212PX;
  margin: 0 auto;
  border: 1px dashed #cecece;
  & > img {
    width: 100%;
    height: 100%;
  }
}
.button-box-more {
  padding-top: 94px;
  background: #fff;
}
</style>