/*******
 * @Description: 个人中心
 * @Version: 0.1
 * @Autor: wujh
 */
export default [
  {
    path: "/person-info/find-ce10",
    name: "从业登记信息",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "personInfoModule" */"@/views/person-info/find-ce10")
  },
  {
    path: "/person-info/find-da10",
    name: "益鹭保投保",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "personInfoModule" */"@/views/person-info/find-da10")
  },
  {
    path: "/person-info/find-bc01",
    name: "维权信息",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "personInfoModule" */"@/views/person-info/find-bc01")
  },
  {
    path: "/person-info/find-cd01",
    name: "就业登记信息",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "personInfoModule" */"@/views/person-info/find-cd01")
  },
  {
    path: "/person-info/find-zhbx",
    name: "职业伤害保险信息",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "personInfoModule" */"@/views/person-info/find-zhbx")
  },
  {
    path: "/person-info/query-list",
    name: "技能提升",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "personInfoModule" */"@/views/person-info/query-list")
  }
]