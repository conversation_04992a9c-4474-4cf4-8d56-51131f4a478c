<!--
 * @Description: 劳动维权--撤回签名
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="sign-box">
    <y-title content="签名确认"/>

    <y-signature @confirm="handleSignconfirm" :isNeedTip="false" />

    <div class="button-box mt30">
      <van-button plain type="info" @click="handleCancle" native-type="button">
        取 消
      </van-button>
      <van-button  round block type="primary" native-type="submit" @click="handleSubmit">
        提交
      </van-button>
    </div>
  </div>
</template>

<script>
import YSignature from "@/components/plugins/y-signature"

export default {
  name: "revoke-sign",
  components: {
    YSignature
  },
  data() {
    return {
      formData: {},
      signature: "", //签名信息
      aae009: "" //撤回理由
    }
  },
  methods: {
    // 签字确认
    handleSignconfirm(val) {
      console.log("签名", val)
      this.signature = val
    },
    // 取消
    handleCancle() {
      this.$emit("handleNext", 0)
    },
    // 提交
    handleSubmit() {
      this.$dialog.confirm({
        title: "温馨提示",
        message: `是否提交撤回申请?`,
        cancelButtonText: "否",
        confirmButtonText: "是"
      }).then(async() => {
        this.$emit("handleSubmit", this.signature)
      })      
    }
  }
}
</script>

<style lang="less" scoped>
.sign-box {
  padding: 16px;
  .button-box {
      padding: 0;
      background-color: @white_text_color;
    }
}
</style>