<!--
 * @Description: 边聊边办
 * @Author: 吕志伟 <EMAIL>
 * @Date: 2025-06-09 11:02:32
 * @LastEditors: 吕志伟 <EMAIL>
 * @LastEditTime: 2025-06-18 17:11:27
-->
<template>
  <div>
    <div ref="chatContainer" :class="{ 'dialogue-container': true, 'dialogue-is-more': isMore }">
      <div v-for="(item, index) in messages" :key="index" class="message-box">
        <img v-if="item.role === 'assistant'" src="@pic/home/<USER>/profile-picture.png" class="assistant-icon" />
        <div :class="['message-content', item.role]">
          <div v-html="item.content"></div>
        </div>
        <div v-if="item.role === 'assistant' && item.stop" class="loading-dots">这条消息已停止</div>
      </div>

      <!-- 加载动画 -->
      <div v-if="isStreaming || lastStatus" class="loading-dots">
        <span>正在思考</span>
        <van-loading type="spinner" size="16px" style="margin-left: 5px;"/>
      </div>
    </div>

    <!-- 输入框区域 -->
    <div class="search-container">
      <div class="search-box" :class="{ 'search-box-active': searchValue, 'search-box-recording-active': isRecording }">
        <template v-if="!showVoice">
          <van-field
            v-if="isStreaming || lastStatus"
            value="正在接收消息…"
            :border="false"
            :disabled="isStreaming || lastStatus"
          />
          <van-field
            v-else
            ref="searchField"
            v-model.trim="searchValue"
            :placeholder="placeholder"
            :border="false"
            @focus="focusInput"
            @blur="placeholder = '发消息或按住说话…'"
          />
        </template>
        <template v-else>
          <div v-if="isStreaming || lastStatus" class="voice-recorder">正在接收消息…</div>
          <div
            v-else
            @touchstart="startRecording"
            @touchmove="handleTouchMove"
            @touchend="stopRecording"
            @touchcancel="touchcancel"
            class="voice-recorder"
          >
            <div v-if="isRecording" class="voice-animation">
              <div v-if="isWillCancel" class="voice-cancel">
                <span>松手取消</span>
              </div>
              <template v-else>
                <span v-for="n in 5" :key="'1-' + n" class="voice-dot"></span>
                <span v-for="n in 10" :key="'2-' + n" class="voice-dot voice-dot-active" :style="{ animationDelay: `${n * 0.2}s` }"></span>
                <span v-for="n in 5" :key="'3-' + n" class="voice-dot"></span>
              </template>
            </div>
            <template v-else>按住说话</template>
          </div>
        </template>
        <span v-if="!isRecording" class="icon-right">
          <template v-if="isStreaming || lastStatus">
            <img src="@pic/home/<USER>/pause-circle.svg" @click="stopCommunication" />
          </template>
          <template v-else>
            <template v-if="searchValue && !showVoice">
              <img src="@pic/home/<USER>/sending.svg" @click="uploadProblem" />
            </template>
            <template v-else>
              <img v-if="!showVoice" src="@pic/home/<USER>/voice.svg" @click="handleShowVoice" />
              <img v-else src="@pic/home/<USER>/keyboard.svg" @click="showKeyboard" />
              <img src="@pic/home/<USER>/plus.svg" :class="{ 'default-img': true, rotated: isMore }" @click="isMore = !isMore" />
            </template>
          </template>
        </span>
      </div>
      <div v-if="isMore" class="more-list">
        <div v-for="(item, index) in moreList" :key="index" class="more-item" @click="handleClickMore(item.sign)">
          <img :src="item.url" />
          <span>{{ item.text }}</span>
        </div>
      </div>
    </div>

    <div v-if="showVoice && isRecording" class="voice-prompt">松开发送 / 上滑取消</div>

    <van-popup v-model="showHistory" position="left" style="height: 100%;">
      <div class="history-title">
        <span class="title">历史记录</span>
        <div class="new-dialogue flex-c-c" @click="handleClickNewDialogue">
          <img src="@pic/home/<USER>/message-active.svg">
          <span>新对话</span>  
        </div>
      </div>
      <div class="history-container">
        <div v-for="(timeItem, timeIndex) in historyMessages" :key="timeIndex" class="history-time">
          <template v-if="timeItem.length">
            <div class="time">{{ timeLabel[timeIndex] }}</div>
            <div
              v-for="(item, index) in timeItem"
              :key="index"
              class="history-message"
              :class="item.messageId === currentDialogue ? 'history-item-active' : ''"
              @click="handleClickHistory(item.messageId)"
            >
              <img v-if="item.messageId === currentDialogue" src="@pic/home/<USER>/message-active.svg">
              <img v-else src="@pic/home/<USER>/message.svg">
              <span class="history-text">{{ item.messageTitle }}</span>
            </div>
          </template>
        </div>
        <div class="history-bottom flex-c-c">已加载全部</div>
      </div>
      <div class="user-bottom">
        <img src="@pic/home/<USER>/user.svg">
        <span>{{ userName }}</span>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { streamChatRequest } from "@/utils/stream-chat"
import xunfei from "@/utils/xfyun/xunfei"
import MarkdownIt from "markdown-it"
import { setYlzinsToken, getYlzinsToken } from "@/utils/cookie"
import { encryptApi } from "@/api"
const md = new MarkdownIt({
  html: true,
  linkify: true,
  xhtmlOut: true,
  typographer: true,
  breaks: true
})

export default {
  data() {
    return {
      messages: [
        { role: "assistant", content: "您好，我是厦门人社智慧小新！" }
      ],
      isStreaming: false,
      tempAssistantIndex: -1,
      searchValue: "",
      placeholder: "发消息或按住说话…",
      isMore: false,
      moreList: [
        { text: "历史", url: require("@pic/home/<USER>/history.svg"), sign: "history" }
      ],
      showVoice: false,
      message_id: Date.now(),
      showHistory: false,
      historyMessages: [],
      userName: "未登录",
      isRecording: false,
      url: "https://app.hrss.xm.gov.cn/ylzins-modelengine",
      checkInterval: null,
      startTime: 0,
      currentDialogue: "",
      timeLabel: {
        0: "今天",
        1: "昨天",
        2: "7 天内",
        3: "30 天内",
        4: "更早"
      },
      isStop: false,
      lastStatus: false,
      isWillCancel: false,
      encryptEnabled: false
    }
  },
  created() {
    this.encryptConfig()
  },
  activated() {
    this.userName = this.$sessionUtil.getItem("userInfo")?.xm0000 || "未登录"
    this.isRecording = false
    this.lastStatus = false
    this.isStop = false
    this.showVoice = false
    xunfei?.releaseMicrophone()
  },
  beforeDestroy() {
    xunfei?.releaseMicrophone()
  },
  watch: {
    isRecording: {
      handler(nval, oval) { 
        if (nval && !oval) {
          this.clearAllInterval()
        }
        if (!nval) {
          xunfei?.releaseMicrophone()
        }
      }
    }
  },
  methods: {
    encryptConfig() {
      encryptApi.encryptConfig().then(res => {
        this.encryptEnabled = res.data.enabled
        const token = this.getNestedTokenFromUrl(window.location.href)
        if (token) {
          this.getSsoWxLogin(token)
        }
      })
    },
    getNestedTokenFromUrl(url) {
      const href = url
      const tokenMatch = href.match(/[?&]token=([^&#]+)/)
      return tokenMatch ? decodeURIComponent(tokenMatch[1].replace(/\+/g, " ")) : null
    },
    getSsoWxLogin(token) {
      encryptApi.ssoWxLogin({ token: token }, { encryptEnabled: this.encryptEnabled }).then(res => {
        if (res?.data?.errorCode == 0) {
          setYlzinsToken(res.data.data.token)
          this.userName = res.data.data.nickname || "未登录"
        }
      })
    },
    async getXunFeiData() {
      if (!this.isRecording) {
        return
      }
      this.isRecording = false
      const endTime = Date.now()
      if (endTime - this.startTime <= 500) {
        this.$toast.fail("说话时间太短")
        return
      }
      this.isStreaming = true
      xunfei.setMediaRecorder()
      try {
        this.checkInterval = setInterval(() => {
          if (xunfei.getIsEnd()) {
            const result = xunfei.getUserInputMsg()
            this.searchValue = result
            if (!this.searchValue) {
              this.$toast.fail("未识别到语音，请重试")
            }
            this.uploadProblem()
            this.clearAllInterval()
          }
        }, 300)
      } catch (err) {
        this.$toast.fail("语音识别失败，请重试")
      } finally {
        this.isStreaming = false
        xunfei?.releaseMicrophone()
      }
    },
    startRecording() {
      if (this.isRecording) {
        return
      }
      this.startTime = Date.now()
      this.isRecording = true
      xunfei.start()
    },
    stopRecording() {
      if (this.isWillCancel) {
        // 取消录音
        this.isRecording = false
        this.isWillCancel = false
        xunfei?.releaseMicrophone()
        this.clearAllInterval()
        return
      }
      this.getXunFeiData()
      this.isWillCancel = false
    },
    touchcancel() {
      this.isRecording = false
      this.isWillCancel = false
      xunfei?.releaseMicrophone()
      this.clearAllInterval()
    },
    handleTouchMove(e) {
      // 手指滑动出按钮区域时取消录音
      const buttonRect = e.currentTarget.getBoundingClientRect()
      const touch = e.touches[0]
      const x = touch.clientX
      const y = touch.clientY

      if (
        x < buttonRect.left ||
        x > buttonRect.right ||
        y < buttonRect.top ||
        y > buttonRect.bottom
      ) {
        if (!this.isWillCancel) {
          this.isWillCancel = true
        }
      } else {
        if (this.isWillCancel) {
          this.isWillCancel = false
        }
      }
    },
    getDialogueList() {
      if (this.isStreaming || this.lastStatus) {
        return
      }
      this.isStop = false
      const userInfo = this.$sessionUtil.getItem("userInfo")
      const session_id = this.$sessionUtil.getItem(process.env.VUE_APP_ZHRS_TOKEN)
      
      this.messages.push({
        role: "user",
        content: this.searchValue
      })
      this.searchValue = ""
      this.clearAllInterval()

      const param = {
        model: "ylzins-model",
        messages: JSON.parse(JSON.stringify(this.messages.filter(m => m.content))),
        message_id: String(this.message_id),
        session_id,
        user_id: userInfo?.zjhm00,
        max_tokens: 4000,
        stream: true,
        dialogueCustom: true
      }
      
      this.isStreaming = true
      this.lastStatus = true
      this.tempAssistantIndex = this.messages.length

      // 创建本轮请求专用的状态对象
      const typingState = {
        charQueue: [],
        isTyping: false,
        currentContent: ""
      }

      this.messages.push({
        role: "assistant",
        content: ""
      })
      const container = this.$refs.chatContainer
      // 判断是否已经处于底部（允许一定的误差）
      const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 10

      if (isAtBottom) {
        this.scrollToBottom()
      }

      streamChatRequest(
        `${this.url}/api/v1/chat/completions`,
        param,
        (content, index) => {
          if (!this.messages[index].stop && !this.isStop && content) {
            // 将新内容拆分为字符队列
            typingState.charQueue = typingState.charQueue.concat(content.split(""))
            // 启动打字机逻辑
            if (!typingState.isTyping) {
              this.startTyping(typingState, index)
            }
          }
        },
        () => {
          this.isStreaming = false
        },
        (err) => {
          this.isStreaming = false
          this.lastStatus = false
          this.messages[err.index].content = `${err.message}`
        },
        this.tempAssistantIndex,
        {
          "ylzins-token": getYlzinsToken(),
          "Authorization": "Bearer " + process.env.VUE_APP_AUTHORIZATION
        }
      )
    },
    startTyping(typingState, index) {
      typingState.isTyping = true

      const interval = setInterval(() => {
        const container = this.$refs.chatContainer
        // 判断是否已经处于底部（允许一定的误差）
        const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 10

        this.lastStatus = true
        if (typingState.charQueue.length === 0 || this.messages[index].stop) {
          clearInterval(interval)
          typingState.isTyping = false
          this.lastStatus = false
          return
        }

        const char = typingState.charQueue.shift()
        typingState.currentContent += char

        const regex = /\[\[(.*?)]]/g
        const replacedText = typingState.currentContent.replace(regex, (_, p1) => {
          return `<a style="color: #2a64f6;" href="${p1}">一键去办理</a>`
        })

        this.$set(this.messages, index, {
          role: "assistant",
          content: md.render(replacedText)
        })
        if (isAtBottom) {
          this.scrollToBottom()
        }
      }, 50)
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.chatContainer
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: "smooth"
          })
        }
      })
    },
    focusInput() {
      this.placeholder = "发消息…"
      this.isMore = false
    },
    showKeyboard() {
      this.showVoice = false
    },
    uploadProblem() {
      if (!this.searchValue) {
        return
      }
      this.getDialogueList()
    },
    handleClickMore(sign) {
      if (sign === "history") {
        this.showHistory = true
        this.aiBotMessage()
      }
    },
    aiBotMessage() {
      this.historyMessages = []
      encryptApi.aiBotMessageList({}, { encryptEnabled: this.encryptEnabled }).then(res => {
        if (res?.data?.errorCode == 0) {
          const todayStart = []
          const yesterdayStart = []
          const sevenDaysAgo = []
          const ThirtyDaysAgo = []
          const earlier = []
          const now = Date.now()
          const oneDay = 24 * 60 * 60 * 1000
          if (res?.data?.data && res?.data?.data.length > 0) {
            res.data.data.forEach(item => {
              const diff = now - Number(item.createAt)
              if (diff <= oneDay) {
                todayStart.push(item)
              } else if (diff <= oneDay * 2) { 
                yesterdayStart.push(item)
              } else if (diff <= oneDay * 7) { 
                sevenDaysAgo.push(item)
              } else if (diff <= oneDay * 30) { 
                ThirtyDaysAgo.push(item)
              } else { 
                earlier.push(item)
              }
            })
            this.historyMessages = [todayStart, yesterdayStart, sevenDaysAgo, ThirtyDaysAgo, earlier]
          }
        } else {
          res.data.message && this.$toast.fail(res.data.message)
        }
      })
    },
    handleShowVoice() {
      this.showVoice = true
      this.isMore = false
    },
    handleClickHistory(id) {
      this.currentDialogue = id
      this.isMore = false
      this.showHistory = false
      this.getMessageList(id)
    },
    getMessageList(id) {
      const session_id = this.$sessionUtil.getItem(process.env.VUE_APP_ZHRS_TOKEN)
      encryptApi.getMessageList({
        session_id: session_id,
        messageId: id
      }, { encryptEnabled: this.encryptEnabled }).then(res => {
        if (res?.data?.errorCode == 0) {
          if (res.data.data && res.data.data.length > 0) {
            this.messages = []
            const data = res.data.data
            data.forEach(item => {
              let content = item.content
              const regex = /\[\[(.*?)]]/g
              content = content.replace(regex, (_, p1) => {
                return `<a style="color: #2a64f6;" href="${p1}">一键去办理</a>`
              })
              this.messages.push({
                ...item,
                content: md.render(content)
              })
            })
            this.message_id = res.data.data[0]?.messageId
            this.scrollToBottom()
          }
        } else {
          res.data.message && this.$toast.fail(res.data.message)
        }
      })
    },
    handleClickNewDialogue() {
      this.messages = this.$options.data().messages
      this.showHistory = false
      this.showVoice = false
      this.isMore = false
      this.message_id = Date.now()
    },
    stopCommunication() {
      this.isStreaming = false
      this.lastStatus = false
      this.isStop = true
      this.messages[this.tempAssistantIndex].stop = true
    },
    clearAllInterval() {
      if (this.checkInterval) {
        clearInterval(this.checkInterval)
        this.checkInterval = null
      }
    }
  }
}
</script>

<style lang="less" scoped>
.dialogue-container {
  padding: 20px 20px 10px;
  height: calc(100vh - 80px); /* 或者使用 max-height + overflow-y: auto */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
  display: flex;
  flex-direction: column;
  
  .message-box {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .assistant-icon {
      width: 40px;
      position: relative;
      left: -10px;
    }

    .message-content {
      padding: 10px 15px;
      font-size: 14px;
      line-height: 1.6;
      border-radius: 15px;
      max-width: calc(100% - 25px);
      word-break: break-all;
      position: relative;

      &.assistant {
        background-color: #f5f7fa;
        align-self: flex-start;
        border-top-left-radius: 0;
      }
      
      &.user {
        background-color: #2a64f6;
        align-self: flex-end;
        color: #fff;
        border-bottom-right-radius: 0;
      }
    }
  }

  .loading-dots {
    display: flex;
    align-items: center;
    padding: 0 10px;
    font-size: 14px;
    color: #999;
  }
}

.dialogue-is-more {
  height: calc(100vh - 182px);
}

.search-container {
  width: 100%;
  padding: 10px;
  position: fixed;
  bottom: 0px;
  background-color: #fff;

  .search-box {
    width: 100%;
    height: 60px;
    border-radius: 15px;
    box-shadow: 0px 0px 8px 0px rgba(117, 155, 212, 0.22);
    padding-right: 79px;
    position: relative;
    display: flex;
    align-items: center;

    .icon-right {
      position: absolute;
      right: 15px;
      display: flex;
      align-items: center;

      img {
        height: 24px;
      }

      img + img {
        margin-left: 15px;
      }

      .default-img {
        transition: transform 0.3s ease;
      }

      .rotated {
        transform: rotate(45deg);
      }
    }

    ::v-deep .van-field__control {
      caret-color: #2a64f6;
    }
  }

  .search-box-active {
    padding-right: 40px;
  }

  .search-box-recording-active {
    padding-right: 0px;
  }

  .more-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-top: 20px;

    .more-item {
      width: 100%;
      aspect-ratio: 1 / 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #f5f7fa;
      border-radius: 10px;

      img {
        height: 25px;
      }

      span {
        font-size: 14px;
        margin-top: 5px;
        color: @second_text_color;
      }
    }
  }
}

.history-title {
  position: fixed;
  top: 0;
  font-size: 16px;
  padding: 15px;
  background-color: #fff;
  width: 100%;
  // height: 52px;

  .title {
    font-weight: 600;
  }

  .new-dialogue {
    padding: 10px;
    border: 1px solid #2a64f6;
    color: #2a64f6;
    border-radius: 10px;
    margin-top: 10px;

    img {
      width: 20px;
      height: 20px;
      margin-right: 5px;
    }
  }
}

.history-container {
  width: 70vw;
  height: calc(100% - 163px);
  padding: 15px;
  padding-top: 0;
  font-size: 14px;
  overflow-y: auto;
  margin-top: 103px;

  .history-time {
    .time {
      padding: 10px 0 6px;
    }
  }

  img {
    width: 16px;
    height: 16px;
  }

  .history-message {
    background-color: rgba(245, 247, 250, 0.5);
    border-radius: 10px;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 10px;
    gap: 6px;

    & + .history-message {
      margin-top: 10px;
    }

    .history-text {
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .history-item-active {
    background-color: rgba(124, 159, 236, 0.2);
    color: #2a64f6;
  }

  .history-bottom {
    width: 100%;
    padding: 15px 15px 0;
    color: @second_text_color;
  }
}

.user-bottom {
  height: 60px;
  display: flex;
  align-items: center;
  box-shadow: 0 -4px 6px -1px rgba(117, 155, 212, 0.22);
  padding-left: 10px;

  img {
    width: 24px;
    height: 24px;
    margin-right: 3px;
  }

  span {
    font-size: 14px;
    position: relative;
    top: 2px;
  }
}

.voice-recorder {
  width: 100%;
  height: 100%;
  border-radius: 15px;
  position: relative;
  overflow: hidden;
  background: transparent;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-user-select: none; /* 禁止iOS选中文本 */
  user-select: none; /* 禁止其他浏览器选中文本 */

  .voice-animation {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #2a64f6;
    height: 100%;
    width: 100%;
  
    .voice-dot, .voice-dot-active {
      width: 3px;
      height: 5px;
      background-color: white;
      margin: 0 1px;
      border-radius: 3px;
    }
  
    .voice-dot-active {
      animation: pulse 1s infinite;
    }

    .voice-cancel {
      width: 100%;
      height: 100%;
      background: #ee0a24;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.voice-prompt {
  position: absolute;
  bottom: 80px;
  left: calc(50% - 55px);
  padding: 8px 10px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 400;
  background-color: #4c4c4c;
  color: #fff;
  line-height: 1;
}

@keyframes pulse {
  0% { height: 5px; }
  25% { height: 10px; }
  50% { height: 30px; }
  75% { height: 10px; }
  100% { height: 5px; }
}
</style>