/*******
 * @Description: 益鹭保
 * @Version: 0.1
 * @Autor: T
 */
export default [
  {
    path: "/yilubao",
    name: "益鹭保",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "yilubaoModule" */"@/views/yilubao")
  },
  {
    path: "/yilubao-v2",
    name: "益鹭保",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "yilubaoModule" */"@/views/yilubao/uncheck-index")
  },
  {
    path: "/yilubao/process",
    name: "益鹭保",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "yilubaoModule" */"@/views/yilubao/insure-against/insure-process")
  },
  {
    path: "/yilubao/add",
    name: "益鹭保",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "yilubaoModule" */"@/views/yilubao/insure-against/add-person")
  },
  {
    path: "/yilubao/agreement",
    name: "益鹭保",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "yilubaoModule" */"@/views/yilubao/agreement-content")
  },
  {
    path: "/yilubao/order",
    name: "益鹭保",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "yilubaoModule" */"@/views/yilubao/order")
  },
  {
    path: "/distributor-info",
    name: "分销员信息",
    meta: {
      keepAlive: true
    },
    component: () => import(/* webpackChunkName: "yilubaoModule" */"@/views/yilubao/distributor-info")
  },
  {
    path: "/palmar-distributor",
    name: "益鹭保",
    meta: {
      keepAlive: false
    },
    component: () => import(/* webpackChunkName: "yilubaoModule" */"@/views/yilubao/palmar-distributor")
  }
]