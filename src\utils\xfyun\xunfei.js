import CryptoJS from 'crypto-js'
import RecorderManager from './index.esm.js'

const APPID = "eeaadd62";
const API_KEY = "a8a368e2b1d0a8fabfa75dae872d2b49";
const API_SECRET = "Yzk1OTdkM2JhNzI5MWUwNmZjNjdhZWI5";

let btnStatus = "UNDEFINED"; // "UNDEFINED" "CONNECTING" "OPEN" "CLOSING" "CLOSED"
let recorderInstance = null;
let stream = null;

let iatWS;
let resultText = "";
let resultTextTemp = "";
let countdownInterval;
let user_input_msg = ""
let end = false

const recorder = new RecorderManager("/dist")
recorder.onStart = () => {
  changeBtnStatus("OPEN");
}

/**
 * 获取websocket url
 * 该接口需要后端提供，这里为了方便前端处理
 */
function getWebSocketUrl() {
  // 请求地址根据语种不同变化
  let url = "wss://iat-api.xfyun.cn/v2/iat";
  const host = "iat-api.xfyun.cn";
  const apiKey = API_KEY;
  const apiSecret = API_SECRET;
  const date = new Date().toGMTString();
  const algorithm = "hmac-sha256";
  const headers = "host date request-line";
  const signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/iat HTTP/1.1`;
  const signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
  const signature = CryptoJS.enc.Base64.stringify(signatureSha);
  const authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
  const authorization = btoa(authorizationOrigin);
  url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
  return url;
}
 
function toBase64(buffer) {
  let binary = "";
  let bytes = new Uint8Array(buffer);
  let len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}
 
function countdown() {
  let seconds = 60;
  countdownInterval = setInterval(() => {
    seconds = seconds - 1;
    if (seconds <= 0) {
      clearInterval(countdownInterval);
      recorder.stop();
    }
  }, 1000);
}
 
function changeBtnStatus(status) {
  btnStatus = status;
  if (status === "CONNECTING") {
    resultText = "";
    resultTextTemp = "";
  } else if (status === "OPEN") {
    countdown();
  } else if (status === "CLOSING") {
  } else if (status === "CLOSED") {
  }
}
 
function renderResult(resultData) {
  // 识别结束
  let jsonData = JSON.parse(resultData);
  if (jsonData.data && jsonData.data.result) {
    let data = jsonData.data.result;
    let str = "";
    let ws = data.ws;
    for (let i = 0; i < ws.length; i++) {
      str = str + ws[i].cw[0].w;
    }
    // 开启wpgs会有此字段(前提：在控制台开通动态修正功能)
    // 取值为 "apd"时表示该片结果是追加到前面singleData的最终结果；取值为"rpl" 时表示替换前面的部分结果，替换范围为rg字段
    if (data.pgs) {
      if (data.pgs === "apd") {
        // 将resultTextTemp同步给resultText
        resultText = resultTextTemp;
      }
      // 将结果存储在resultTextTemp中
      resultTextTemp = resultText + str;
    } else {
      resultText = resultText + str;
    }
    user_input_msg = resultTextTemp || resultText || "";
    if (jsonData.data.status == 2) {
      end = true
    }
  }
  if (jsonData.code === 0 && jsonData.data.status === 2) {
    iatWS.close();
  }
  if (jsonData.code !== 0) {
    iatWS.close();
    console.error(jsonData);
  }
}
 
function connectWebSocket(callback) {
  const websocketUrl = getWebSocketUrl();
  if ("WebSocket" in window) {
    iatWS = new WebSocket(websocketUrl);
  } else if ("MozWebSocket" in window) {
    iatWS = new MozWebSocket(websocketUrl);
  } else {
    alert("浏览器不支持WebSocket");
    return;
  }
  changeBtnStatus("CONNECTING");
  iatWS.onopen = (e) => {
    // 开始录音
    // recorder.start({
    //   sampleRate: 16000,
    //   frameSize: 1280,
    // });
    let params = {
      common: {
        app_id: APPID,
      },
      business: {
        language: "zh_cn",
        domain: "iat",
        accent: "mandarin",
        vad_eos: 1000,
        dwa: "wpgs"
      },
      data: {
        status: 0,
        format: "audio/L16;rate=16000",
        encoding: "raw",
      },
    };
    iatWS.send(JSON.stringify(params));
    callback()
  };
  iatWS.onmessage = (e) => {
    renderResult(e.data);
  };
  iatWS.onerror = (e) => {
    console.error("错误", e);
    recorder.stop();
    changeBtnStatus("CLOSED");
  };
  iatWS.onclose = (e) => {
    recorder.stop();
    changeBtnStatus("CLOSED");
  };
}

recorder.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
  if (iatWS.readyState === iatWS.OPEN) {
    iatWS.send(
      JSON.stringify({
        data: {
          status: isLastFrame ? 2 : 1,
          format: "audio/L16;rate=16000",
          encoding: "raw",
          audio: toBase64(frameBuffer),
        },
      })
    );
    if (isLastFrame) {
      changeBtnStatus("CLOSING");
    }
  }
};

recorder.onStop = () => {
  clearInterval(countdownInterval);
};
 
const start = () => {
  if (btnStatus === "UNDEFINED" || btnStatus === "CLOSED") {
    user_input_msg = "";
    stream = null
    end = false
    startRecording()
  } else if (btnStatus === "CONNECTING" || btnStatus === "OPEN") {
    recorder.stop();
  }
};

async function startRecording() {
  // 获取麦克风权限
  stream = await navigator.mediaDevices.getUserMedia({ audio: true })

  // 创建 AudioContext，指定采样率为 16000Hz（如果支持）
  const context = new (window.AudioContext || window.webkitAudioContext)({
    sampleRate: 16000
  });

  // 创建媒体流源节点
  const source = context.createMediaStreamSource(stream);

  // 创建 ScriptProcessorNode 用于获取原始音频数据
  const bufferSize = 4096;
  const channelCount = 1; // 单声道
  const recorder2 = context.createScriptProcessor(bufferSize, channelCount, channelCount);

  // 连接音频流
  source.connect(recorder2);
  recorder2.connect(context.destination);

  // 存储录制的数据
  const recordedData = [];

  recorder2.onaudioprocess = function(e) {
    const leftChannel = e.inputBuffer.getChannelData(0); // 获取单声道数据
    recordedData.push(leftChannel.slice());
  };

  // 返回一个停止函数
  recorderInstance = {
    stop: () => {
      // 停止录音
      recorder2.disconnect();
      source.disconnect();

      // 合并所有 buffer 数据
      const totalLength = recordedData.reduce((sum, arr) => sum + arr.length, 0);
      const result = new Float32Array(totalLength);
      let offset = 0;

      for (const arr of recordedData) {
        result.set(arr, offset);
        offset += arr.length;
      }

      // 转换为 16bit PCM
      const wavBuffer = floatTo16BitPCM(result);

      // 关闭麦克风轨道
      releaseMicrophone()

      return wavBuffer;
    }
  };
}

function floatTo16BitPCM(float32Array) {
  const buffer = new Int16Array(float32Array.length);
  for (let i = 0; i < buffer.length; i++) {
    let s = Math.max(-1, Math.min(1, float32Array[i]));
    buffer[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
  }
  return buffer;
}

async function setMediaRecorder() {
  if (btnStatus !== "CONNECTING") {
    const pcmData = await recorderInstance.stop(); // 获取 Int16Array 数据
    const audioString = toBase64(pcmData.buffer); // 直接使用 toBase64 函数
    connectWebSocket(() => {
      let offset = 0;
      while(offset < audioString.length) {
        const subString = audioString.substring(offset, offset + 1280)
        offset += 1280
        const isEnd = offset >= audioString.length
        iatWS.send(
          JSON.stringify({
            data: {
              status: isEnd ? 2 : 1,
              format: "audio/L16;rate=16000",
              encoding: "raw",
              audio: subString
            },
          })
        );
      }
    })
  }
}

function releaseMicrophone() {
  if (stream && stream.getTracks) {
    stream.getTracks().forEach(track => {
      track?.stop();
    });
  }
}

export default {
  start,
  setMediaRecorder,
  getUserInputMsg: () => { return user_input_msg },
  getIsEnd: () => { return end },
  releaseMicrophone
}