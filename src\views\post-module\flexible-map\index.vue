<!--
 * @Description: 环卫爱心驿站
 * @Author: hwx
 * @date: 2024/9/4 11:29
 * @LastEditors: Please set LastEditors
-->
<template>
  <div class="love-station">
    <!-- 搜索 -->
    <div class="search-box flex-c-c">
      <img class="search-icon" src="@pic/life-service/<EMAIL>" alt="" />
      <van-field
        v-model="input"
        input-align="left"
        placeholder="请输入名称"
        :border="false"
        clearable
      />
      <span class="search-btn" @click="handleSearch">搜索</span>
    </div>
    <!-- <div class="float-components">
      <div class="location" @click="handleLocate(true)">
        <img src="@pic/post-module/<EMAIL>" />
      </div>
      <div class="location" @click="showPopup = true">
        <img src="@pic/post-module/<EMAIL>" />
      </div>
    </div> -->
    <!-- 地图 -->
    <div :class="['map-container', showPopup ? 'map-container-short' : 'map-container-long']" id="map"></div>

    <!-- 弹窗 -->
    <van-popup
      :class="['info-popup', showPopup ? '' : 'info-popup-hide']"
      v-model="showPopup"
      position="bottom"
      :round="true"
      :overlay="true"
      :lock-scroll="false"
      :close-on-click-overlay="false"
    >
    <van-list
        ref="vanList"
        v-model="loading"
        :finished="finished"
        :finished-text="list?.length > 0 ? '没有更多了' : ''"
        :immediate-check="false"
        @load="onLoad"
      >
      <div v-if="list?.length > 0">
        <div
          class="tabs-item"
          v-for="(item, key) in list"
          :key="key"
        >
        <van-row>
          <van-col class="item-title text-ellipsis-2 ellipsis-2" span="12" @click="handleClickPost(item)">{{ item.gwmc00 }}</van-col>
          <van-col class="item-price" span="12">{{ item.salary }}</van-col>
        </van-row>
        <div class="label-list">
          <span class="label" v-if="item.countyName">{{item.countyName}}</span>
          <span class="label" v-if="item.workExperienceName">{{item.workExperienceName}}</span>
          <span class="label" v-if="item.educationName">{{item.educationName}}</span>
          <span class="label" v-if="item.natureName">{{item.natureName}}</span>
        </div>
        <van-row>
          <van-col class="inviter" span="24">            
            <img class="user-logo" src="@/assets/imgs/post-module/<EMAIL>" alt="">
            <span class="inviter-name">招聘者：{{ item.contacts }}</span>            
          </van-col>
        </van-row>
        <van-row class="item-bottom">
          <van-col span="16 text-ellipsis-2 ellipsis-2">
            {{ item.aab004 }}
          </van-col>
          <van-col class="text-align-right text-ellipsis-2 ellipsis-2" span="8">
            {{ item.countyName }}
          </van-col>
        </van-row>
        </div>
      </div>

      <y-empty v-else></y-empty>
        
      </van-list>
    </van-popup>
  </div>
</template>

<script>
/* eslint-disable no-undef */

import { commonApi } from "@/api"
import {cloneDeep} from "lodash"
import {setWxConfig, getLocation} from "@/utils/wechat"

export default {
  name: "love-station",
  data() {
    return {
      // 地图相关
      map: null, //地图实例
      markersArray: null, //地图标记点数组
      infoWindow: null, // 信息窗口
      mapKey: process.env.VUE_APP_TMAP_KEY, //map key
      locationMaker: null,
      //信息弹窗
      showPopup: true, 

      // 搜索
      input: "",
      pageInfo: {
        page: 1,
        size: 10
      },
      searchParams: {
        page: 1,
        size: 10
      },
      list: [],
      loading: false,
      finished: false,
      stageListTotal: 0,
      stageList: [], //驿站列表
      CAE026List: [], //所属区字典
      FWNR00List: [], //服务内容字典
      showLocation: false,
      activeLocation: "02",

      // 服务内容
      serviceList: [
        { 
          label: "休息",
          value: "01",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        },
        { 
          label: "饮水",
          value: "02",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        },        
        { 
          label: "充电",
          value: "04",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        },
        { 
          label: "急救药箱",
          value: "05",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        }
      ]
    }
  },
  computed: {
    activeLocationName() {
      const data = this.CAE026List?.find(i => i.value === this.activeLocation) || {}      
      return data.label
    },
    stageData() {
      return this.stageList?.[0] || {}
    }
  },
  async created() {
    setWxConfig() // 微信SDK签名配置
    this.findRcygByPage()
    // await this.getPlatformList() //查询字典
    // this.findRcygByPage(location) //分页查询-环卫爱心驿站信息表    
  },
  methods: {
    handleLocate() {
      getLocation(setCenter)
      const _this = this
      function setCenter(location) {
        const {latitude, longitude} = location || {}
        if (latitude && longitude) {
          _this.clearLocationMaker()
        }
        const center = new qq.maps.LatLng(latitude, longitude)
        _this.map.setCenter(center)
        _this.setLocation(latitude, longitude)
      }
    },
    onLoad() {
      this.searchParams.page++
      this.findRcygByPage()
    },
    // 搜索
    handleSearch() {
      this.showPopup = false
      this.clearOverlays()
      this.findRcygByPage()
    },
    //查询字典
    async getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["CAE026", "FWNR00"]
      }
      const res = await commonApi.proxyApi(params)
      const { data } = res.map
      const dictInfo = {
        CAE026: "CAE026List",
        FWNR00: "FWNR00List"
      }
      for (const key in dictInfo) {
        this[dictInfo[key]] = data[key].map((item) => {
          return { label: item.aaa103, value: item.aaa102 }
        })
      }
    },
    //查询列表信息
    findRcygByPage() {
      const serviceName = "xytQzzp_findLhygByPage"
      const params = {
        serviceName,
        gwmc00: this.input,
        gwdwmc: this.input,
        ...this.searchParams
      }
      this.loading = true
      commonApi.proxyApi(params).then((res) => {
        const {rows=[], total=0} = res.map.data
        this.list = [...this.list, ...rows]

        this.loading = false

        if (this.list.length >= total) {
          this.finished = true
        }
        const lat = "24.485783", lng = "118.123054"
        this.reserMap(lat, lng)
      }).catch(() => {
        this.loading = false
        this.finished = true
      })
    },
    //地图显示
    reserMap(lat, lng, zoom=13) {
      const center = new qq.maps.LatLng(lat, lng)
      const mapContainer = document.getElementById("map")
      this.map = new qq.maps.Map(mapContainer, {
        center: center, 
        zoom: zoom, 
        draggable: true,
        mapSign: false // 隐藏右下角图标
      })
      
      const markersArray = [] //标记点列表
      const iconUrl = `${process.env.VUE_APP_PUBLIC_PATH}map-marker/map-marker.svg`      
      const markerIcon = new qq.maps.MarkerImage( //标记点图标及样式
        iconUrl, // 图片的URL
        new qq.maps.Size(100, 100), // 图标的大小
        new qq.maps.Point(0, 0), // 图标的坐标
        new qq.maps.Point(15, 30) // 图标的锚点
      )
      
      this.list.forEach(item => {        
        const {latitude, longitude, daz012} = item
        const lat = latitude && Number(latitude)
        const lng = longitude && Number(longitude)        
        const marker = new qq.maps.Marker({
          id: daz012,
          daz012,
          position: new qq.maps.LatLng(lat, lng),
          map: this.map,
          icon: markerIcon
        })       
        markersArray.push(marker)
      })
      getLocation(this.setLocation)
      this.markersArray = markersArray //标记点列表
    },
    //清除地图标记点
    clearOverlays() {
      const {markersArray} = this
      if (markersArray?.length > 0) {
        for (const i in markersArray) {          
          markersArray[i].setMap(null)
        }
      }
    },
    setLocation(position) {
      // const lat = "24.485783", lng = "118.123054"
      const {latitude= "24.485783", longitude= "118.123054"} = position || {}
      const center = new qq.maps.LatLng(latitude, longitude)
      if (longitude && latitude) {
        this.map.setCenter(center)
      }
      /* const lat = latitude || "24.485783", lng = longitude || "118.123054"
      // 定位图标
      const iconUrl = `${process.env.VUE_APP_PUBLIC_PATH}location.png`  
      const locationIcon = new qq.maps.MarkerImage( //标记点图标及样式
        iconUrl, // 图片的URL
        new qq.maps.Size(200, 200), // 图标的大小
        new qq.maps.Point(0, 0), // 图标的坐标
        // new qq.maps.Point(15, 30) // 图标的锚点
      )
      this.locationMaker = new qq.maps.Marker({
        id: "location",
        position: new qq.maps.LatLng(lat, lng),
        map: this.map,
        icon: locationIcon
      }) */
    },
    // 清除定位图标
    clearLocationMaker() {
      this.locationMaker.setMap(null)
    },
    // 格式化服务内容数据
    formatServiceList(data) {
      const list = data.fwnr00.split(";")
      const serviceList = cloneDeep(this.serviceList)        
      data.serviceList = serviceList.map((item) => {
        return {...item, imgUrl: require(`@/assets/imgs/life-service/service${item.value}${list.includes(item.value) ? "-active" : ""}@2x.png`)}
      })
      return data
    },
    // 关闭弹窗
    closePopup() {
      this.infoWindow && this.infoWindow.setMap(null)
      this.showPopup = false
    }
  
  }
}
</script>

<style lang="less" scoped>
.love-station {
  background: #4c4c4c;
  position: relative;
  .search-box {
    position: absolute;
    width: 305px;
    height: 36px;
    background: #ffffff;
    border-radius: 18px;
    top: 16px;
    left: 50%;
    transform: translate(-50%);
    z-index: 99999;
    padding: 0 16px;
    .search-icon {
      width: 16px;
      height: 14px;
    }
    .van-field {
      flex: 1;
      padding: 0 10px;
      /deep/.van-icon-clear {
        font-size: 14px;
      }
      /deep/.van-field__control::-webkit-input-placeholder {
        font-size: 14px;
      }
    }
    .search-btn {
      font-size: 14px;
      color: #bd1a2d;
      display: inline-block;
      height: 100%;
      line-height: 36px;
    }
  }
  .map-container {
    width: 100%;
    &-short {
      height: calc(100vh - 324px);
    }
    &-long {
      height: calc(100vh);
    }
  }
  .float-components {
    position: absolute;
    bottom: 20px;
    right: 16px;
    z-index: 999;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    img {
      width: 32px;
      height: 32px;
    }
  }
  ::v-deep .map-container {
    & > div > div:nth-child(2) {
      display: none;
    }
    .smnoprint {
      display: none;
    }
  }
  .info-popup {    
    box-sizing: border-box;
    padding-bottom: 20px;
    height: 50vh;
    background-color: #F6F6F6;
    &-hide {
      display: none;
    }
    .tabs-item {
      padding: 20px 16px;
      background-color: #fff;
      margin-bottom: 16px;
      .item-title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #222327;
        text-align: left;
        font-style: normal;
      }
      .item-price {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #FF3B37;
        line-height: 22px;
        text-align: right;
        font-style: normal;
      }
      .label-list {
        padding-bottom: 18px;
        border-bottom: 1px solid #E5E5E5;
        margin-top: 16px;
        .label {
          padding: 5px;
          background: #F6F6F6;
          border-radius: 2px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          margin-right: 4px;
        }
      }
      .inviter {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        display: flex;
        align-items: center;
        margin-top: 11px;
        .user-logo {
          width: 12px;
          height: 15px;
        }
        .inviter-name {
          margin-left: 10px;
        }
        .icon {
          margin-right: 9px;
          width: 12px;
          height: 15px;
          border: 1px solid;
        }
      }
      .item-bottom {
        margin-top: 6px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        .text-align-right {
          text-align: right         ;
        }
      }
    }
  }
  /deep/.van-overlay {
    background: rgba(0, 0, 0, 0);
    z-index: -1 !important;
  }
}
</style>
