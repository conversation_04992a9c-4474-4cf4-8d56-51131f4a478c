//模块导入
import AvatarPlatform, {
  PlayerEvents,
  SDKEvents
} from "@/vm-sdk/avatar-sdk-web_3.1.1.1011/index.js"
import { encryptApi } from "@/api"
import { getEncryptEnabled, getWelcomeText, setWelcomeText, getMessages } from "@/utils/cookie"
import MarkdownIt from "markdown-it"
const md = new MarkdownIt({
  html: true,
  linkify: true,
  xhtmlOut: true,
  typographer: true,
  breaks: true
})

let avatarPlatform2 = null
// eslint-disable-next-line no-unused-vars
let recorder = null
export default {
  name: "avatarComponent",
  data() {
    return {
      SetApiInfodialog: false,
      SetGlobalParamsdialog: false,
      form: {},

      globalParams: {},
      // 旧版参数，留作参考
      setglobalparamsform: {
        stream: {
          protocol: "xrtc", //（必传）实时视频协议，支持webrtc/xrtc/rtmp，其中只有xrtc支持透明背景，需参数alpha传1
          fps: 25, //（非必传）视频刷新率,值越大，越流畅，取值范围0-25，默认25即可
          bitrate: 1000000, //（非必传）视频码率，值越大，越清晰，对网络要求越高，默认1000000即可
          alpha: true //（非必传）是否开启透明背景，0关闭1开始，需配合protocol=xrtc使用
        },
        avatar: {
          avatar_id: "110005011", //（必传）授权的形象资源id，请到交互平台-接口服务-形象列表中获取
          width: 1080, //（非必传）视频分辨率宽（不是画布的宽，调整画布大小需调整名为wrapper的div宽）
          height: 1920, //（非必传）视频分辨率高（不是画布的高，调整画布大小需调整名为wrapper的div高）
          mask_region: "[0,0,1080,1920]", //（非必传）形象裁剪参数，[从左到右，从上到下，从右到左，从下到上]
          scale: 0.7, //（非必传）形象缩放比例，取值范围0.1-1
          move_h: -20, //（非必传）形象左右移动
          move_v: 250, //（非必传）形象上下移动
          audio_format: 1//（非必传）音频采样率，传1即可
        },
        tts: {
          vcn: "x4_lingxiaoying_assist", //（必传）授权的声音资源id，请到交互平台-接口服务-声音列表中获取
          speed: 60, //（非必传）语速
          pitch: 50, //（非必传）语调
          volume: 100, //（非必传）音量
          emotion: 13//（非必传）情感系数，仅带有情感能力的超拟人音色支持该能力，普通音色不支持
        },
        avatar_dispatch: {
          interactive_mode: 1//（非必传）0追加模式，1打断模式
        },
        subtitle: {
          subtitle: 0, //（非必传）开启字幕，2D形象支持字幕，透明背景不支持字幕，3D形象不支持字幕（3D形象多为卡通形象，2D多为真人形象）
          font_color: "#FFFFFF", //（非必传）字体颜色
          font_name: "Sanji.Suxian.Simple", //（非必传）不支持自定义字体，若不想使用默认提供的
          //字体，那么可以设置asr和nlp监听事件，去获取语音识别和语义理解的文本，自己前端贴字体。
          //支持一下字体：'Sanji.Suxian.Simple','Honglei.Runninghand.Sim','Hunyuan.Gothic.Bold',
          //'Huayuan.Gothic.Regular','mainTitle'
          position_x: 500, //（非必传）设置字幕水平位置，必须配置width、height一起使用，否则字幕不显示
          position_y: 1200, //（非必传）设置字幕竖向位置，必须配置width、height一起使用，否则字幕不显示
          font_size: 5, //（非必传）设置字幕字体大小，取值范围：1-10
          width: 100, //（非必传）设置字幕宽
          height: 100//（非必传）设置字幕高
        },
        enable: true, //demo中用来控制是否开启背景的参数，与虚拟人参数无关
        background: {
          type: "res_key", //（非必传）上传图片的类型，支持url以及res_key。（res_key请到交互平台-素材管理-背景中上传获取)
          data: "22SLM2teIw+aqR6Xsm2JbH6Ng310kDam2NiCY/RQ9n6s3nYJXloZWW1l64/g32vrn7d2lJQR7m9xD5EHYkVs14yBnvom5y9AoG/2iC1c/5Yuc/uDn8HsLAigE+rmEOEf2AQ8ET93anPEAiCeogF0aN02yYT55gCjRpKQZojzSXbzJYvzC+QxF6Lbfl1D00B3u5Ob8W/yBsQ0hn0IfBFEpJ6MFPHglM1VzhvCueW0C7yBg5trbgfhQ5tHQRACIngq/A+DaByP/AR2l0TmoNmV8H8L6ikjIxYnwQmXn9X3lhcixkmTpGuF8nR/AxYzlsgkAzA5uePn8CPZdhsJXbP9U2D5tqvbe1jTxRX9/SVmmQU="
          //（非必传）图片的值，当type='url'时,data='http://xxx/xxx.png'，当type='res_key'时，data='res_key值'（res_key请到交互平台-素材管理-背景中上传获取)
        },
        air: {
          air: 1,
          add_nonsemantic: 1
        }
      },
      formLabelWidth: "120px",
      vc: "",
      nlp: true,
      emotion: 0,
      action: "A_RH_hello_O",
      volume: 100,

      // 存储播放器实体以及绑定的方法
      player: null,

      // 对话列表
      messages: [],
      tempAssistantIndex: -1,
      typingState: {
        charQueue: [],
        isTyping: false,
        isEnd: false,
        expired: false,
        currentContent: ""
      },
      sdkConnected: false,
      longVoice: false,
      isPlaying: false,
      typingInterval: null,
      hasUserInteracted: false,
      playingCount: 0,
      forcePaused: false,
      reconnectCount: 0
    }
  },
  async created() {
    encryptApi.aiBotDetail({}, { encryptEnabled: getEncryptEnabled() }).then(res => {
      if (res?.data?.errorCode == 0 && res.data.data?.h5Human) {
        const globalParams = JSON.parse(res.data.data.h5Human)
        const { appId, apiKey, apiSecret, serverUrl, sceneId } = globalParams
        this.form = { appId, apiKey, apiSecret, serverUrl, sceneId }
        this.globalParams = globalParams.globalParams
        if (getMessages(this.type) && JSON.parse(getMessages(this.type)).length) {
          this.messages = JSON.parse(getMessages(this.type))
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        } else {
          if (globalParams.globalParams.welcome.welcome_text) {
            setWelcomeText(globalParams.globalParams.welcome.welcome_text)
            this.messages.push({ role: "assistant", content: getWelcomeText() })
          }
        }
        this.init()
      } else {
        this.$toast.fail("获取虚拟人失败，请刷新页面")
      }
    })
  },
  methods: {
    init() {
      // 1. 实例化SDK
      this.initSDK()

      // 2. 创建录音器
      this.createRecoder()

      // 3. 设置SDK监听事件
      this.setSDKEvenet()

      // 4. 设置播放器监听事件
      this.setPlayerEvenet()

      // 5. 设置API信息
      avatarPlatform2.setApiInfo(this.form)

      // 6. 设置全局参数
      avatarPlatform2.setGlobalParams(this.globalParams)

      // 7. 开始
      this.start()
    },
    initSDK() {
      //必须先实例化SDK，再去调用其挂载的方法
      avatarPlatform2 = new AvatarPlatform()
      if (avatarPlatform2 != null) {
        this.open2("实例化SDK成功")
      }
    },
    createRecoder() {
      if (avatarPlatform2 != null) {
        recorder = avatarPlatform2.createRecorder()
        this.open2("创建录音器成功")
      } else {
        this.$toast.fail("获取虚拟人失败，请刷新页面")
      }
    },
    setSDKEvenet() {
      // 绑定SDK事件
      if (avatarPlatform2 != null) {
        const that = this
        avatarPlatform2
          .on(SDKEvents.connected, function(initResp) {
            that.sdkConnected = true
            initResp.userid = "1234567890",
            console.log("SDKEvent.connect:initResp:", initResp)
          })
          .on(SDKEvents.stream_start, function() {
            if (that.messages[0]?.content && that.messages.length === 1) {
              avatarPlatform2.writeText(that.messages[0].content, {
                nlp: false
              })
            }
            console.log("stream_start")
          })
          .on(SDKEvents.disconnected, function(err) {
            that.sdkConnected = false
            that.setAllStatus()
            console.log("SDKEvent.disconnected:", err)
            if (err) {
              // 因为异常 而导致的断开！ 此处可以进行 提示通知等
              console.error("ws link disconnected because of Error")
              console.error(err.code, err.message, err.name, err.stack)
              that.$toast.fail("连接已断开，正在尝试重连……")
              // 自动重连逻辑
              if (that.reconnectCount < 3) {
                that.reconnectCount++
                setTimeout(() => {
                  that.init()
                }, 1000)
              } else {
                that.$toast.fail("多次重连失败，请刷新页面")
              }
            }
            that.playingCount = 0
            that.isPlaying = false
          })
          .on(SDKEvents.nlp, (nlpData) => {
            console.log("nlpData", nlpData)
            const content = nlpData.answer.text
            const status = nlpData.status
            if (status == 2) {
              that.typingState.isEnd = true
              that.typingState.expired = true // 文本结束
            }
            if (content) {
              // 设置状态，显示停止按钮
              that.lastStatus = true
              // 将新内容拆分为字符队列
              that.typingState.charQueue = that.typingState.charQueue.concat(content.split(""))
              // 启动打字机逻辑
              if (!that.typingState.isTyping) {
                that.startTyping(that.typingState, that.tempAssistantIndex)
              }
            }
          })
          .on(SDKEvents.frame_start, function(frame_start) {
            if (that.forcePaused) {
              that.isPlaying = false
              that.playingCount = 0
              return
            }
            that.playingCount++
            that.isPlaying = true
            console.log("推流开始 frame_start, playingCount:", that.playingCount)
          })
          .on(SDKEvents.frame_stop, function(frame_stop) {
            if (that.forcePaused) {
              that.isPlaying = false
              that.playingCount = 0
              return
            }
            that.playingCount--
            if (that.playingCount <= 0) {
              that.isPlaying = false
              that.playingCount = 0
            }
            console.log("推流结束 frame_stop, playingCount:", that.playingCount)
          })
          .on(SDKEvents.error, function(error) {
            that.sdkConnected = false
            console.log("错误信息error:", error)
            that.setAllStatus()
            that.playingCount = 0
            that.isPlaying = false
          })
          .on(SDKEvents.connected, function() {
            console.log("connected")
          })
          .on(SDKEvents.asr, (asrData) => {
            console.log("语音识别数据asr:", asrData)
            if (that.messages[that.messages.length - 1] && that.messages[that.messages.length - 1].role === "assistant") {
              that.messages[that.messages.length - 1].stop = true
            }
            that.messages.push({
              role: "user",
              content: asrData.text
            })
            that.tempAssistantIndex = that.messages.length
            // 创建本轮请求专用的状态对象
            that.initTypingState()
            that.messages.push({
              role: "assistant",
              content: ""
            })
            that.lastStatus = true
            this.setGuessQuestions()
            that.scrollToBottom()
          })
          .on(SDKEvents.tts_duration, function(ttsData) {
            console.log("语音合成用时tts：", ttsData)
          })
          .on(SDKEvents.subtitle_info, function(subtitleData) {
            console.log("subtitleData：", subtitleData)
          })
          .on(SDKEvents.action_start, function(action_start) {
            if (that.forcePaused) {
              that.isPlaying = false
              that.playingCount = 0
              return
            }
            that.playingCount++
            that.isPlaying = true
            console.log("动作推流开始 action_start, playingCount:", that.playingCount)
          })
          .on(SDKEvents.action_stop, function(action_stop) {
            if (that.forcePaused) {
              that.isPlaying = false
              that.playingCount = 0
              return
            }
            that.playingCount--
            if (that.playingCount <= 0) {
              that.isPlaying = false
              that.playingCount = 0
            }
            console.log("动作推流结束 action_stop, playingCount:", that.playingCount)
          })
        this.open2("监听SDK事件成功")
      } else {
        this.$toast.fail("获取虚拟人失败，请刷新页面")
      }
    },
    setPlayerEvenet() {
      if (avatarPlatform2 != null) {
        // 绑定播放器事件
        const player = avatarPlatform2.createPlayer()
        player
          .on(PlayerEvents.play, function() {
            if (this.hasUserInteracted) {
              this.isPlaying = true
            }
          }.bind(this))
          .on(PlayerEvents.frame_start, function() {
            this.playingCount++
            this.isPlaying = true
          }.bind(this))
          .on(PlayerEvents.frame_stop, function() {
            this.playingCount--
            if (this.playingCount <= 0) {
              this.isPlaying = false
              this.playingCount = 0
            }
          }.bind(this))
          .on(PlayerEvents.action_start, function() {
            this.playingCount++
            this.isPlaying = true
          }.bind(this))
          .on(PlayerEvents.action_stop, function() {
            this.playingCount--
            if (this.playingCount <= 0) {
              this.isPlaying = false
              this.playingCount = 0
            }
          }.bind(this))
          .on(PlayerEvents.playing, function() {
            console.log("playing")
          })
          .on(PlayerEvents.waiting, function() {
            console.log("waiting")
          })
          .on(PlayerEvents.stop, function() {
            console.log("stop")
            // 保险起见，stop时也归零
            this.playingCount = 0
            this.isPlaying = false
            this.isStreaming = false
            this.lastStatus = false
          }.bind(this))
          .on(PlayerEvents.playNotAllowed, () => {
            this.isPlay = true
            console.log(
              "playNotAllowed：触发了游览器限制自动播放策略，播放前必须与游览器产生交互（例如点击页面或者dom组件），触发该事件后调用avatarPlatform2.player.resume()方法来接触限制"
            )
            player.resume()
          })
        this.player = player
        console.log("player播放器", this.player)
        this.open2("监听播放器事件成功")
      } else {
        this.$toast.fail("获取虚拟人失败，请刷新页面")
      }
    },
    start() {
      if (avatarPlatform2 != null) {
        avatarPlatform2
          .start({ wrapper: document.querySelector("#wrapper") })
          .then(() => {
            console.log("connected &&  stream play successfully")
            // 暂时做为初始化事件的起点

            // 注意这里仅是流可以播放， 如果是进页面在用户未交互网页时，代码自动连，
            // 第三步骤 player实例 可能收到PlayerEvents.playNotAllowed事件。
            // 您需要交互层面再次用户点击网页 并主动调用 player.resume() 恢复播放！！
            // 原因：受限于浏览器的自动播放策略
          })
          .catch((e) => {
            console.error(e.code, e.message, e.name, e.stack)
          })
      } else {
        this.$toast.fail("获取虚拟人失败，请刷新页面")
      }

    },
    writeText(text) {
      if (avatarPlatform2 != null && text !== "") {
        this.hasUserInteracted = true
        avatarPlatform2.writeText(text, {
          nlp: this.nlp // 是否开启语义理解
        })
      }
    },
    writeCmd() {
      avatarPlatform2.writeCmd("action", this.action)
    },
    interrupt() {
      if (avatarPlatform2 != null) {
        avatarPlatform2.interrupt()
      } else {
        this.$toast.fail("获取虚拟人失败，请刷新页面")
      }
    },
    startRecord() {
      if (avatarPlatform2 != null) {
        avatarPlatform2.recorder.startRecord(0, () => {
          console.log("成功录音")
          console.warn("STOPED RECORDER")
        }, {
          nlp: true,
          avatar_dispatch: {
            interactive_mode: 0 // 交互模式（追加或打断）
          }
        })
      } else {
        this.$toast.fail("获取虚拟人失败，请刷新页面")
      }
    },
    stopRecord() {
      if (avatarPlatform2 != null) {
        avatarPlatform2.recorder.stopRecord()
      } else {
        this.$toast.fail("获取虚拟人失败，请刷新页面")
      }
    },
    stop() {
      if (avatarPlatform2 != null) {
        avatarPlatform2.stop()
      } else {
        this.$toast.fail("获取虚拟人失败，请刷新页面")
      }
    },
    destroy() {
      if (avatarPlatform2 != null) {
        // 销毁SDK示例，内部包含stop协议，重启需重新示例化avatarPlatform实例
        avatarPlatform2.destroy()
        avatarPlatform2 = null
      } else {
        console.error("请先实例化SDK")
      }
    },
    open2(text) {
      // this.$dialog.alert({
      //   message: text,
      //   theme: "round-button"
      // })
    },
    sentMessage(content) {
      if (!content || !avatarPlatform2) {
        return
      }
      if (!this.sdkConnected) {
        this.setAllStatus()
        this.$set(this.messages, this.messages.length - 1, {
          role: "assistant",
          content: "服务未连接，请稍后重试"
        })
        return
      }
      // 更新临时助手索引
      this.tempAssistantIndex = this.messages.length - 1
      // 创建本轮请求专用的状态对象
      this.initTypingState()
      // 滚动到底部
      this.scrollToBottom()
      // 使用虚拟人SDK发送消息
      this.hasUserInteracted = true
      avatarPlatform2.writeText(content, {
        nlp: this.nlp // 是否开启语义理解
      })
    },
    stopAnswer() {
      // 重置状态
      this.setAllStatus()

      if (!avatarPlatform2) {
        return
      }
      // 停止虚拟人SDK的回答
      avatarPlatform2.interrupt()

      // 停止打字机效果
      if (this.typingState && this.typingState.isTyping) {
        this.typingState.isTyping = false
        this.typingState.isEnd = true
        this.typingState.manuallyInterrupt = true // 手动打断
        this.typingState.charQueue = []
      }
      // 如果当前有空的助手消息，标记为已停止
      if (this.messages[this.tempAssistantIndex] && this.messages[this.tempAssistantIndex].content === "") {
        this.$set(this.messages, this.tempAssistantIndex, {
          role: "assistant",
          content: "",
          stop: true
        })
      }
      // 主动复原暂停按钮
      this.playingCount = 0
      this.isPlaying = false
    },
    startTyping(typingState, index) {
      typingState.isTyping = true

      // 清理之前的定时器
      if (this.typingInterval) {
        clearInterval(this.typingInterval)
      }
      this.typingInterval = setInterval(() => {
        const container = this.$refs.messageBox && this.$refs.messageBox.$refs.dialogueContainer
        if (!container) {
          clearInterval(this.typingInterval)
          this.typingInterval = null
          return
        }
        const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 10

        if (typingState.charQueue.length === 0) {
          clearInterval(this.typingInterval)
          this.typingInterval = null
          typingState.isTyping = false
          if (typingState.isEnd) {
            this.setAllStatus()
            if (!typingState.manuallyInterrupt && typingState.expired) {
              this.$refs.searchBox.guessQuestion(this.messages[index].content)
              this.$set(this.messages, index, {
                ...this.messages[index],
                expired: true
              })
            }
          }
          return
        }

        const char = typingState.charQueue.shift()
        typingState.currentContent += char

        const regex = /\[\[(.*?)]]/g
        const replacedText = typingState.currentContent.replace(regex, (_, p1) => {
          return `<a style="color: #0194FF;" href="${p1}">一键去办理</a>`
        })

        this.$set(this.messages, index, {
          ...this.messages[index],
          content: md.render(replacedText)
        })
        if (isAtBottom) {
          this.scrollToBottom()
        }
      }, 50)
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messageBox && this.$refs.messageBox.$refs.dialogueContainer
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: "smooth"
          })
        }
      })
    },
    handleVolume(val) {
      if (this.isPlay) {
        this.player.muted = true
      } else {
        this.player.resume()
      }
      this.isPlay = val
    },
    setAllStatus() {
      this.lastStatus = false
      this.isStreaming = false
      if (this.$refs.searchBox) {
        this.$refs.searchBox.lastStatus = false
        this.$refs.searchBox.isStreaming = false
      }
    },
    sendGuessQuestion(question) {
      this.$refs.searchBox.searchValue = question
      this.$refs.searchBox.stopCommunication()
      this.$refs.searchBox.getDialogueList()
    },
    initTypingState() {
      this.typingState = {
        charQueue: [],
        isTyping: false,
        isEnd: false,
        expired: false,
        currentContent: ""
      }
    }
  },

  beforeDestroy() {
    // 关闭页面时调用stop协议，确保链接断开，释放资源
    if (avatarPlatform2) {
      avatarPlatform2.stop()
    }
    // 清理定时器，防止内存泄漏和报错
    if (this.typingInterval) {
      clearInterval(this.typingInterval)
      this.typingInterval = null
    }
  }
}
