<!--
 * @Description: 人民调解-结案查看
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-protect-case">
    <van-form class="base-form" :disabled="true">
      <y-title content="结案情况" />
      <van-cell-group inset>

        <van-field v-model="formData.aac003" name="aac003" label="申请人" placeholder="请输入" />

        <van-field v-model="formData.aab004" name="aab004" label="被申请人" placeholder="请输入" />

        <van-field v-model="formData.abb013" name="abb013" label="案件编号" placeholder="请输入" />

        <van-field v-model="formData.abb277" name="abb277" label="登记日期" placeholder="请输入" />

        <van-field v-model="formData.aae037" name="aae037" label="结案日期" placeholder="请输入" />

        <van-field v-model="formData.abb286" type="textarea" input-align="left"
          :autosize="{ maxHeight: 70, minHeight: 50 }" maxlength="100" name="abb286" label="申请人陈诉" placeholder="请输入" />

        <van-field v-model="formData.abb959" type="textarea" input-align="left"
          :autosize="{ maxHeight: 70, minHeight: 50 }" maxlength="100" name="abb959" label="案件结果" placeholder="请输入" />

      </van-cell-group>

      <div class="button-box case-button mt18">
        <van-button plain type="info" @click="$router.go(-1)" native-type="button">
          关 闭
        </van-button>
      </div>
    </van-form>

    <!-- 满意度气泡 -->
    <van-popup v-model="showSatisfyDialog" closeable round>
      <div class="satisfy-dialog">
        <div class="dialog-title">
          <van-icon name="smile-o" class="title-icon" />
          <span>满意度调查</span>
        </div>
        <div class="dialog-content">您对本次调解是否满意？</div>
        <div class="dialog-buttons">
          <van-button type="primary" size="large" round @click="handleSatisfy(true)" class="satisfy-btn">
            <van-icon name="like-o" />
            满意
          </van-button>
          <van-button type="danger" size="large" round @click="handleSatisfy(false)" class="unsatisfy-btn">
            <van-icon name="close" />
            不满意
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 否后分支选择 -->
    <van-popup v-model="showTransferDialog" closeable round>
      <div class="transfer-dialog">
        <div class="dialog-title">
          <van-icon name="guide-o" class="title-icon" />
          <span>后续处理</span>
        </div>
        <div class="dialog-content">请选择后续处理方式</div>
        <div class="dialog-buttons">
          <van-button type="primary" size="large" round @click="handleTransfer('监察')" class="transfer-btn">
            <van-icon name="shield-o" />
            转劳动监察
          </van-button>
          <van-button type="warning" size="large" round @click="handleTransfer('仲裁')" class="transfer-btn">
            <van-icon name="balance-o" />
            转劳动仲裁
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 劳动监察业务类型选择 -->
    <van-popup v-model="showBusinessTypeDialog" closeable round>
      <div class="business-dialog">
        <div class="dialog-title">
          <van-icon name="setting-o" class="title-icon" />
          <span>业务选择</span>
        </div>
        <div class="dialog-content">您需要转到劳动监察哪个业务？</div>
        <div class="business-buttons">
          <van-button type="primary" size="large" round @click="handleBusinessTypeSelected('投诉')" class="business-btn">
            <van-icon name="comment-o" />
            投诉
          </van-button>
          <van-button type="success" size="large" round @click="handleBusinessTypeSelected('举报')" class="business-btn">
            <van-icon name="warning-o" />
            举报
          </van-button>
          <van-button type="info" size="large" round @click="handleBusinessTypeSelected('行政调解')" class="business-btn">
            <van-icon name="friends-o" />
            行政调解
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 选择仲裁委员会 -->
    <van-popup class="organ-popup" v-model="showOrganPopup" position="bottom">
      <div class="organ-list">
        <p v-for="item in organList" :key="item.value" @click="handleSelectOrgan(item)">{{ item.label }}</p>
      </div>
    </van-popup>

    <van-popup class="commit-popup" v-model="showCommitPopup" position="center">
      <div class="commit-box">
        <div class="title">申请须知</div>
        <div class="content">
          <p>本入口为{{ organName }}。</p>
          <p>
            仲裁事项线上预申请通道，在您提交预申请材料后，本委将在3个工作日内进行预审核，若审核通过，预申请人需在3个工作日内携带相关材料到本委立案窗口现场核实，逾期未到现场核实的，视为放弃预申请。预申请人提交预申请材料应当遵守诚实信用原则，对上传材料的合法性、真实性负责。
          </p>
        </div>
        <van-radio-group v-model="checkCommit">
          <van-radio name="check" :disabled="timer > 0">我已仔细阅读申请须知</van-radio>
          <div v-if="timer > 0" class="timer">倒计时{{ timer }}S</div>
        </van-radio-group>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { commonApi } from "@/api"

export default {
  data() {
    return {
      formData: {
        aac003: "",
        aab004: "",
        abb280: "",
        abb277: "",
        abb026: "",
        abb286: "",
        abb027: ""
      },
      searchData: {
        page: 1,
        limit: 10,
        total: 0
      },
      //劳动维权一站式服务
      showSatisfyDialog: false,
      showTransferDialog: false,
      showBusinessTypeDialog: false,
      showAreaPicker: false,
      // 仲裁委员会
      showOrganPopup: false,
      organId: "", //id
      organName: "", //名称
      organList: [], //列表
      // 申请须知
      showCommitPopup: false,
      checkCommit: "",
      timer: 5,
      setTimer: null
    }
  },
  watch: {
    checkCommit(val) {
      if (val) {
        setTimeout(() => {
          this.handleCommit()

          this.showCommitPopup = false
        }, 100)
      }
    }
  },
  created() {
    const { bcz001 } = this.$route.query
    this.getJaxx({ bcz001 })
    this.getPlatformList() //查询字典列表
    this.getShowSatisfyDialog()
  },
  methods: {
    // 获取结案信息
    getJaxx(data) {
      const params = {
        serviceName: "xytBc01_getBc01AndBc45ById",
        ...data
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "获取结案信息")
        this.formData = res?.map?.data || {}
        const { abb277, aae037 } = this.formData
        this.formData.abb277 = this.dayFormatFn(abb277, "date")
        this.formData.aae037 = this.dayFormatFn(aae037, "date")
      })
    },
    getShowSatisfyDialog(){
      const { bcz001 } = this.$route.query
      const personParams = {
        serviceName: "xytBc01_getBc01ById",
        bcz001: bcz001
      }

      commonApi.proxyApi(personParams).then((res) => {
        const personInfoResult = res?.map?.data || {}
        if (!personInfoResult.sfmy00){
          this.showSatisfyDialog = true
        }
      }) //获取个人信息
    },
    // 获取平台列表
    getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["ZCY000"]
      }
      commonApi.proxyApi(params).then((res) => {
        const { data } = res.map
        const dictInfo = {
          "ZCY000": "organList"
        }
        for (const key in dictInfo) {
          this[dictInfo[key]] = data[key].map((item) => {
            return { label: item.aaa103, value: item.aaa102 }
          })
        }

        console.log(this.organList, "organList#######")
      })
    },
    // 满意度选择
    handleSatisfy(isSatisfy) {
      const sfmy00 = isSatisfy ? "001" : "000"
      const { bcz001 } = this.$route.query
      const params = {
        serviceName: "xytBc01_updateBc01Sfmy00",
        bcz001: bcz001,
        sfmy00: sfmy00 //数据来源
      }
      if (isSatisfy) {
        commonApi.proxyApi(params).then((res) => {
        })
        this.$toast("感谢您的反馈，本次调解已结束！")
        this.showSatisfyDialog = false
      } else {
        this.showSatisfyDialog = false
        this.showTransferDialog = true
      }
    },
    handleAreaSelected(area) {
      // 跳转仲裁页面并回填
      this.$router.push({
        path: "/labor-arbitrate/add-apply-people/index",
        query: {
          // 回填参数
        }
      })
      this.showAreaPicker = false
    },
    handleTransfer(type) {
      this.showTransferDialog = false
      if (type === "监察") {
        this.showBusinessTypeDialog = true
      } else if (type === "仲裁") {
        this.showOrganPopup = true
      }
    },
    async handleBusinessTypeSelected(businessType) {
      const { bcz001 } = this.$route.query
      const personParams = {
        serviceName: "xytBc01_getBc01ById",
        bcz001: bcz001
      }

      const res = await commonApi.proxyApi(personParams) //获取个人信息
      const personInfo = res.map.data
      let superviseType = ""
      if (businessType === "投诉") { superviseType = "1" } else if (businessType === "举报") { superviseType = "2" } else if (businessType === "行政调解") { superviseType = "3" }
      const fixedParams = { //固定参数
        range: 1,
        useAnd: true,
        orderMap: {},
        specialConditionArray: [],
        superviseType: superviseType
      }
      const dateMap = { ...personInfo }
      const searchMap = { ...personInfo }
      delete searchMap.dateBegin
      delete searchMap.dateEnd

      const data = {
        serviceName: "jftjsq_transferFromXyt",
        ...searchMap,
        ...this.searchData,
        ...fixedParams,
        searchMap: JSON.stringify(searchMap),
        dateMap: JSON.stringify(dateMap)
      }
      // 调用接口

      commonApi.proxyApi({serviceName: "xytCommon_tyscGetDataByInterface", data}).then(() => {
        const { bcz001 } = this.$route.query
        const params = {
          serviceName: "xytBc01_updateBc01Sfmy00",
          bcz001: bcz001,
          sfmy00: "000" //数据来源
        }
        this.updateInfoSfmy(params)
        this.$toast("已转劳动监察，数据已同步！")
      })
      this.showBusinessTypeDialog = false
    },
    setCountDown() {
      this.checkCommit = ""
      this.timer = 5
      clearInterval(this.setTimer)

      this.setTimer = setInterval(() => {
        this.timer--
        if (this.timer === 0) {
          clearInterval(this.setTimer)
        }
      }, 1000)
    },
    // 选择仲裁委员会
    handleSelectOrgan(data) {
      console.log(data, "data")
      const { label, value } = data
      this.organId = value
      this.organName = label
      this.showOrganPopup = false
      this.showCommitPopup = true
      this.setCountDown()
    },
    // 确定申请须知
    handleCommit() {
      const { organId } = this
      console.log(organId, "organId")
      // 添加formData参数传递
      this.$router.push({
        path: "/labor-arbitrate-handle",
        query: {
          pageType: "add",
          organId,
          formData: JSON.stringify(this.formData)
        }
      })
    },
    updateInfoSfmy(params){
      if (params.bcz001){
        params.serviceName = "xytBc01_updateBc01Sfmy00"
      } else {
        params.serviceName = "jftjsq_updateInfoSfmy"
      }
      if (!params.sfmy00){
        params.sfmy00 = "000"
      }
      commonApi.proxyApi(params).then((res) => {
      })
    }
  }
}
</script>

<style lang="less" scoped>
.labor-protect-case {
  overflow-x: hidden;

  .base-form {
    padding: 16px;

    .separate-box {
      position: relative;
      width: 120%;
      margin-left: -10%;
    }

    .button-box {
      padding: 0;
      background-color: #ffffff;

      &.case-button {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

/* 弹窗样式 */
.satisfy-dialog {
  padding: 32px 24px;
  text-align: center;
  min-width: 320px;

  .dialog-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: #323233;

    .title-icon {
      margin-right: 8px;
      font-size: 20px;
      color: #1989fa;
    }
  }

  .dialog-content {
    margin-bottom: 24px;
    font-size: 16px;
    color: #646566;
    line-height: 1.5;
  }

  .dialog-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;

    .satisfy-btn,
    .unsatisfy-btn {
      flex: 1;
      height: 44px;
      font-size: 16px;
      font-weight: 500;

      .van-icon {
        margin-right: 6px;
      }
    }
  }
}

.transfer-dialog {
  padding: 32px 24px;
  text-align: center;
  min-width: 320px;

  .dialog-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: #323233;

    .title-icon {
      margin-right: 8px;
      font-size: 20px;
      color: #ff976a;
    }
  }

  .dialog-content {
    margin-bottom: 24px;
    font-size: 16px;
    color: #646566;
    line-height: 1.5;
  }

  .dialog-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .transfer-btn {
      height: 44px;
      font-size: 16px;
      font-weight: 500;

      .van-icon {
        margin-right: 6px;
      }
    }
  }
}

.business-dialog {
  padding: 32px 24px;
  text-align: center;
  min-width: 320px;

  .dialog-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: #323233;

    .title-icon {
      margin-right: 8px;
      font-size: 20px;
      color: #07c160;
    }
  }

  .dialog-content {
    margin-bottom: 24px;
    font-size: 16px;
    color: #646566;
    line-height: 1.5;
  }

  .business-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .business-btn {
      height: 44px;
      font-size: 16px;
      font-weight: 500;

      .van-icon {
        margin-right: 6px;
      }
    }
  }
}

/deep/.organ-popup {
  height: 380px;

  .organ-list>p {
    font-size: 14px;
    color: #303133;
    line-height: 20px;
    text-align: center;
    margin: 30px;
  }
}

/deep/.commit-popup {
  width: 80%;
  border-radius: 12px;

  .commit-box {
    padding: 16px;

    .title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      text-align: center;
    }

    .content {
      margin-top: 20px;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      margin-bottom: 20px;
      text-indent: 2em;
    }

    .timer {
      font-size: 14px;
      text-align: center;
      margin-top: 8px;
      color: #FA7E00;
    }

    .van-radio {
      display: flex;
      justify-content: center;
      align-items: center;

      .van-radio__label {
        font-size: 14px;
      }
    }
  }
}
</style>
