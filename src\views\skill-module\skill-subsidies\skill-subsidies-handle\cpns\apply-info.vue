<!--
 * @Description: 申请信息
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="apply-info">    
    <van-form ref="baseForm" class="base-form" @failed="onFailed" @submit="handleNext">
      <y-title content="证书信息" />
      <van-cell-group inset class="border-bottom-wide">    
        <y-select-dict v-model="formData.jzbtlx" label="补贴申报类型" :required="true" :rules="formRules.jzbtlx" :filterabled="false" :disabled="pageType !== 'add'" dict-type="JZBT_BTLX00" is-link />

        <y-select
          v-if="pageType === 'add'"
          :required="true"
          label="证书编号"
          v-model="formData.zsbh00"
          :columns="zsbh00List"
          :format="conmonFormat"
          :rules="formRules.zsbh00"
          @change="handleChangeNumber"
        />  

        <van-field
          v-else
          v-model="formData.zsbh00"
          name="zsbh00"
          label="证书编号"
          placeholder="请输入"
          :required="true"
          disabled
        /> 

      </van-cell-group>
      <y-title content="银行信息" />
      <van-cell-group inset class="border-bottom-wide">        
        <van-field
          v-model="formData.yhkh00"
          name="yhkh00"
          label="银行卡号"
          placeholder="请输入"
          :required="true"
          :rules="formRules.yhkh00"
          @blur="getYhjg"
          clearable
        />

        <y-select
          class="y-select"      
          :required="true"
          label="银行机构"
          v-model="formData.yhjg00"
          :columns="bankList"
          :format="conmonFormat"
          :rules="formRules.yhjg00"
          clearable
        />   

        <y-select
          class="y-select"
          :required="true"
          label="分行（省）"
          v-model="formData.province"
          :columns="provinceList"
          :format="format"
          :rules="formRules.province"
          @change="changeProvince"
        />

        <y-select
          :required="true"
          label="分行（市）"
          v-model="formData.city"
          :columns="cityList"
          :format="format"
          :rules="formRules.city"
          @change="changeCity"
        />     
         
      </van-cell-group>
      <div class="button-box-more">
        <van-button plain type="info" @click="handleCancle" native-type="button">
          {{ pageType !== 'detail' ? '取 消' : '关 闭' }}
        </van-button> 

        <!-- @click="handleNext" -->
        <van-button round block type="primary" native-type="submit" :disabled="isDisabledBtn">
          下一步
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import {commonApi} from "@/api"
import {provinceList, bankList} from "@/assets/data/jnbt-dict"
import {checkBankNo} from "@/utils/check"

export default {
  name: "apply-info",
  components: {},
  props: {
    baseFormData: {
      type: Object,
      default: () => () => {}
    }
  },  
  data() {
    return {
      // 表单信息
      formData: {
        jzbtlx: "", //补贴申报类型
        zsbh00: "", //证书编号
        yhkh00: "", //银行卡号
        yhjg00: "", //银行机构
        province: "", //分行（省）
        city: "", //分行（市）
        fkhzh0: "" //支行
      },
      formRules: {
        jzbtlx: [{ required: true, message: "请选择补贴申报类型" }],
        zsbh00: [{ required: true, message: "请选择证书编号" }],
        yhkh00: [
          { required: true, message: "请输入银行卡号" },
          {
            validator: checkBankNo,
            message: "请输入正确的银行卡号",
            trigger: "onBlur"
          }
        ],
        yhjg00: [{ required: true, message: "请选择银行机构" }],
        province: [{ required: true, message: "请选择分行（省）" }],
        city: [{ required: true, message: "请选择分行（市）" }]
      },
      // 证书编号
      zsbh00List: [],
      // 地区选择
      provinceList,
      format: {
        name: "name",
        value: "name"
      },
      // 银行机构
      bankList,
      conmonFormat: {
        name: "label",
        value: "value"
      },
      // 下一步
      isDisabledBtn: false
    }
  },
  watch: {
    baseFormData: {
      handler(val) {
        for (const key in this.formData) {
          if (val[key]) {
            this.formData[key] = val[key]
          }
        }
      },
      immediate: true,
      deep: true
    },
    "formData.city": {
      handler(val) {
        this.formData.fkhzh0 = val ? `${val}分行` : ""
      },
      immediate: true
    },
    "formData.zsbh00": {
      handler(val) {
        if (val && this.pageType === "add") {
          this.getZsxx()
        }
      },
      immediate: true
    },
    "formData.jzbtlx": {
      handler() {
        if (this.pageType === "add") {
          this.formData.zsbh00 = ""
        }        
      },
      immediate: true
    }
  },
  computed: {
    pageType() {
      return this.$route.query.pageType || ""
    },
    userInfo() {
      const {xm0000: aac003, zjhm00: aac002} = this.$sessionUtil.getItem("userInfo")
      return {aac003, aac002}
    },
    cityList() {
      const {province} = this.formData
      if (!province) {
        return [{ name: "请选择", areaList: [""] }]
      }

      const list = this.provinceList.find(item => (item.name === province))?.cityList || []
      return list
    }
  },
  created() {
    if (this.pageType !== "add") {
      return
    }     
     
    this.getZsbhList() // 获取证书编号列表
  },
  methods: {
    handleChangeNumber(data) {
      if (!this.formData.jzbtlx) {
        this.formData.zsbh00 = ""
        this.$dialog.alert({
          title: "提示",
          message: "请先选择补贴申报类型！",
          theme: "round-button"
        })
      }
    },
    // 获取证书信息
    getZsxx() {
      const { zsbh00, jzbtlx } = this.formData
      const params = {
        serviceName: "xytjzbt_getZsxx",
        ...this.userInfo,
        zsbh00,
        jzbtlx
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "获取证书信息")
        const data = res.data?.[0] || {}
        this.formData = { ...this.formData, ...data, zsbh00, jzbtlx }
        this.$emit("updateFormData", this.formData)
        this.isDisabledBtn = false
      }).catch(() => {
        this.isDisabledBtn = true
      })
    },
    // 获取证书编号列表
    getZsbhList() {
      const params = {
        serviceName: "xytjzbt_getZsbhList",
        ...this.userInfo
      }
      commonApi.proxyApi(params).then(res => {        
        const {rowLists} = res.data[0]
        this.zsbh00List = rowLists.map(item => ({value: item, label: item}))
      })
    },
    // 选择省份
    changeProvince(val) {
      if (val === "请选择") {
        this.formData.province = ""
        this.formData.city = ""
      }
    },
    // 选择城市
    changeCity(val) {
      if (val === "请选择") {
        this.formData.city = ""
      }
    },
    // 表单校验失败
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },
    // 获取银行机构信息
    getYhjg() {
      const {yhkh00} = this.formData
      if (!yhkh00) {
        this.formData.yhjg00 = ""
        return
      }

      const params = {
        serviceName: "xytjzbt_getYhjg",
        yhkh00
      }
      commonApi.proxyApi(params).then(res => {
        console.log(res, "获取银行机构信息ers")
        this.formData.yhjg00 = res.data?.[0]?.yhjg00 || ""
      })
    },
    // 取消
    handleCancle() {
      window.history.go(-1)
    },
    // 下一步
    handleNext() {
      this.$emit("handleNext", 1)      
    }
  }
}
</script>

<style lang="less" scoped>
.button-box {
  padding: 15px 0;
  width: 100%;
  display: flex;
  background-color: #fff;
  
  .van-button  {
    flex: 1;
    .van-button__content {
      color: @main_color;
    }
  }
}
.search-field {
  /deep/.van-field__control {
    text-align: left;
  }
}
</style>