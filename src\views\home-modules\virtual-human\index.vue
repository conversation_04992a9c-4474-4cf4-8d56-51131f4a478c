<template>
  <div class="virtual-human">
    <ErrorTip
      v-if="errorPage"
      :errorPage="errorPage"
      :errorTip="errorTip"
      @toHome="toHome"
    ></ErrorTip>
    <template v-else>
      <Home
        v-if="showHome"
        :loginStatus="loginStatus"
        @enter="homeEnter"
      ></Home>
      <Disclaimer
        v-else-if="countdown"
        :countdownTime="countdownTime"
        :loginStatus="loginStatus"
      ></Disclaimer>
      <template v-if="!showHome && !countdown">
        <MiniDigitalHuman 
          ref="miniDigitalHuman"
          v-if="!fullView"
          @changeLongVoice="val => changeLongVoice(val, 'newDigitalHuman')"
        ></MiniDigitalHuman>
        <NewDigitalHuman
          ref="newDigitalHuman"
          v-else
          @changeLongVoice="val => changeLongVoice(val, 'miniDigitalHuman')"
        ></NewDigitalHuman>
        <div class="new-dialogue" @click="newDialogue">
          <img src="~@pic/home/<USER>/chat.svg" />
          <span>开启新对话</span>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import ErrorTip from "./error-tip.vue"
import Disclaimer from "./disclaimer.vue"
import Home from "./home.vue"
import MiniDigitalHuman from "./mini-digital-human.vue"
import NewDigitalHuman from "./new-digital-human.vue"
import { setYlzinsToken, setEncryptEnabled, getEncryptEnabled } from "@/utils/cookie"
import { encryptApi } from "@/api"
import { setMessages, getMessages } from "@/utils/cookie"

export default {
  components: {
    ErrorTip,
    Home,
    Disclaimer,
    MiniDigitalHuman,
    NewDigitalHuman
  },
  data() {
    return {
      countdown: true,
      countdownTime: 2,
      countdownTimer: null,
      fullView: false,
      loginStatus: "",
      showHome: true,
      errorPage: false,
      errorTip: "",
      type: "new"
    }
  },
  created() {
    this.homeStartCountdown()
    // 判断是否刷新
    if (performance.navigation.type === 1) {
      this.toRefreshPage()
    }
    this.loginStatus = ""
    this.encryptConfig()
  },
  beforeDestroy() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
      this.countdownTimer = null
    }
  },
  methods: {
    toRefreshPage() {
      const url = new URL(window.location.href)
      let newUrl = url
      if (url.searchParams.has("token")) {
        url.searchParams.delete("token")
        // 构造去掉token的新url
        newUrl = url.origin + url.pathname + (url.searchParams.toString() ? "?" + url.searchParams.toString() : "") + url.hash
      }
      window.location.replace(process.env.VUE_APP_LOGIN_URL + newUrl)
      return
    },
    encryptConfig() {
      encryptApi.encryptConfig().then(res => {
        setEncryptEnabled(res.data.enabled)
        this.getSsoWxLogin()
      })
    },
    getNestedTokenFromUrl(url) {
      const href = url
      const tokenMatch = href.match(/[?&]token=([^&#]+)/)
      return tokenMatch ? decodeURIComponent(tokenMatch[1].replace(/\+/g, " ")) : null
    },
    getSsoWxLogin() {
      const token = this.getNestedTokenFromUrl(window.location.href)
      if (!token) {
        this.toRefreshPage()
        return
      }
      encryptApi.ssoWxLogin({ token: token }, { encryptEnabled: getEncryptEnabled() }).then(res => {
        if (res?.data?.errorCode == 0) {
          setYlzinsToken(res.data.data.token)
          this.aiBotDetail()
        } else {
          this.loginStatus = "fail"
          this.errorPage = true
          this.errorTip = res.data.message || "登录失败，请重新登录"
        }
      })
    },
    startCountdown() {
      this.countdownTimer = setInterval(() => {
        if (this.countdownTime > 1) {
          this.countdownTime--
        } else {
          this.countdown = false
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      }, 1000)
    },
    changeLongVoice(val, dom) {
      this.fullView = val
      this.$nextTick(() => {
        this.$refs[dom].setGuessQuestions()
      })
    },
    aiBotDetail() {
      encryptApi.aiBotDetail({}, { encryptEnabled: getEncryptEnabled() }).then(res => {
        if (res?.data?.errorCode == 0 && res.data.data?.options) {
          const publicWebChannelEnabled = res.data.data.options?.publicWebChannelEnabled || false
          const status = res.data.data.status
          if (publicWebChannelEnabled && status == 1) {
            this.loginStatus = "success"
            this.startCountdown()
          } else {
            this.loginStatus = "fail"
            this.errorPage = true
            if (!publicWebChannelEnabled) {
              this.errorTip = res.data.message || "当前应用正在调试中，暂不支持访问。"
              return
            }
            if (status != 1) {
              this.errorTip = res.data.message || "应用升级中，暂不支持访问。"
              return
            }
          }
        } else {
          this.loginStatus = "fail"
          this.errorPage = true
          this.errorTip = res.data.message || "当前应用正在调试中，暂不支持访问。"
        }
      })
    },
    homeStartCountdown() {
      setTimeout(() => {
        this.showHome = false
      }, 1000)
    },
    toHome() {
      this.showHome = true
      this.errorPage = false
    },
    homeEnter() {
      if (this.loginStatus === "fail") {
        this.loginStatus = ""
        this.toRefreshPage()
      } else {
        this.showHome = false
      }
    },
    newDialogue() {
      encryptApi.aiBotDetail({}, { encryptEnabled: getEncryptEnabled() }).then(res => {
        if (res?.data?.errorCode == 0 && res.data.data?.h5Human) {
          const globalParams = JSON.parse(res.data.data.h5Human)
          if (globalParams.globalParams.welcome.welcome_text) {
            setMessages(this.type, JSON.stringify([{ role: "assistant", content: globalParams.globalParams.welcome.welcome_text }]))
          } else {
            setMessages(this.type, JSON.stringify([]))
          }
        }
      }).catch(() => {
        setMessages(this.type, JSON.stringify([]))
      }).finally(() => {
        if (this.fullView) {
          this.$refs.newDigitalHuman.messages = JSON.parse(getMessages(this.type))
          this.$refs.newDigitalHuman.stopAnswer()
          this.$refs.newDigitalHuman.guessQuestions = []
        } else {
          this.$refs.miniDigitalHuman.messages = JSON.parse(getMessages(this.type))
          this.$refs.miniDigitalHuman.stopAnswer()
          this.$refs.miniDigitalHuman.guessQuestions = []
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.virtual-human {
  width: 100vw;
  height: 100vh;
  font-size: 14px;
}

.new-dialogue {
  z-index: 9;
  position: absolute;
  left: 50%;
  bottom: 90px;
  transform: translate(-50%, 0%);
  font-size: 12px;
  color: #fff;
  background-color: #0194FF;
  border-radius: 20px;
  padding: 5px 10px;
  display: flex;
  align-items: center;
  line-height: 1;

  img {
    width: 14px;
    margin-right: 3px;
  }
}
</style>