<!--
 * @Description: 业务类型-表单信息
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <y-title
      content="申请人基本情况"
      moreText
      :colorMore="colorMore"
      :moreType="3"
      foldName="showBaseInfo"
      @changeFold="changeFold"
    />

    <van-cell-group inset v-show="showBaseInfo" class="border-bottom-wide">
      <van-field
        v-model="formData.aac003"
        name="aac003"
        :label="aac003Label"
        placeholder="请输入"
        :required="true"
        :disabled="true"
        :rules="formRules.aac003"
      />

      <y-select-dict
        v-show="businessType !== 'xztj'"
        v-model="formData.aac058"
        label="证件类型"
        :rules="formRules.aac058"
        dict-type="AAC058_DICT"
        :disabled="true"
        is-link
      />

      <van-field
        v-model="formData.aac147"
        name="aac147"
        label="证件号码"
        placeholder="请输入"
        :required="true"
        :disabled="true"
        :rules="formRules.aac147"
      />

      <y-select-dict
        v-model="formData.aac004"
        :filterabled="false"
        :rules="formRules.aac004"
        dict-type="AAC004"
        label="性别"
        :disabled="true"
        is-link
      />

      <van-field
        v-model="formData.abb919"
        name="abb919"
        label="年龄"
        :required="true"
        :disabled="true"
        :rules="formRules.abb919"
        placeholder="请输入"
      />

      <van-field
        class="label-width"
        v-model="formData.aac151"
        name="aac151"
        type="textarea"
        input-align="left"
        :autosize="{ maxHeight: 70, minHeight: 50 }"
        maxlength="100"
        label="户籍地址"
        placeholder="请输入"
      />

      <van-field
        v-if="businessType !== 'xztj'"
        v-model="formData.aac152"
        name="aac152"
        label="现通信地址"
        :required="true"
        :rules="formRules.aac152"
        placeholder="请输入"
      />

      <van-field
        v-model="formData.abb284"
        name="abb284"
        label="涉及人数"
        :required="true"
        :rules="formRules.abb284"
        placeholder="请输入"
        :disabled="businessType !== 'juBao' || pageType === 'detail'"
      />

      <van-field
        v-model="formData.abb281"
        name="abb281"
        label="联系电话"
        :required="true"
        placeholder="请输入"
        :rules="formRules.abb281"
      />

      <template v-if="businessType === 'else'">
        <y-select-dict
          v-model="formData.abba69"
          :filterabled="false"
          :rules="formRules.abba69"
          dict-type="YES_NO"
          label="是否孕妇"
          :disabled="pageType === 'detail'"
          :required="true"
          is-link
        />

        <y-select-dict
          v-model="formData.aae411"
          :filterabled="false"
          :rules="formRules.aae411"
          dict-type="YES_NO"
          label="是否农民工"
          :disabled="pageType === 'detail'"
          :required="true"
          is-link
        />
      </template>

      <template v-if="businessType !== 'juBao'">
        <y-select-dict
          v-model="formData.abbaa2"
          :filterabled="false"
          :rules="formRules.abbaa2"
          dict-type="YES_NO"
          label="是否集体访"
          :disabled="pageType === 'detail'"
          :required="true"
          is-link
        />

        <y-select-dict
          v-model="formData.abbaa0"
          :filterabled="false"
          :rules="formRules.abbaa0"
          dict-type="YES_NO"
          label="是否重复访"
          :disabled="pageType === 'detail'"
          :required="true"
          is-link
        />

        <y-select-dict
          class="last-select-cell"
          v-model="formData.abbaa1"
          :filterabled="false"
          :rules="formRules.abbaa1"
          dict-type="YES_NO"
          label="是否重复信"
          :disabled="pageType === 'detail'"
          :required="true"
          is-link
        />
      </template>

      <template v-if="businessType === 'juBao'">
        <y-select-dict
          class="last-select-cell"
          v-model="formData.sfnm"
          :filterabled="false"
          :rules="formRules.sfnm"
          dict-type="YES_NO"
          label="是否实名"
          :disabled="pageType === 'detail'"
          :required="true"
          is-link
        />
      </template>
    </van-cell-group>

    <y-title
      content="被申请单位信息"
      moreText
      :colorMore="colorMore"
      :moreType="3"
      foldName="showUnitInfo"
      @changeFold="changeFold"
    />

    <van-cell-group inset v-show="showUnitInfo" class="border-bottom-wide">
      <van-search
        v-if="pageType !== 'detail'"
        class="search-filed"
        v-model="unitValue"
        show-action
        left-icon=""
        input-align="left"
        placeholder="请输入单位名称查询"
        @search="handleSearchUnit"
      >
        <template #action>
          <div @click="handleSearchUnit">查询</div>
        </template>
      </van-search>

      <van-field
        class="show-placeholder"
        v-model="formData.aab004"
        name="aab004"
        label="单位名称"
        placeholder="查询单位后自动回填"
        :required="true"
        :rules="formRules.aab004"
        disabled
      />

      <van-field
        v-model="formData.abb300"
        name="abb300"
        label="社会信用代码"
        placeholder="查询单位后自动回填"
        :rules="formRules.abb300"
      />

      <van-field
        class="label-width"
        type="textarea"
        input-align="left"
        :autosize="{ maxHeight: 70, minHeight: 50 }"
          maxlength="100"
        v-model="formData.aae006"
        name="aae006"
        label="被申请单位注册地址"
        placeholder="查询单位后自动回填"
        :required="true"
        :rules="formRules.aae006"
      />

      <van-field
        v-model="formData.aab513"
        name="aab513"
        label="单位联系电话"
        placeholder="请输入"
        :required="isRrequired"
        :rules="formRules.aab513"
      />

      <template v-if="businessType === 'else'">
        <van-field
          v-model="formData.aae406"
          name="aae406"
          label="实际经营地址"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aae406"
        />
      </template>

      <template v-else>
        <van-field
          class="label-width"
          type="textarea"
          input-align="left"
          :autosize="{ maxHeight: 70, minHeight: 50 }"
          maxlength="100"
          v-model="formData.aae406"
          name="aae406"
          label="被申请单位现实际地址"
          :required="isRrequired"
          placeholder="请输入"
          :rules="formRules.aae406"
        />
      </template>

      <van-field
        v-model="formData.aab013"
        name="aab013"
        label="单位联系人"
        placeholder="请输入"
        :required="isRrequired"
        :rules="formRules.aab013"
      />

      <van-field
        class="label-width"
        v-model="formData.abb299"
        name="abb299"
        label-width="140"
        label="单位直接联系人电话"
        placeholder="请输入"
        :required="isRrequired"
        :rules="formRules.abb299"
      />

      <van-field
        v-model="formData.aab020"
        name="aab020"
        label="班组长名字"
        placeholder="请输入"
        :rules="formRules.aab020"
      />

      <y-select-dict
        class="label-width last-select-cell"
        v-model="formData.aae408"
        label="是否为建筑单位"
        :required="true"
        :filterabled="false"
        :rules="formRules.aae408"
        dict-type="YES_NO"
        :disabled="pageType === 'detail'"
        is-link
      />

      <template
        v-if="
          formData.aae408 === '1' &&
          !(pageType === 'detail' && businessType === 'xztj')
        "
      >
        <van-search
          v-if="pageType !== 'detail'"
          class="search-filed"
          v-model="projectValue"
          show-action
          left-icon=""
          input-align="left"
          placeholder="请输入项目名称查询"
          @search="handleSearchProject"
        >
          <template #action>
            <div @click="handleSearchProject">查询</div>
          </template>
        </van-search>

        <van-field
          class="show-placeholder"
          v-model="formData.projectname"
          name="projectname"
          label="建筑工程项目"
          placeholder="查询项目后自动回填"
          :required="true"
          :rules="formRules.projectname"
          disabled
        />
      </template>
    </van-cell-group>

    <!-- 单位名称查询弹窗 -->
    <van-popup
      class="search-popup"
      v-model="showPicker"
      closeable
      position="center"
      :close-on-click-overlay="false"
    >
      <div class="search-popup-box">
        <div class="search-title">选择单位</div>

        <div
          class="search-content"
          v-for="(item, index) in unitList"
          :key="index"
        >
          <div class="unit-name">
            <span class="title">单位名称：</span>
            <span>{{ item.text }}</span>
          </div>
          <div class="unit-adress">
            <span class="title">单位地址：</span>
            <span>{{ item.aae006 }}</span>
          </div>
          <van-button
            class="select-button"
            @click="handleConfirmUnit(item)"
            block
            size="mini"
            type="primary"
            native-type="button"
          >
            选 择
          </van-button>
        </div>

        <div class="search-button flex-c-c">
          <van-button
            plain
            type="info"
            round
            @click="showPicker = false"
            native-type="button"
          >
            关 闭
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 建筑工程项目查询弹窗 -->
    <van-popup
      class="search-popup"
      v-model="showProjectPicker"
      closeable
      position="center"
      :close-on-click-overlay="false"
    >
      <div class="search-popup-box">
        <div class="search-title">选择工程</div>

        <div
          class="search-content"
          v-for="(item, index) in projectList"
          :key="index"
        >
          <div class="unit-name">
            <span class="title">项目名称：</span>
            <span>{{ item.text }}</span>
          </div>
          <van-button
            class="select-button"
            @click="handleConfirmProject(item)"
            block
            size="mini"
            type="primary"
            native-type="button"
          >
            选 择
          </van-button>
        </div>

        <div class="search-button flex-c-c">
          <van-button
            plain
            type="info"
            round
            @click="showProjectPicker = false"
            native-type="button"
          >
            关 闭
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { five_text_color } from "@/styles/theme/theme-params.less"
import { commonApi } from "@api"
import { checkMobile, checkInteger } from "@utils/check"
import { getInfoByIdCard, getAge } from "@/utils/common"

const AAC058_DICT = [
  { value: "1", label: "居民身份证(户口簿)" },
  { value: "10", label: "军烈属证明" },
  { value: "2", label: "中国人民解放军军官证" },
  { value: "3", label: "中国人民武装警察警官证" },
  { value: "4", label: "香港特区护照、港澳居民往来内地通行证" },
  { value: "5", label: "澳门特区护照、港澳居民往来内地通行证" },
  { value: "6", label: "台湾居民来往大陆通行证" },
  { value: "8", label: "外国人护照" },
  { value: "9", label: "残疾人证" },
  { value: "90", label: "社会保障卡" }
]

const AAC003_NAME = {
  xztj: "申请人姓名",
  else: "投诉人姓名",
  juBao: "举报人姓名"
}
export default {
  props: {
    businessType: {
      type: String,
      default: "xztj" //业务类型 默认行政调解
    },
    pageType: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      formData: {
        aac003: "", //姓名
        aac058: "", //证件类型
        aac147: "", //证件号码
        aac004: "", //性别
        abb919: "", //年龄
        aac151: "", //户籍地址
        aac152: "", //现通信地址
        abb284: "1", //涉及人数
        abb281: "", //联系电话
        abba69: "", //是否孕妇
        aae411: "", //是否农民工
        abbaa2: "", //是否集体访
        abbaa0: "", //是否重复访
        abbaa1: "", //是否重复信
        sfnm: "", //是否实名
        aab004: "", //单位名称
        abb300: "", //社会信用代码
        aae006: "", //被申请单位注册地址
        aab513: "", //单位联系电话
        aae406: "", //被申请单位现实际地址|实际经营地址
        aab013: "", //单位联系人
        abb299: "", //单位直接联系人电话
        aab020: "", //班组长名字
        aae408: "", //是否为建筑单位
        projectname: "" //建筑工程项目
      },
      formRules: {
        aac003: [{ required: true, message: "请输入" }],
        aac058: [{ required: true, message: "请选择" }],
        aac147: [{ required: true, message: "请输入" }],
        aac004: [{ required: true, message: "请选择" }],
        abb919: [
          { required: true, message: "请输入" },
          {
            validator: checkInteger,
            message: "请输入正确的年龄",
            trigger: "onBlur"
          }
        ],
        aac151: [{ required: true, message: "请选择" }],
        aac152: [{ required: true, message: "请输入" }],
        abb284: [
          { required: true, message: "请输入" },
          {
            validator: checkInteger,
            message: "请输入正确的人数",
            trigger: "onBlur"
          }
        ],
        abb281: [
          { required: true, message: "请输入" },
          {
            validator: checkMobile,
            message: "请输入正确的手机号",
            trigger: "onBlur"
          }
        ],
        abba69: [{ required: true, message: "请选择" }],
        aae411: [{ required: true, message: "请选择" }],
        abbaa2: [{ required: true, message: "请选择" }],
        abbaa0: [{ required: true, message: "请选择" }],
        abbaa1: [{ required: true, message: "请选择" }],
        sfnm: [{ required: true, message: "请选择" }],
        aab004: [{ required: true, message: "请输入" }],
        aae006: [{ required: true, message: "请输入" }],
        aae408: [{ required: true, message: "请选择" }],
        projectname: [{ required: true, message: "请输入" }],

        aae406: [{ required: true, message: "请输入" }],
        aab513: [{ required: true, message: "请输入" }],
        aab013: [{ required: true, message: "请输入" }],
        abb299: [{ required: true, message: "请输入" }]
      },

      unitValue: "", //单位搜索
      showPicker: false, //单位信息弹窗
      unitList: [], //单位信息列表

      projectValue: "", //项目搜索
      showProjectPicker: false, //项目信息弹窗
      projectList: [], //项目信息列表
      isShowProjectname: false,

      // 标题
      colorMore: five_text_color,
      showBaseInfo: true, //申请人基本信息
      showUnitInfo: true //被申请人人单位信息
    }
  },
  watch: {
    "formData.aae408": {
      handler(val) {
        this.isShowProjectname = val === "1"
      }
    },
    "formData.sfnm": {
      //匿名 提示
      handler(val) {
        if (!val || val === "1" || this.pageType === "detail") {
          return
        }

        const message =
          "如选择保密，您将无法获得我局相关办理和核实反馈信息，请慎重考虑。如您反映问题为个人权益受侵犯，建议您通过行政调解或投诉方式。"

        this.$dialog.alert({
          title: "提示",
          message,
          theme: "round-button"
        })
      }
    },
    formData: {
      handler(val) {
        if (this.pageType === "detail") {
          this.formData.aac058 = val.aac058 || "" //行政调解添加证件类型字段 避免字典报错
        }
      }
    },
    businessType(val) {
      const list = ["aab513", "aae406", "aab013", "abb299"]
      list.forEach((item) => {
        this.formRules[item][0].required = val !== "juBao"
      })
    }
  },
  computed: {
    aac003Label() {
      return AAC003_NAME[this.businessType]
    },
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    },
    isRrequired() {
      //不同类型必填项
      return this.businessType !== "juBao"
    }
  },
  created() {
    this.initFormData()

    const codeList = this.$sessionUtil.getItem("codeList") || {}
    codeList.AAC058_DICT = AAC058_DICT
    this.$sessionUtil.setItem("codeList", codeList)
  },
  methods: {
    // 初始化人员基本信息
    initFormData() {
      const { xm0000: aac003, zjhm00: aac147 } = this.userInfo
      const { sex: aac004, birthdate } = getInfoByIdCard(aac147)
      const birthdateValue = this.dayFormatFn(birthdate, "date")
      this.formData = {
        ...this.formData,
        aac147,
        aac058: "1",
        aac003,
        aac004,
        abb919: getAge(birthdateValue)
      } //证件类型默认身份证
    },
    // 折叠表单
    changeFold({ foldName, state }) {
      console.log(foldName, "foldName")
      console.log(state, "state")
      this[foldName] = state
    },
    // 搜索单位
    async handleSearchUnit() {
      console.log(this.unitValue, "unitValue")
      const { unitValue: aab004 } = this
      if (!aab004) {
        this.$dialog.alert({
          title: "温馨提示",
          message: "请输入单位名称查询！",
          theme: "round-button",
          showConfirmButton: true
        })
        return
      }

      const params = {
        serviceName: "jftjsq_queryCompanyList",
        aab004,
        page: "1",
        limit: "10"
      }
      const res = await commonApi.proxyApi(params)

      const { getUnitInfo = [] } = res.map || {}
      if (getUnitInfo.length === 0) {
        this.$toast("未查询到相关单位信息！")
        return
      }
      this.unitList = getUnitInfo.map((item) => ({
        aab001: item.aab001,
        aab004: item.aab004,
        abb300: item.aab525,
        aae006: item.aae006,
        // aab513: item.aab513,
        text: item.aab004,
        value: item.aab004
      }))

      this.showPicker = true
    },
    // 选择单位
    handleConfirmUnit(data) {
      console.log(data, "data")
      this.formData = { ...this.formData, ...data }
      this.showPicker = false
    },
    // 搜索项目
    async handleSearchProject() {
      const { projectValue: key } = this
      if (!key) {
        this.$dialog.alert({
          title: "温馨提示",
          message: "请输入建筑工程项目名称查询！",
          theme: "round-button",
          showConfirmButton: true
        })
        return
      }

      const params = {
        serviceName: "jftjsq_getProject",
        key
      }
      const res = await commonApi.proxyApi(params)
      const { list = [] } = res.map || {}
      if (list.length === 0) {
        this.$toast("未查询到相关项目信息！")
        return
      }
      this.projectList = list.map((item) => ({
        text: item.text,
        value: item.id
      }))

      console.log(this.projectList, "this.projectList")

      this.showProjectPicker = true
    },
    // 选择项目
    handleConfirmProject(data) {
      console.log(data, "data")
      const { text, value } = data
      this.formData.projectname = text
      this.formData.rowguid = value
      this.formData.site_project = text
      this.showProjectPicker = false
    }
  }
}
</script>
<style lang="less" scoped>
.search-filed {
  position: relative;
  padding-right: 16px;
  .van-search__action {
    text-align: right;
    padding: 0;
    color: #1c65b9;
    font-weight: bold;
  }
  /deep/ .van-search__content {
    background-color: #fff;
    padding-left: 0;
    .van-cell .van-field__control {
      text-align: left;
      color: @main_text_color;
      &::placeholder {
        color: @four_text_color;
      }
    }
  }
  &::after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: 16px;
    bottom: 0;
    left: 16px;
    border-bottom: 0.5px solid @border_color;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}
.search-popup {
  width: 90% !important;
  max-height: calc(100vh - 80px);
  border-radius: 8px;
  .search-popup-box {
    .search-title {
      text-align: center;
      font-size: 16px;
      margin: 16px 0;
    }
    .search-content {
      padding: 16px;
      font-size: 14px;
      line-height: 22px;
      border-bottom: 1px solid @border_color;
      .unit-adress {
        margin-top: 8px;
      }
      .title {
        font-weight: bold;
      }
      .select-button {
        width: 58px;
        font-size: 12px;
        margin-top: 8px;
      }
    }
    .search-button {
      padding: 12px 0;
      .van-button--primary {
        margin-left: 8px;
      }
    }
    /deep/.van-radio-group {
      padding: 4px 16px;
      font-size: 14px;
    }
    /deep/.van-field-textarea {
      font-size: 14px;
    }
  }
}
</style>
