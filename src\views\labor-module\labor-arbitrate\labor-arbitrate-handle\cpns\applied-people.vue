<!--
 * @Description: 被申请人
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="applied-people">
    <add-shared title="被申请人" add-title="被申请人" :showPackUp="list.length > 0" :showAddBtn="pageType !== 'details'" @handleAdd="handleAdd">
      <template slot="content">
        <info-box v-for="(item,index) in list" :key="index" :title="'被申请人' + (index + 1)">
          <template #cells>
            <van-cell title="被申请人类型" :value="getDictName(applyTypeList, item.aac001)" :border="false" />

            <template v-if="item.aac001 === '001'">
              <van-cell title="姓名" :value="item.aac003" :border="false" />
              <van-cell title="证件号码" :value="item.aac002" :border="false" />
            </template>

            <template v-else>
              <van-cell title="单位名称" :value="item.aab004" :border="false" />
              <van-cell title="法定代表人" :value="item.aab013" :border="false" />
            </template>

          </template>
          <template #buttons>
            <template v-if="pageType !== 'details'">
              <van-button type="primary" @click="handleEdit(item.bczbsq)">修改</van-button>
              <van-button type="warning" @click="handleDelete(item.bczbsq)">删除</van-button>
            </template>

            <van-button v-else type="warning" class="info-button" @click="handleDetails(item.bczbsq)">查看详情</van-button>
          </template>
        </info-box>
      </template>
    </add-shared>
  </div>
</template>

<script>
import InfoBox from "@/components/business/info-box"
import AddShared from "./add-shared"

import isEmpty from "lodash/isEmpty"
import {commonApi} from "@/api"
import {getDictName} from "@utils/common"

export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    propList: {
      type: Array,
      default: () => []
    }
  },
  components: {
    InfoBox,
    AddShared
  },
  data() {
    return {
      list: [],

      applyTypeList: [] //预申请人类型
    }
  },
  computed: {
    pageType(){
      return this.$route.query.pageType
    }
  },
  watch: {
    propList(val) {
      if (this.pageType === "details") { //详情页面
        this.list = val
      }
    }
  },
  created() {
    const {AAC001} = this.$sessionUtil.getItem("codeList") || {}
    if (isEmpty(AAC001)) {
      this.getPlatformList() //查询字典列表
    } else {
      this.applyTypeList = AAC001
    }

    if (this.pageType !== "details") { //新增|编辑页面
      this.findBc05BsqrByPage() //查询申请列表
    }
  },
  methods: {
    //查询字典列表
    getDictName,
    getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["AAC001"]
      }
      commonApi.proxyApi(params).then((res) => {
        const {data} = res.map
        const dictInfo = {
          "AAC001": "applyTypeList"
        }
        for (const key in dictInfo) {
          this[dictInfo[key]] = data[key].map((item) => {
            return {label: item.aaa103, value: item.aaa102}
          })
        }

        console.log(this.applyTypeList, "applyTypeList#######")
      })
    },
    // 查询申请列表
    findBc05BsqrByPage() {
      commonApi.proxyApi({
        serviceName: "xytBc05Bsqr_findBc05BsqrByPage"
      }).then((res) => {
        console.log(res, "res222")
        this.list = res.map?.data?.rows || []
      }).catch((err) => {
        console.error(err)
      })
    },
    // 添加
    handleAdd() {
      const zcy000 = this.$attrs.organId
      // 获取父组件中的formData
      const formData = this.$parent.formData || {}
      console.log("传入表单数据", formData)
      this.$router.push({
        path: "/add-applied-people",
        query: {
          pageType: "add",
          zcy000,
          formData: JSON.stringify(formData)
        }
      })
    },
    // 修改
    handleEdit(bczbsq) {
      this.$router.push({path: "/add-applied-people", query: {pageType: "edit", bczbsq }})
    },
    // 删除
    handleDelete(id) {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定删除",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        commonApi.proxyApi({
          serviceName: "xytBc05Bsqr_deleteBc05BsqrByIds",
          ids: [id]
        }).then((res) => {
          console.log("已删除！", res)
          this.$toast("已删除！")
          this.findBc05BsqrByPage()
        }).catch((err) => {
          console.error(err)
        })
      })
    },
    // 查看详情
    handleDetails(bczbsq) {
      this.$router.push({path: "/add-applied-people", query: {pageType: "details", bczbsq }})
    }
  }
}
</script>
<style lang="less" scoped>
</style>
