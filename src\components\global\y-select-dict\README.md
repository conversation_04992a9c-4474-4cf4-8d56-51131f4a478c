# YSelectDict 字典选择框

### 介绍

`YSelectDict` 是 `Field` 与 `Picker` 组合的业务场景组件，用于数据字典字段的选择和展示， `YSelectDict` 本质还是个 `Field` 组件用法与 `Field` 一致，继承 `Field` 属性与方法。

* `YSelectDict` 新增搜索过滤数据功能，通过 `filterabled` 属性开启搜索过滤框，
* 通过配置 `dict-type` 属性设置字典数据

### 操作步骤

组件基于 `@vue/cli 4.0+` 进行开发，请配合框架一起使用。

#### 1. 下载组件

``` bash
# 在项目终端中输入指令进行下载组件
# 方法一：
vue add @ylz/vue-cli-plugin-pc-template-y-select-dict --registry http://ued.edtsoft.com 

# 方法二：采用 yarn。 复制下面全部指令，回车根据提示进行安装
yarn add -D @ylz/vue-cli-plugin-pc-template-y-select-dict --registry http://ued.edtsoft.com
vue invoke @ylz/vue-cli-plugin-pc-template-y-select-dict
```

#### 2. 组件目录结构

``` bash
# 执行下载命令后，往项目中自动添加的组件目录格式如下：
.
└── src
    └── components
        └── global
            └── y-select-dict
                └── index.vue
```

#### 3. 使用方式

可直接复制对应标题下的代码进行使用。

#### 4. 依赖安装问题

* [插件包安装常见问题](http://wxfwtest.ylzms.com/ylzued/medical-component/#/home#chang-jian-wen-ti)

## 代码演示

### 基础应用

``` HTML
<template>
  <y-select-dict v-model="select" dict-type="sex" label="性别" is-link />
</template>
<script>
  export default {
    data() {
      return {
        select: ''
      }
    },
  }
</script>
```

### 无搜索框

``` HTML
<template>
  <y-select-dict v-model="select" dict-type="sex" label="性别" is-link :filterabled="false" />
</template>
<script>
  export default {
    data() {
      return {
        select: ''
      }
    },
  }
</script>
```

### 自定义接口/格式化数据

``` HTML
<template>
  <y-select-dict v-model="select" dict-type="sex" label="性别" is-link :formatter="formatter" :getCodeApi="getCodeApi" />
</template>
<script>
  import jsonToArray from "@/utils/common"
  export default {
    data() {
      return {
        select: ''
      }
    },
    methods: {
      formatter(data) {
        // 数据格式转换
        // sex:{ '0':'男0号 Api', '1':'男1号 Api' } =>>>
        // sex:[{ value:'0',label:'男0号 Api' },{ value:'1',label:'男1号 Api' }]
        Object.keys(data).map((item) => {
          data[item] = jsonToArray(data[item])
        })
        return data
      },

      getCodeApi(params) {
        // 指定字典数据 api
        const sex = {}
        for (let index = 0; index < 200; index++) {
          sex[`${index}`] = `序号 ${index}`
        }

        return new Promise((resolve, reject) => {
          resolve({
            code: 0,
            data: {
              sex2: sex
            }
          })
        })
      }
    }
  }
</script>
```

### YAutoForm组件基础应用

``` HTML
<template>
  <y-auto-form v-model="form" :formItemList="formItemList" />
</template>
<script>
  export default {
    data() {
      return {
        form: {},
        formItemList: [{
          type: "selectDict",
          prop: "sex4",
          label: "性别",
          dictType: "sex",
          isLink: true,
          placeholder: "请选择",
          inputAlign: "right"
        }],
      }
    },
  }
</script>
```

### YAutoForm组件自定义接口/格式化数据

``` HTML
<template>
  <y-auto-form v-model="form" :formItemList="formItemList" />
</template>
<script>
  import jsonToArray from "@/utils/common"
  export default {
    data() {
      return {
        form: {},
        formItemList: [{
          type: "selectDict",
          prop: "sex5",
          label: "性别",
          dictType: "sex5",
          isLink: true,
          formatter: (data) => {
            // 数据格式转换
            // sex:{ '0':'男0号 Api', '1':'男1号 Api' } =>>>
            // sex:[{ value:'0',label:'男0号 Api' },{ value:'1',label:'男1号 Api' }]
            Object.keys(data).map((item) => {
              data[item] = jsonToArray(data[item])
            })
            return data
          },
          getCodeApi: () => {
            // 指定字典数据 api
            const sex = {}
            for (let index = 0; index < 200; index++) {
              sex[`${index}`] = `YAutoForm ${index}`
            }

            return new Promise((resolve, reject) => {
              resolve({
                code: 0,
                data: {
                  sex5: sex
                }
              })
            })
          },
          placeholder: "请选择",
          inputAlign: "right"
        }],
      }
    },
  }
</script>
```

### 传递额外参数

如果要传递额外参数可以给 `dict-type` 绑定一个对象， `type` 字段为字典类型（必填），其余参数都写在该对象中

``` json
{
  type: "sex", // 字典类型，必填
  keyword: '男' // 额外参数，选填
}
```

``` HTML
<y-select-dict v-model="select" :dict-type="dictType" label="性别" is-link />
```

``` JS
export default {
  data() {
    return {
      select: '',
      dictType: {
        type: "sex", // 字典类型
        keyword: "男" // 额外参数
      }
    }
  },
}
```

## API

### Props

| 参数                     | 说明                                         | 类型               | 默认值     |
| ------------------------ | ------------------------------------------- | ------------------ | ---------- |
| v-model (value)          | 当前输入的值 | _number \| string_ | -          |
| label                    | 输入框左侧文本 | _string_           | -          |
| dict-type `extend` | 字典类型，</br> 当输入值为 `string` 时默认转换为 `{ type: dictType }` </br> 当输入值为 `object` 时 `{ type: dictType, ...otherParams }` | _string/object_           | -          |
| filterabled `extend` | 开启过滤搜索 | _boolean_          | -          |
| formatter `extend` | 格式化数据回调函数, `(data:any,params:object):any` | _function_          | -          |
| getCodeApi `extend` | 数据字典请求api函数, `(types:object):promise` | _function_          | -          |
| name `v2.5.0` | 名称，提交表单的标识符 | _string_           | -          |
| size                     | 大小，可选值为 `large` | _string_           | -          |
| maxlength                | 输入的最大字符数 | _number \| string_ | -          |
| placeholder              | 输入框占位提示文字 | _string_           | -          |
| border                   | 是否显示内边框 | _boolean_          | `true` |
| disabled                 | 是否禁用输入框 | _boolean_          | `false` |
| colon `v2.7.2` | 是否在 label 后面添加冒号 | _boolean_          | `false` |
| required                 | 是否显示表单必填星号 | _boolean_          | `false` |
| center                   | 是否使内容垂直居中 | _boolean_          | `false` |
| clear-trigger `v2.9.1` | 显示清除图标的时机， `always` 表示输入框不为空时展示，<br> `focus` 表示输入框聚焦且不为空时展示 | _string_ | `focus` |
| clickable                | 是否开启点击反馈 | _boolean_          | `false` |
| is-link                  | 是否展示右侧箭头并开启点击反馈 | _boolean_          | `false` |
| autofocus                | 是否自动聚焦，iOS 系统不支持该属性 | _boolean_          | `false` |
| error                    | 是否将输入内容标红 | _boolean_          | `false` |
| error-message            | 底部错误提示文案，为空时不展示 | _string_           | -          |
| format-trigger `v2.8.7` | 格式化函数触发的时机，可选值为 `onBlur` | _string_           | `onChange` |
| arrow-direction `v2.0.4` | 箭头方向，可选值为 `left`  `up`  `down` | _string_           | `right` |
| label-class              | 左侧文本额外类名 | _any_              | -          |
| label-width              | 左侧文本宽度，默认单位为 `px` | _number \| string_ | `6.2em` |
| label-align              | 左侧文本对齐方式，可选值为 `center`  `right` | _string_           | `left` |
| input-align              | 输入框对齐方式，可选值为 `center`  `right` | _string_           | `left` |
| error-message-align      | 错误提示文案对齐方式，可选值为 `center`  `right` | _string_           | `left` |
| left-icon                | 左侧[图标名称](#/zh-CN/icon)或图片链接 | _string_           | -          |
| right-icon               | 右侧[图标名称](#/zh-CN/icon)或图片链接 | _string_           | -          |
| icon-prefix `v2.5.3` | 图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props) | _string_           | `van-icon` |
| rules `v2.5.0` | 表单校验规则，详见 [Form 组件](#/zh-CN/form#rule-shu-ju-jie-gou) | _Rule[]_           | -          |

### Events

除下列事件外，Field 默认支持 Input 标签所有的原生事件

| 事件                 | 说明                 | 回调参数                       |
| -------------------- | -------------------- | ------------------------------ |
| input                | 输入框内容变化时触发 | _value: string (当前输入的值)_ |
| focus                | 输入框获得焦点时触发 | _event: Event_                 |
| blur                 | 输入框失去焦点时触发 | _event: Event_                 |
| clear                | 点击清除按钮时触发   | _event: Event_                 |
| click                | 点击 Field 时触发    | _event: Event_                 |
| click-input `v2.8.1` | 点击输入区域时触发   | _event: Event_                 |
| click-left-icon      | 点击左侧图标时触发   | _event: Event_                 |
| click-right-icon     | 点击右侧图标时触发   | _event: Event_                 |

### 方法

通过 ref 可以获取到 Field 实例并调用实例方法，详见[组件实例方法](#/zh-CN/quickstart#zu-jian-shi-li-fang-fa)

| 方法名 | 说明           | 参数 | 返回值 |
| ------ | -------------- | ---- | ------ |
| focus  | 获取输入框焦点 | -    | -      |
| blur   | 取消输入框焦点 | -    | -      |
