<template>
  <div class="home">
    <div class="home-container">
      <img src="@pic/home/<USER>/robot-hi.png" class="logo" />
      <p>厦门人社智慧小新</p>
      <div class="home-enter-btn" @click="$emit('enter')">{{ loginStatus === "fail" ? "重新进入" : "进入" }}</div>
    </div>
    <p class="copyright-notice">Copyright © 2025 厦门市人力资源和社会保障局. All Rights Reserved<br>技术支持：易联众·保睿通</p>
  </div>
</template>

<script>
export default {
  props: {
    loginStatus: {
      type: String,
      default: ""
    }
  }
}
</script>

<style lang="less" scoped>
.home {
  width: 100%;
  height: 100%;
  background: transparent url("~@pic/home/<USER>/disclaimer.png") no-repeat center / 100% 100%;
  position: relative;

  .home-container {
    position: relative;
    top: 20%;
    text-align: center;

    .logo {
      width: 150px;
      left: 138px;
      position: relative;
      left: 25px;
    }

    p {
      text-align: center;
      font-size: 26px;
      font-weight: 700;
      letter-spacing: 2px;
      background: linear-gradient(90deg, #4eb5ff 0%, #6ee2f5 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 2px 8px rgba(78,181,255,0.25), 0 1px 0 #fff;
      margin-top: 18px;
      margin-bottom: 0;
    }

    .home-enter-btn {
      position: relative;
      bottom: -45px;
      width: 145px;
      padding: 9px 30px;
      border-radius: 25px;
      color: #fff;
      font-size: 16px;
      background:
        radial-gradient(circle at 10% 10%, #E8F9FF 0%, #00D8FF 40%),
        #00D8FF;
      border: 0.5px solid #E0FBFF;
      box-shadow:
        0px 0px 9.36px 0px #d9f0ff inset,
        0px 0px 8px 0px #00a3e4 inset,
        0px 20px 30px 0px rgba(34,161,203,0.20),
        0px 8px 15px 0px rgba(22,107,177,0.30);
      user-select: none;
      display: inline-block;
      transition: box-shadow 0.2s;
    }
  }

  .copyright-notice {
    text-align: center;
    position: absolute;
    bottom: 20px;
    font-size: 10px;
    width: 100%;
  }
}
</style>