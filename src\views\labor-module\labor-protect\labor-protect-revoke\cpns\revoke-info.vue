<!--
 * @Description: 劳动维权--撤回申请
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="revoke-box">    
    
    <van-form class="base-form" @failed="onFailed" @submit="handleNext">
      <y-title content="撤回申请"/>
      <van-cell-group inset>

        <van-field
          v-model="formData.aac003"
          name="aac003"
          label="姓名"
          placeholder="请输入"
          disabled
        />

        <van-field
          v-model="formData.aac002"
          name="aac002"
          label="证件号码"
          placeholder="请输入"
          disabled
        />

        <y-select-dict class="revoke-select-dict" v-model="formData.aac004" :filterabled="false"
        dict-type="AAC004" label="性别" is-link disabled />

        <van-field                   
          v-model="formData.aac006"
          name="aac006"
          label="年龄"
          placeholder="请输入"
          disabled
        />

        <van-field
          v-model="formData.aae005"
          name="aae005"
          label="联系电话"
          placeholder="请输入"
        />   

        <van-field
          v-model="formData.tjsj00"
          name="tjsj00"
          label="申请时间"
          :formatter="formatterTime"
          placeholder="请输入"
          disabled
        />     

        <y-select-dict v-model="formData.aab299" :rules="formRules.aab299" dict-type="AAB299"
        label="户籍所在地" is-link />

        <van-field
          v-model="formData.aab300"
          name="aab300"
          label="户籍详细地址"
          placeholder="请输入"
          :rules="formRules.aab300"
        />  

        <y-select-dict v-model="formData.aab302" :rules="formRules.aab302" dict-type="AAB299"
        label="现居住地" is-link />
        
        <van-field
          class="laber-wider van-cell-border-bottom"
          v-model="formData.aab303"
          name="aab303"
          label="现居住地详细地址"
          placeholder="请输入"
          :rules="formRules.aab303"
        />       

        <div class="separate-box"></div>

        <van-field          
          v-model="formData.aab004"
          name="aab004"
          label="被申请人单位"
          placeholder="请输入"
          disabled
        />

        <van-field
          v-model="formData.aae007"
          name="aae007"
          label="单位电话"
          placeholder="请输入"
        />

        <van-field
          class="van-cell-border-bottom"
          v-model="formData.aae008"
          name="aae008"
          label="单位地址"
          placeholder="请输入"
        />
      <div class="separate-box"></div>
        
      </van-cell-group>

      <y-title content="撤回理由" />
      <van-cell-group inset >
        
        <van-field
          v-if="isRevoke === '1'"
          v-model="formData.aab277"
          name="aab277"
          label="撤回时间"
          placeholder="请选择"          
          readonly
        />

        <van-field
          class="van-field-textarea"
          v-model="formData.aae009"
          name="aae009"
          label="撤回理由"
          placeholder="请输入撤回理由"
          type="textarea"
          :required="isRevoke !== '1'"
          :rules="formRules.aae009"
          :disabled="isRevoke === '1'"
        />

      </van-cell-group>
      
      <div class="button-box mt18" :class="{'button-box-only': isRevoke === '1'}">
        <van-button plain type="info" @click="handleCancle" native-type="button">
          取 消
        </van-button>
        <van-button v-if="isRevoke !== '1'" round block type="primary" native-type="submit">
          下一步
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
export default {
  name: "revoke-info",
  model: {
    prop: "formData"
  },
  props: {
    formData: {
      type: Object,
      require: true
    }
  },
  data() {
    return {
      formRules: {
        aae009: [{ required: true, message: "请输入" }]
      }
    }
  },
  computed: {
    isRevoke() {
      return this.$route.query.sfch00
    }
  },
  
  methods: { 
    formatterTime(time) {
      return time.substring(0, 10)
    },   
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },
    // 取消
    handleCancle() {
      this.$router.go(-1)
    },
    // 下一步
    handleNext() {
      this.$emit("handleNext", 1)
    }
  }
}
</script>

<style lang="less" scoped>
.revoke-box {
  overflow-x: hidden;
  .base-form {
    padding: 16px;
    .separate-box {
      position: relative;
      width: 120%;
      margin-left: -10%;
    }
    .button-box {
      padding: 0;
      background-color: @white_text_color;
    }
  }
}

</style>