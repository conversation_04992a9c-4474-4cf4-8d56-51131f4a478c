<!--
 * @Description: 地图服务
 * @Author: hwx
 * @date: 2024/9/4 11:29
 * @LastEditors: hwx <EMAIL>
-->
<template>
  <div class="love-station">
    <!-- 搜索 -->
    <div class="search-box flex-c-c">
      <img class="search-icon" src="@pic/life-service/<EMAIL>" alt="" />
      <van-field
        v-model="input"
        input-align="left"
        placeholder="请输入名称"
        :border="false"
        clearable
      />
      <span class="search-btn" @click="handleSearch">搜索</span>
    </div>

    <!-- 地图 -->
    <div :class="['map-container', showPopup ? 'map-container-short' : 'map-container-long']" id="map"></div>

    <!-- 弹窗 -->
    <van-popup
      :class="['info-popup', showPopup ? '' : 'info-popup-hide']"
      v-model="showPopup"
      position="bottom"
      :round="true"
      :overlay="true"
      :lock-scroll="false"
      :close-on-click-overlay="false"
    >
      <div class="title-box flex-c-c">
        <div
          :class="['title-box-left','title-box-active',]"
        >
          <span>{{outletsName}}网点</span>
          <van-icon @click="closePopup" class="close-icon" name="close" />
        </div>
      </div>

      <div v-if="stageList.length > 0" class="list-box">
        <div class="item">
          <div class="item-top">
            <div class="item-top-left">
              <p class="name">{{ stageData.yzmc00 }}</p>
              <p class="text">网点地址：{{ stageData.yzdz00 }}</p>
            </div>
            <div class="item-top-right" @click="handleNavigate(stageData)">
              <img src="@pic/life-service/<EMAIL>" alt="" />
              <span>到这去</span>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="list-box">
        <y-empty ></y-empty>
      </div>
    </van-popup>
  </div>
</template>

<script>
/* eslint-disable no-undef */

import { commonApi } from "@/api"
import {cloneDeep} from "lodash"
import {setWxConfig, openLocation} from "@/utils/wechat"

//001环卫爱心驿站，002近邻图书馆，003调解组织，004法律援助，005仲裁机构
const typeMap = {
  "mediate": {
    value: "003",
    label: "调解组织"    
  },
  "law": {
    value: "004",
    label: "法律援助"
  },
  "arbitration": {
    value: "005",
    label: "仲裁机构"
  }
}

export default {
  name: "love-station",
  data() {
    return {
      // 地图相关
      map: null, //地图实例
      markersArray: null, //地图标记点数组
      infoWindow: null, // 信息窗口
      mapKey: process.env.VUE_APP_TMAP_KEY, //map key

      //信息弹窗
      showPopup: false, 

      // 搜索
      input: "",
      pageInfo: {
        page: 1,
        size: 10
      },
      type: "", //查询类型
      daz012: "", //查询ID
      stageList: [], //列表

      // 服务内容
      serviceList: [
        { 
          label: "休息",
          value: "01",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        },
        { 
          label: "饮水",
          value: "02",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        },        
        { 
          label: "充电",
          value: "04",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        },
        { 
          label: "急救药箱",
          value: "05",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        }
      ]
    }
  },
  computed: {
    stageData() {
      return this.stageList?.[0] || {}
    },
    outletsName() {
      return typeMap[this.type]?.label || ""
    }
  },
  async created() {
    setWxConfig() // 微信SDK签名配置

    const {type, daz012} = this.$route.query
    this.type = type
    this.daz012 = daz012
    this.findDa12ByPage() //分页查询
  },
  methods: {
    // 搜索
    handleSearch() {
      this.showPopup = false
      this.findDa12ByPage()
    },
    //分页查询
    async findDa12ByPage() {      
      const ywlx00 = typeMap[this.type]?.value || ""
      const params = {
        serviceName: "xytHwyz_findDa12ByPage",
        yzmc00: this.input,
        ywlx00,
        page: 1,
        size: 10
      }
      const res1 = await commonApi.proxyApi(params)    
      const { total } = res1.map.data
      params.size = total

      const res2 = await commonApi.proxyApi(params)   
      const { rows } = res2.map.data

      // 无数据状态
      if (rows?.length === 0) {
        this.stageList = []
        this.clearOverlays()        
        this.$dialog.alert({
          title: "提示",
          message: "未查询到相关地图服务信息！",
          theme: "round-button"
        })   
        return
      }

      // 网点
      rows.forEach((item, v) => {    
        item = this.formatServiceList(item)        
      })
      this.stageList = rows         

      // 地图描点
      const {zdwd00, zdjd00} = this.stageList[0]
      const lat = zdwd00 && Number(zdwd00)
      const lng = zdjd00 && Number(zdjd00)
      this.reserMap(lat, lng)
    },
    //地图显示
    reserMap(lat, lng, zoom=13) {
      const center = new qq.maps.LatLng(lat, lng)
      const mapContainer = document.getElementById("map")
      this.map = new qq.maps.Map(mapContainer, {
        center: center,
        zoom: zoom, 
        draggable: true,
        mapSign: false // 隐藏右下角图标
      })

      const markersArray = [] //标记点列表
      const iconUrl = `${process.env.VUE_APP_PUBLIC_PATH}map-marker/map-marker.svg`      
      const markerIcon = new qq.maps.MarkerImage( //标记点图标及样式
        iconUrl, // 图片的URL
        new qq.maps.Size(27, 33), // 图标的大小
        new qq.maps.Point(0, 0), // 图标的坐标
        new qq.maps.Point(15, 30) // 图标的锚点
      )
      this.stageList.forEach(item => {        
        const {zdwd00, zdjd00, daz012} = item
        const lat = zdwd00 && Number(zdwd00)
        const lng = zdjd00 && Number(zdjd00)        
        const marker = new qq.maps.Marker({
          id: daz012,
          daz012,
          position: new qq.maps.LatLng(lat, lng),
          map: this.map,
          icon: markerIcon
        })       
        markersArray.push(marker)
        
        qq.maps.event.addListener(marker, "click", this.handleClickMap) // 地图坐标点 点击事件
      })      
      this.markersArray = markersArray //标记点列表
      // 主动触发第一个标记点的点击事件
      if (markersArray.length > 0) {
        const target = markersArray.find(i => i.daz012 == this.daz012) || markersArray[0]        
        this.handleClickMap({ target })
      }
    },
    //清除地图标记点
    clearOverlays() {
      const {markersArray} = this
      if (markersArray?.length > 0) {
        for (const i in markersArray) {          
          markersArray[i].setMap(null)
        }
      }
    },
    // 地图点击事件    
    async handleClickMap(e) {
      // 移除信息窗口
      this.infoWindow && this.infoWindow.setMap(null)

      // 移动到指定坐标点
      const {position, daz012} = e.target
      const newCenter = new qq.maps.LatLng(position.lat, position.lng)
      this.map.panTo(newCenter)
      
      // 网点信息
      const params = {serviceName: "xytHwyz_getDa12ById", daz012}
      const res = await commonApi.proxyApi(params)
      const { data } = res.map         
      const newData = this.formatServiceList(data)   
      this.stageList = [{...newData}]
      this.showPopup = true        

      // 信息窗口
      this.infoWindow = new qq.maps.InfoWindow({
        map: this.map,
        position: new qq.maps.LatLng(position.lat, position.lng),
        offset: { x: 0, y: -32 }, //设置信息窗相对position偏移像素，为了使其显示在Marker的上方
        enableCustom: true,
        maxHeight: 40,
        maxWidth: 230,
        minHeight: 0,
        minWidth: 0
      })      
      this.infoWindow.open() //打开信息窗
      this.infoWindow.setPosition(position)//设置信息窗位置
      
      this.infoWindow.setContent(`<div class="ref-card">${newData.yzmc00}</div>`)//设置信息窗内容 
      setTimeout(() => { //设置字体样式
        document.querySelector(".ref-card").style="font-size: 14px; color: #333333;"
      }, 100)           
    },
    // 格式化服务内容数据(目前未用到 先保留)
    formatServiceList(data) {
      const list = data?.fwnr00?.split(";") || []
      const serviceList = cloneDeep(this.serviceList)        
      data.serviceList = serviceList.map((item) => {
        return {...item, imgUrl: require(`@/assets/imgs/life-service/service${item.value}${list.includes(item.value) ? "-active" : ""}@2x.png`)}
      })
      return data
    },
    // 关闭弹窗
    closePopup() {
      this.infoWindow && this.infoWindow.setMap(null)
      this.showPopup = false
    },
    // 到这去
    handleNavigate(item) {      
      const {zdwd00: lat, zdjd00: lng, yzdz00: address, yzmc00: name} = item      
      openLocation({lat, lng, address, name}) //微信查看地图SDK
    }
  }
}
</script>

<style lang="less" scoped>
.love-station {
  background: #4c4c4c;
  position: relative;
  .search-box {
    position: absolute;
    width: 305px;
    height: 36px;
    background: #ffffff;
    border-radius: 18px;
    top: 16px;
    left: 50%;
    transform: translate(-50%);
    z-index: 99999;
    padding: 0 16px;
    .search-icon {
      width: 16px;
      height: 14px;
    }
    .van-field {
      flex: 1;
      padding: 0 10px;
      /deep/.van-icon-clear {
        font-size: 14px;
      }
      /deep/.van-field__control::-webkit-input-placeholder {
        font-size: 14px;
      }
    }
    .search-btn {
      font-size: 14px;
      color: #bd1a2d;
      display: inline-block;
      height: 100%;
      line-height: 36px;
    }
  }
  .map-container {
    width: 100%;
    &-short {
      height: calc(100vh - 118px);
    }
    &-long {
      height: calc(100vh);
    }
  }
  ::v-deep .map-container {
    & > div > div:nth-child(2) {
      display: none;
    }
    .smnoprint {
      display: none;
    }
  }
  .info-popup {    
    overflow: hidden;
    &-hide {
      display: none;
    }
    .title-box {
      width: 100%;
      height: 44px;
      font-size: 16px;
      color: #666666;
      line-height: 22px;
      text-align: center;
      line-height: 44px;
      &-left {
        flex: 1;
        height: 100%;
        position: relative;
        .close-icon {
          position: absolute;
          right: 8px;
          top: 8px; 
          color: #666666;
        }
      }
      &-right {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        position: relative;
        .van-icon {
          font-size: 14px;
          margin-left: 4px;
          transform: rotate(-90deg);
          transition: all 0.7s;
          padding-bottom: 2px;
        }
        .location-list {
          width: 160px;
          background: #fff;
          border-radius: 4px;
          z-index: 99999;
          box-shadow: 0px -1px 8px 0px rgba(186, 186, 186, 0.32);
          height: 270px;
          overflow: scroll;
          position: absolute;
          top: 44px;
          left: 50%;
          transform: translate(-50%);
          & > p {
            color: #666666;
          }
          .active {
            color: #bd1a2d;  
          }
        }
      }
      &-active {
        color: #bd1a2d;        
      }
      .icon-active {
        .van-icon {
          font-size: 14px;
          margin-left: 4px;
          transform: rotate(90deg);
          transition: all 0.7s;
        }
      }
    }
    .content-title {
      width: 100%;
      line-height: 44px;
      text-align: center;
      font-weight: bold;
      font-size: 16px;
      color: #bd1a2d;
      border-bottom: 1px solid #ebebeb;
    }
    .list-box {
      overflow: auto;
      .item {
        padding: 8px 16px 16px;
        &:not(:last-child) {
          border-bottom: 1px solid #ebebeb;
        }
        &-top {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          &-left {
            flex: 1;
            .name {
              font-weight: bold;
              font-size: 16px;
              color: #333333;
              line-height: 22px;
              margin-bottom: 6px;
            }
            .text {
              font-size: 12px;
              color: #666666;
              line-height: 18px;
            }
            .hour-icon {
              margin-top: 10px;
              & > img {
                width: 13px;
                height: 14px;
              }
              & > span {
                font-size: 12px;
                color: #999999;
                line-height: 18px;
                margin-left: 4px;
              }
            }
          }
          &-right {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-left: 12px;
            & > img {
              width: 32px;
              height: 32px;
            }
            & > span {
              margin-top: 4px;
              font-size: 10px;
              color: #999999;
              line-height: 14px;
              text-align: center;
            }
          }
        }
        &-bottom {
          .service-title {
            font-size: 12px;
            color: #666666;
            line-height: 18px;
            margin-top: 12px;
          }
          .service-list {
            margin-top: 12px;
            .service {
              width: 52px;
              height: 52px;
              border-radius: 4px;
              border: 1px solid #eeeeee;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              &:not(:first-child) {
                margin-left: 10px;
              }
              & > img {
                width: 16px;
                height: 16px;
              }
              & > span {
                font-size: 12px;
                color: #666666;
                line-height: 18px;
                text-align: center;
                margin-top: 2px;
              }
            }
            .other-service {
              font-size: 12px;
              color: #bd1a2d;
              line-height: 18px;
              text-align: left;
            }
          }
        }
      }
    }
  }
  /deep/.van-overlay {
    background: rgba(0, 0, 0, 0);
    z-index: -1 !important;
  }
}
</style>
