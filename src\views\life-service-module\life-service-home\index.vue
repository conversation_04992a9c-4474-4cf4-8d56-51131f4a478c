<!--
 * @Description: 生活服务
 * @Author: hwx
 * @date: 2024/9/4 11:29
 * @LastEditors: hwx
-->
<template>
  <div class="trade-union-service-container">
    <div class="header-box">
      <img :src="require('@pic/life-service/<EMAIL>')" alt="">
    </div>
    <div class="trade-union-service-container">
      <div class="business-container">
        <y-title content="服务地图" fontWeight="bold" font-cont-size="14" />
        <div class="business-box flex-c-sb mt16">
          <div class="business-item flex-c-c-c mb16" v-for="(item,index) in businessList" :key="index" @click="handleJump(item)">
            <img :src="item.imgUrl" alt="">
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="trade-union-service-container trade-union-service-end">
      <div class="business-container">
        <y-title content="其他服务" fontWeight="bold" font-cont-size="14" />
        <div class="business-box flex-c-s mt16">
          <div class="business-item flex-c-c-c" v-for="(item,index) in businessList2" :key="index" @click="handleJump(item)">
            <img :src="require('@pic/labor-protect/<EMAIL>')" alt="">
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import {isValidUrl} from "@/utils/str-util"

export default {
  name: "life-service-home",

  data() {
    return {
      businessList: [
        {
          imgUrl: require("@pic/trade-union-service/6.png"),
          title: "总览",
          path: "https://weixin.xmgh.org/xmzgh/m/gh/services/map.html?type=0"
        },
        {
          imgUrl: require("@pic/trade-union-service/2.png"),
          title: "服务地点",
          path: "https://weixin.xmgh.org/xmzgh/m/gh/services/map.html?type=4"
        },
        {
          imgUrl: require("@pic/trade-union-service/8.png"),
          title: "工会爱心驿站",
          path: "https://weixin.xmgh.org/xmzgh/m/gh/services/map.html?type=1"
        },
        {
          imgUrl: require("@pic/trade-union-service/5.png"),
          title: "福建爱心驿站",
          path: "https://weixin.xmgh.org/xmzgh/m/gh/services/map.html?type=3"
        },
        {
          imgUrl: require("@pic/trade-union-service/1.png"),
          title: "特惠商家",
          path: "https://weixin.xmgh.org/xmzgh/m/gh/services/map.html?type=2"
        },
        {
          imgUrl: require("@pic/trade-union-service/7.png"),
          title: "共享职工之家",
          path: "https://weixin.xmgh.org/xmzgh/m/gh/services/map.html?type=5"
        },
        {
          imgUrl: require("@pic/trade-union-service/7.png"),
          title: "环卫爱心驿站",
          path: "/love-station"
        }
      ],
      businessList2: [
        // {
        //   imgUrl: require("@pic/trade-union-service/3.png"),
        //   title: "职工书屋",
        //   path: "https://content.dzzgsw.com/weixin/visit.html?appid=xmszgh"
        // },
        {
          imgUrl: require("@pic/trade-union-service/4.png"),
          title: "职工学堂",
          path: "https://i.xmgh.org/jyh5/home/<USER>"
        }
      ]
    }
  },
  methods: {
    handleJump(item) {
      if (!item.path){
        this.$toast("功能建设中，敬请期待!")
        return
      }

      if (isValidUrl(item.path)){
        window.open(item.path)
      } else {
        this.$router.push(item.path)
      }
    }
  }
}
</script>

<style scoped lang="less">
.trade-union-service-container{
  background-color: #F6F6F6;
  .header-box {
    width: 100%;
    & > img {
      width: 100%;
    }
  }
  .trade-union-service-container {
    padding: 24px 16px;
    .business-container {
      background-color: @white_bg_color;
      padding: 8px 18px 6px;
      border-radius: 4px;
      .business-box .business-item {
        min-width: 33%;
        & > img {
          width: 44px;
        }
        .item-title {
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
          margin-top: 12px;
          text-align: center;
        }
      }
    }
  }
  .trade-union-service-end{
    padding-top: 0;
    .business-container{
      .business-box{
        padding-bottom: 8px;
      }
    }
  }
}
</style>