.animBig {
  animation: anim<PERSON>ig 0.8s linear 0s infinite;
  -webkit-animation: anim<PERSON>ig 0.8s linear 0s infinite;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.4s;
}
.fade-enter,
.fade-leave-active {
  opacity: 0;
}
.dialog-enter-active,
.dialog-leave-active {
  transition: all 0.4s;
}
.dialog-enter,
.dialog-leave-active {
  transform: translate3d(0, -100px, 0) scale(0.9);
  opacity: 0;
}
.slide-enter-active,
.slide-leave-active {
  transition: all 0.7s;
}
.slide-enter,
.slide-leave-active {
  opacity: 0;
}

@keyframes animBig {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.anim {
  -webkit-animation: tranColor 2s infinite;
  animation: tranColor 2s infinite;
}
@-webkit-keyframes tranColor {
  from,
  50%,
  to {
    opacity: 1;
  }
  25%,
  75% {
    opacity: 0.5;
  }
}
@keyframes tranColor {
  from,
  50%,
  to {
    opacity: 1;
  }
  25%,
  75% {
    opacity: 0.5;
  }
}
