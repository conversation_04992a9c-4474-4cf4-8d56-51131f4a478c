/**
 * @Description: axios加密封装
 * @Author: 吕志伟 <EMAIL>
 * @Date: 2025-06-24 17:00:00
 */

import Axios from "axios"
import { decryptResponseData, encryptRequestData, generateRandomAESKey, isResponseEncrypted} from "@/utils/crypto.js"
import { getYlzinsToken } from "@/utils/cookie"

// 环境变量
const service = Axios.create({
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
    "Authorization": "Bearer " + process.env.VUE_APP_AUTHORIZATION
  }
})

// 请求拦截器
service.interceptors.request.use(config => {
  const YlzinsToken = getYlzinsToken()
  if (YlzinsToken) {
    config.headers["ylzins-token"] = YlzinsToken
  }

  // 清理 get 请求的 params
  if (config.method && config.method.toLowerCase() === "get" && config.params && typeof config.params === "object") {
    Object.keys(config.params).forEach(key => {
      const value = config.params[key]
      if (value === null || value === undefined || value === "") {
        delete config.params[key]
      }
    })
  }

  // 清理 post 请求的 data
  if (config.method && config.method.toLowerCase() === "post" && config.data && typeof config.data === "object" && !Array.isArray(config.data)) {
    Object.keys(config.data).forEach(key => {
      const value = config.data[key]
      if (value === null || value === undefined || value === "") {
        delete config.data[key]
      }
    })
  }

  // 检查加密开关
  if (config?.encryptEnabled || config?.options?.encryptEnabled) {
    // 添加请求加密
    try {
      // 获取或生成AES密钥（存储在sessionStorage中）
      const aesKey = generateRandomAESKey()
      encryptRequestData(config, aesKey)
    } catch (error) {
      console.warn("加密请求数据失败:", error)
    }
  }
  return config
}, error => {
  return Promise.reject(error)
})

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    console.log("response", response)
    // 检查加密开关和响应头
    if (response?.headers["x-encrypted"] == "true" && isResponseEncrypted(response)) {
      try {
        const decryptedData = decryptResponseData(response)
        response.data = decryptedData
      } catch (error) {
        console.warn("解密响应数据失败:", error)
        // 解密失败时保持原始数据
      }
    }
    return response
  },
  (error) => {
    // 响应错误处理
    console.error("请求响应错误:", error)
    return Promise.reject(error)
  }
)

export default service