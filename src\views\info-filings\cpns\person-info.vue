<!--
 * @Description: 人员信息--步骤
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="person-info">
    <!-- 表单信息 -->
    <van-form ref="baseForm" class="base-form" :disabled="!required" @submit="handleNext" @failed="onFailed">
      <van-cell-group inset>

        <van-field
          v-model="formData.aac003"
          name="aac003"
          label="姓名"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aac003"
          disabled
        />

        <y-select-dict v-model="formData.ccg981" :filterabled="false" :disabled="!required"
        :rules="formRules.ccg981"
        dict-type="CCG981" label="证件类型" is-link />

        <van-field
          v-model="formData.aac002"
          name="aac002"
          label="证件号码"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aac002"
          disabled
        />

        <y-select-dict v-model="formData.aac004" :filterabled="false" :disabled="!required || isReadonly"
        :rules="formRules.aac004"
        dict-type="AAC004" label="性别" is-link />

        <van-field
          v-model="formData.aac006"
          name="aac006"
          label="出生日期"
          placeholder="请选择"
          :required="required"
          :rules="formRules.aac006"
          :disabled="isReadonly"
          @click="handleSelectDate('aac006')"
        />

        <van-field
          v-model="formData.aae005"
          name="aae005"
          label="联系电话"
          placeholder="请输入"
          :required="required"
          :rules="formRules.aae005"
        />

        <y-select-dict v-model="formData.aac011" :rules="formRules.aac011" dict-type="AAC011" label="文化程度" :disabled="!required"  is-link />

        <y-select-dict v-model="formData.ccc010" :rules="fillEducation ? formRules.ccc010 : []" dict-type="CCC010" label="毕业学校" :disabled="!required"  is-link />

        <y-select-dict v-model="formData.aac183" :rules="fillEducation ? formRules.aac183 : []" dict-type="AAC183" label="毕业专业" :disabled="!required"  is-link />

        <van-field
          v-model="formData.ccd027"
          name="ccd027"
          label="毕业时间"
          placeholder="请选择"
          :required="!!fillEducation && businessType === 'business'"
          :rules="fillEducation ? formRules.ccd027 : []"
          :readonly="true"
          @click="handleSelectDate('ccd027')"
        />

        <y-cascader-area
          v-model="formData.aab299"
          :rules="formRules.aab299"
          label="户籍所在地"
          :disabled="!required"          
        />

        <van-field
          v-model="formData.ccd032"
          name="ccd032"
          label="户籍地详址"
          placeholder="请输入"
          :required="required"
          :rules="formRules.ccd032"
        />

      </van-cell-group>

      <!-- 底部按钮 -->
      <div class="button-box" v-if="showBtn">
        <van-button @click="handleBack" plain type="info" native-type="button">
          返 回
        </van-button>
        <van-button round block type="primary" native-type="submit">
          下一步
        </van-button>
      </div>
    </van-form>

    <!-- 日期选择弹出层 -->
    <van-popup v-model="pickerShow" round position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        title="选择日期"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="handleConfirmPicker"
        @cancel="pickerShow = false"
        />
    </van-popup>
  </div>
</template>

<script>
import {validateIdCard, checkMobile} from "@utils/check"
import {getAge} from "@/utils/common"
import YCascaderArea from "@/components/y-cascader-area"

export default {
  name: "person-info",
  components: {
    YCascaderArea
  },
  model: {
    prop: "formData"
  },
  props: {
    formData: {
      type: Object,
      required: true
    },
    businessType: { //业务页面business 详情页面details
      type: String,
      default: "business"
    },
    showBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      formRulesInfo: {
        aac003: [{ required: true, message: "请输入" }],
        ccg981: [{ required: true, message: "请选择" }],
        aac002: [
          { required: true, message: "请输入" },
          {
            validator: validateIdCard,
            message: "请输入正确身份证号码",
            trigger: "onBlur"
          }
        ],
        aac004: [{ required: true, message: "请选择" }],
        aac006: [{ required: true, message: "请选择" }],
        aae005: [
          { required: true, message: "请输入" },
          {
            validator: checkMobile,
            message: "请输入正确的手机号",
            trigger: "onBlur"
          }
        ],
        aab299: [{ required: true, message: "请选择" }],
        ccd032: [{ required: true, message: "请输入" }],
        aac011: [{ required: true, message: "请选择" }],
        ccc010: [{ required: true, message: "请选择" }],
        aac183: [{ required: true, message: "请选择" }],
        ccd027: [{ required: true, message: "请选择" }]
      },
      fillEducation: false,

      pickerShow: false,
      currentDate: new Date(),
      minDate: new Date(new Date().getFullYear() - 120, 1, 1),
      maxDate: new Date(new Date().getFullYear() + 120, 1, 1),

      pickerType: ""
    }
  },
  watch: {
    "formData.aac011": {
      handler(val) {
        this.fillEducation = val && Number(val) <= 31 //技工学校及以上时 学校信息必填
      },
      immediate: true
    }
  },
  computed: {
    required() {
      return this.businessType === "business"
    },
    isReadonly() {
      return this.formData.ccg981 === "001"
    },
    formRules() {
      return this.businessType === "business" ? this.formRulesInfo : {}
    },
    maxDateValue() {
      return this.dayFormatFn(new Date(), "date")
    }
  },
  methods: {
    // 选择日期
    handleConfirmPicker(val) {
      if (this.pickerType === "aac006") { //出生日期
        const birthdate = this.dayFormatFn(val, "date")
        if (getAge(birthdate) < 16) {
          this.$toast("当前年龄不能小于16周岁！")
          this.formData.aac006=""
        } else {
          this.formData.aac006=this.dayFormatFn(val, "other")
        }
      } else { //毕业时间
        this.formData[this.pickerType] = this.dayFormatFn(val, "other")
      }

      this.pickerShow = false
      this.currentDate = new Date()
    },
    // 点击选择日期
    handleSelectDate(type) {
      this.maxDate = new Date()

      this.pickerShow = true
      this.pickerType = type
    },
    formatDate(date) {
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
    },
    // 返回
    handleBack() {
      this.$router.go(-1)
    },
    // 表单校验失败
    onFailed() {
      this.$toast("请完善表单信息！")
    },
    // 下一步
    handleNext() {
      this.$emit("handleNext", 1)
    }
  }
}
</script>

<style lang='less' scoped>

</style>

