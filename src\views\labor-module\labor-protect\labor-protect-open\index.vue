<!--
 * @Description: 在线调解页面
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-protect">
    <div class="header-box">
      <img :src="require('@pic/labor-protect/<EMAIL>')" alt="">
    </div>
    <div class="info-container">
      <div class="business-container">
      </div>
    </div>
  </div>
</template>

<script>
// import DingRTC from "dingrtc"
export default {
  name: "labor-open",

  data() {
    return {
      
    }
  },
  mounted(){
    const { channelId, userId, userName } = this.$route.query
    this.openVideo(channelId, userId, userName)
  },
  methods: {
    // async openVideo(channel, userId, userName){
    //   const client = DingRTC.createClient()
    //   await client.join({
    //     appId: "p8u4hz9e",
    //     token: "000eJxjYGBQsPncI/7kZRnT5rzy1P9Z3Fqrzu46WH/14oLzFQfPW68KkWFgYOAosCg1yaiyTE2zXPjMtGX1FRANFGczNDI2MjSGskxMjRgYGUbBEAIAG7YdfQ==",
    //     uid: userId,
    //     channel: channel,
    //     userName: userName
    //   })
    // }
  }
}
</script>

<style lang="less" scoped>

</style>