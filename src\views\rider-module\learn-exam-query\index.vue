<!--
 * @Description: 学习与考试情况查询
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <van-tabs v-model="activeName">
      <van-tab name="learn" title="学习情况" />
      <van-tab name="exam" title="考试情况" />
    </van-tabs>

    <!-- 学习情况 -->
    <template v-if="activeName === 'learn' && learnList.length > 0">
      <view-container viewStatus="0" pageSize="mini" v-for="(item, index) in learnList" :key="index">
        <template slot="content">          
          <van-cell-group>
            <van-cell title="姓名" :value="item.aac003" />
            <van-cell title="身份证号码" :value="item.aac002" />
            <van-cell title="学习状态" :value="item.xxzt00" />
            <van-cell title="视频名称" :value="item.qsspmc" />
            <van-cell title="学习开始时间" :value="item.kssj00" />
            <van-cell title="课程总时间" :value="item.kczsj0" />
            <van-cell title="已学习时间" :value="item.xxsj00" />
          </van-cell-group>
        </template>
      </view-container>
    </template>

    <!-- 考试情况 -->
    <template v-if="activeName === 'exam' && examList.length > 0">
      <view-container viewStatus="0" pageSize="mini" v-for="(item, index) in examList" :key="index">
        <template slot="content">
          <van-cell-group>     
            <van-cell title="考试名称" :value="item.title" />
            <van-cell title="开始时间" :value="item.startTime" />
            <van-cell title="考试时长" :value="item.userTime" />
            <van-cell title="及格分数" :value="item.qualifyScore" />
            <van-cell title="总分" :value="item.totalScore" />
            <van-cell title="用户得分" :value="item.userScore" />
          </van-cell-group>
        </template>
      </view-container>
    </template>

    <y-empty v-else></y-empty>
  </div>

</template>

<script>
import {commonApi} from "@/api"
import ViewContainer from "./components/view-container"

export default {
  name: "learn-exam-query",
  components: {
    ViewContainer
  },
  
  data() {
    return {
      activeName: "learn", //学习learn 考试exam
      learnList: [], //学习情况列表
      examList: [], //考试情况列表
      cf05id: "" //安全警示名单记录id
    }
  },
  mounted() {
    const {cf05id, ccf032, examId} = this.$route.query
    this.cf05id = cf05id
    if (ccf032 === "1") { //已完成学习
      this.findXxjlByPage(cf05id) //查询学习记录
    } else { //未完成学习
      this.featchRecord(cf05id)
    }    

    if (!examId) { //考试id为空 未考试
      return
    }
    this.queryPaper(examId) //查询考试记录
  },
  methods: {
    //查询学习记录 已完成学习
    findXxjlByPage(cf05id) {
      commonApi.proxyApi({
        serviceName: "xytQsgl_findXxjlByPage",
        cf05id
      }).then((res) => {
        console.log(res, "查询学习记录")
        const {rows} = res.map.data
        this.learnList = rows
      })
    },
    //查询学习记录 未完成学习
    async featchRecord(cf05id) {
      const res = await commonApi.proxyApi({serviceName: "xytQsgl_getXxjlURL", cf05id})
      console.log(res, "查询学习记录 未完成学习")

      const url = res.msg      
      fetch(url)
        .then(response => {
          console.log("响应数据：", response)
          if (!response.ok) {
            throw new Error("响应失败：" + response.statusText)
          }
          return response.json() // 解析JSON
        })
        .then(data => {
          console.log(data, "处理数据") // 处理数据
          if (data?.length > 0) {
            this.learnList = data.map(item => {
              return {
                aac003: item.StudentName,
                aac002: item.IdentityCode,
                xxzt00: item.StudyStatus,
                kssj00: item.StartTime,
                kczsj0: item.TotalTimes,
                xxsj00: item.PlayedTimes
              }
            })
          }
        })
        .catch(error => {
          console.error("获取学习记录解析报错:", error) // 处理错误
        })
    },

    //查询考试记录
    queryPaper(examId) {
      commonApi.proxyApi({
        serviceName: "xytKsxt_queryPaper",
        examId
      }).then((res) => {
        console.log(res, "查询考试记录")
        const {rows} = res.map.data
        this.examList = rows
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.van-tabs {
  box-shadow: 0 0 0.21333rem 0 #e0e1e6;
  padding-bottom: 12px;
}
</style>