<!--
 * @Description: 投保添加人员
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div>
    <div class="ylb-page-wrapper content" :style="{paddingBottom: `${paddingBottom}px`}">
      <van-form class="base-form" @failed="onFailed" @submit="handleNext">
        <!-- 申请人基本信息 -->
        <y-title content="申请人基本信息" :background-color="ylb_color"  />
        <!-- disabled true: TODO 申请人基本西信息不允许修改(测试改为false)  -->
        <base-info class="mb-8" :disabled="true" key="baseInfo" :form-data="formData.baseInfo" :need-email="true"></base-info>
        
        <!-- 被保人信息 -->
        <y-title content="被保人信息" :background-color="ylb_color" :color-more="ylb_color" more-text="常用联系人" @onMoreCilck="onMoreCilck"></y-title>
        
        <relationship v-model="formData.da02List[0].daa002" :dict-data="dictData.DAA002" @change="restInsuredInfo"></relationship>
        <!-- 被保人基础信息 -->
        <base-info v-if="formData.da02List[0].daa002 !== '001'" :form-data="formData.da02List[0]" key="insuredInfo"></base-info>
        <!-- 被保人其它信息 -->
        <edition-form :form-data="formData.da02List[0]"  :form-rules="formRules"></edition-form>
        
        <!-- 常用联系人 -->
        <conect-radio v-model="formData.da02List[0].aae100" :dict-data="dictData['YES_NO']" class="plr-32"></conect-radio>
        <special-rules></special-rules>
        <!-- 分销员 -->
        <y-title v-if="formData.bxywy.rygh00" content="分销员信息" :background-color="ylb_color"  />
        <distribute-code v-if="formData.bxywy.rygh00" :form-data="formData.bxywy"></distribute-code>
        <y-title content="选择投保时间" :background-color="ylb_color"></y-title>
        <time-picker :form-data.sync="formData.da02List[0]" :required="true" :rules="formRules" :maxDate="maxData" :minDate="minDate"></time-picker>
        <!-- 操作 -->
        <div class="botton-btn ylb-btn" ref="bottomBtn">
            <div class="row">
              <div class="escape-clause">
                本人承诺投保信息的真实性,理解并同意<span class="link">《投保须知及声明》《保险条款》 《理赔指引》《免责告知》</span>的全部内容。
              </div>
            </div>
            <div class="row">
              <div class="cost-box">
            
              </div>
              <van-button round block type="primary" native-type="submit">阅读相关资料</van-button>
            </div>
        </div>
      </van-form>
    </div>
    <!-- 常用联系人 -->
    <ylb-dialog :visible.sync="dialogVisible" title="常用联系人">
      <template>
        <div v-if="connectList&&connectList.length" class="mb-20">
          <card v-for="(item, index) in connectList" :key="index" :title="item.aac003" :id-num="item.aac002" :allow-delete="false" :allow-edit="false" :info="item" @showDetail="showDetail(item)">
            <template #label>
              <PersonalityLabel :label="getRelationshipLable(item.daa002)" color="#FA7E00"></PersonalityLabel>
            </template>
            <div class="row">
              <div class="label" >手机号码：</div>
              <div class="label-content">{{ item.aae005 }}</div>
            </div>
          </card>
        </div>
        <div v-else class="none-data">
          暂无数据，空空如也~
        </div>
      </template>
    </ylb-dialog>
  </div>
</template>

<script>
import DistributeCode from "./components/distribute-code"
import ConectRadio from "./components/conect-radio"
import SpecialRules from "./components/special-rules"
import PersonalityLabel from "@/views/yilubao/components/personality-label"
import BaseInfo from "@/views/yilubao/components/base-info"
import Relationship from "@/views/yilubao/components/relationship"
import Card from "@/views/yilubao/components/card"
import EditionForm from "./components/edition-form/index"
import TimePicker from "@/views/yilubao/components/time-picker"
import YlbDialog from "@/views/yilubao/components/ylb-dialog"
import {ylb_color} from "@/styles/theme/theme-params.less"
import { checkCN } from "@utils/check"

import { commonApi } from "@/api"
import { mapGetters } from "vuex"
import dayjs from "dayjs"
import SessionUtil from "@/utils/session-storage"

export default {
  name: "add-person",
  components: {
    SpecialRules,
    BaseInfo,
    EditionForm,
    Relationship,
    Card,
    PersonalityLabel,
    TimePicker,
    YlbDialog,
    ConectRadio,
    DistributeCode
  },
  data() {

    const checkTime = (str) => {
      if (!str) {
        return false
      }
      const { bxqsrq, bxjzrq } = this.formData.da02List[0]
      if (!bxqsrq || !bxjzrq) {
        return false
      }
      if (!dayjs(bxqsrq).isAfter(dayjs()) || dayjs(bxqsrq).isAfter(dayjs(bxjzrq))) {
        return false
      } 
      
      return true
    }
    // 校验时间跨度
    const checkTimeLength = (str) => {
      if (!str) {
        return false
      }

      const { bxzq00 } = this.formData.da02List[0]
      if (+bxzq00 > 365) {
        return false
      }

      return true
    }

    return {
      minDate: new Date(dayjs().add(1, "day").format("YYYY-MM-DD")),
      maxData: new Date(dayjs("20250913").format("YYYY-MM-DD")),
      ylb_color,
      formRules: {
        aab301: [{ required: true, message: "请选择工作地区" }],
        gzpt00: [{ required: true, message: "请选择工作岗位" }],
        time: [
          {required: true, message: "请选择保险期限"},
          {
            validator: checkTime, message: "请选择合法投保时间段"
          },
          {
            validator: checkTimeLength, message: "投保时长不能超过一年"
          }
        ],
        ygmc00: [
          {
            validator: checkCN, message: "请输入中文用工名称"
          }
        ]
      },
      formData: {
        // 基本信息 - 待处理数据
        baseInfo: {
          aac003: "", // 姓名
          aac002: "", // 证件号码
          ccg981: "", // 证件类型
          aae005: "", // 手机号码
          aae006: "" // 邮箱
        },
        // 投保提交数据
        daa001: "002", // 投保类别 001-企业投保 002-个人投保
        dac001: "", // 投保人证件类型
        dac002: "", // 投保人证件号码
        dac003: "", // 投保人姓名
        aae005: "", // 投保人手机号码
        aae006: "", // 投保人邮箱
        // 分销员信息
        bxywy: {
          aac003: "", // 分销员姓名
          aab004: "", // 公司名称
          rygh00: "" // 分销员工号
        },
        // 被投保人信息
        da02List: [
          {
            daa002: "001", // 与投保人关系
            aac003: "", // 姓名
            daa005: "", // 证件类型
            ccg981: "",
            aac002: "", // 证件号码
            aae005: "", // 手机号码
            aab301: "", // 工作地址
            gzpt00: "", // 工作平台
            ccd032: "", // 工作详细地址
            ygsd00: "00:00-24:00", // 用工时段
            ygmc00: "", // 用工名称
            ywsb00: "", // 有无社保
            bxjzrq: "", // 保险结束日期
            bxqsrq: "", // 保险起始日期
            aae100: "1", // 是否为常用联系人
            bxzq00: 0,
            selectionType: "" // 套餐类型
          }
        ]
      },
      isAddVisible: false,
      paddingBottom: 0,
      dialogVisible: false, // 常用联系人弹窗
      relationship: "001",
      insuredInfo: { // 被保人信息
        topContacts: "1"
      },
      dictData: { // 字典数据
        YES_NO: [], // 是否
        DAA002: [] // 与投保人关系
      },
      connectList: [] // 常用联系人列表
      
    }
  },
  computed: {
    ...mapGetters(["formData_getter"]),
    money() {
      return (+(this.formData.da02List[0].bxzq00 || 0) * 0.4).toFixed(2)
    }
  },
  watch: {
    isAddVisible() {
      this.$nextTick(() => {
        this.paddingBottom = 16 + this.$refs.bottomBtn.getBoundingClientRect().height
      })
    }
  },
  created() {
    this.getDicData()
    this.getUserInfo()
  },
  mounted() {
    if (this.formData_getter && Object.keys(this.formData_getter).length) {
      this.formData = Object.assign({}, this.formData_getter)
    }
    
    this.paddingBottom = 16 + this.$refs.bottomBtn.getBoundingClientRect().height
  },
  methods: {
    showDetail(item) {
      this.dialogVisible = false
      if (!item) {
        return
      }

      item.ccg981 = item.daa005 
      const formData = Object.assign({}, this.formData)
      const da02ListInfo = formData.da02List[0]
      formData.da02List = [{...da02ListInfo, ...item}]
      this.formData = JSON.parse(JSON.stringify(formData))
    },
    /**
     * @description: 重置被投保人信息
     * @param {*}
     * @return {*}
     * @author: T
     */    
    restInsuredInfo() {
      this.formData.da02List = [
        Object.assign({}, this.formData.da02List[0], {
          aac003: "", // 姓名
          daa005: "", // 证件类型
          aac002: "", // 证件号码
          aae005: "" // 手机号码
        })
      ]
    },
    getUserInfo() {
      const userInfo = this.$sessionUtil.getItem("userInfo")
      if (!userInfo || Object.keys(userInfo).length === 0) {
        return
      }
      const { zjhm00: aac002, xm0000: aac003, mobile: aae005, zjlx00: ccg981} = userInfo
      let zjlx00 = ""
      if (!ccg981) {
        zjlx00 = ""
      } else {
        zjlx00 = ccg981?.length !== 3 ? `0${ccg981}` : ccg981
      }
      const insuranceInfo = SessionUtil.getItem("insuranceInfo")
      if (insuranceInfo && Object.keys(insuranceInfo).length) {
        this.formData.bxywy = insuranceInfo
      }
    
      this.formData.baseInfo = Object.assign(this.formData.baseInfo, { aac002, aac003, aae005, ccg981: zjlx00 })
      console.log(this.formData.baseInfo)
    },
    handleAdd() {
      this.isAddVisible = false
    },
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },
    handleCancel() {
      this.isAddVisible = true
    },
    async handleNext() {

      // 投保人字段转换
      const { aac003, aac002, ccg981, aae005, aae006} = this.formData.baseInfo
      this.formData.dac001 = ccg981 // 投保人证件类型
      this.formData.dac002 = aac002 // 投保人证件号码
      this.formData.dac003 = aac003 // 投保人姓名
      this.formData.aae005 = aae005 // 投保人手机号码
      this.formData.aae006 = aae006 // 投保人邮箱
      // 本人投保被投保信息处理
      const { daa002 } = this.formData.da02List[0]
      if (daa002 === "001") {
        this.formData.da02List = [
          Object.assign(
            {...this.formData.da02List[0], 
              dac002: aac002, // 投保人证件号码
              aac003, 
              aac002, // 被投保人证件号码
              daa005: ccg981, 
              ccg981,
              aae005
            })
          
        ]
      }
      // 数据存前端
      this.$store.dispatch("insure/updateForm", this.formData)

      // 校验-是否可进行投保申报
      const res = await this.checkApplyAvailable()
      console.log(res, "校验-是否可进行投保申报res")
      if (!res) {
        return
      }

      if (res.message) {
        this.$dialog.confirm({
          title: "提示",
          message: res.message,
          theme: "round-button",
          className: "ylb-dialog-alert"
        }).then(async() => {
          await this.saveOrUpdateDa03()
          this.$router.push({
            path: "/yilubao/agreement",
            query: {
              money: res.bfje00
            }
          })
        })
        return
      }

      await this.saveOrUpdateDa03()
      this.$router.push({
        path: "/yilubao/agreement",
        query: {
          money: res.bfje00
        }
      })
    },
    /**
     * @description: 承保校验 TODO
     * @param {*}
     * @return {*}
     * @author: T
     */    
    async checkApplyAvailable() {
      const otherInfo = this.formData.da02List?.[0] || {}
      const params = {
        ...this.formData,
        ...otherInfo,
        serviceName: "xytDa01Web_checkApplyAvailable"
      }
      params.da02List[0].daa005 = otherInfo.ccg981?.replace(/0/g, "")
      console.log(params, "checkApplyAvailable 承保校验接口入参")
      return await commonApi.proxyApi(params).then(res => {
        const { data } = res.map
        return data
      }).catch(() => false)
    },
    // 标签转码
    getRelationshipLable(relathionship) {
      return this.dictData.DAA002.find(item => item.aaa102 === relathionship) ?. aaa103 || ""
    },
    // 展示常用联系人
    onMoreCilck() {
      this.connectList = [] 
      this.findDa03ByPage()
      this.dialogVisible = true
    },
    // 查看常用联系人
    findDa03ByPage() {
      const { aac002: dac002 } = this.formData.baseInfo
      const params = {
        serviceName: "xytDa03_findDa03ByDac002",
        aae100: "1", // 是否添加常用联系人
        dac002,
        "page": 1,
        "size": 20
      }
      commonApi.proxyApi(params).then(res => {
        const { data } = res?.map || {}
        this.connectList = data || []
      })
    },
    // 常用联系人添加
    async saveOrUpdateDa03() {
      const { aac002, ccg981} = this.formData.baseInfo
      const { ccg981: da02_ccg981} = this.formData.da02List[0]
      const { daa002 } = this.formData.da02List[0]
      
      const params = {
        serviceName: "xytDa03_saveOrUpdateDa03",
        ...this.formData.da02List[0],
        daa005: da02_ccg981,
        dac002: aac002
      }
      if (daa002 === "001") {
        params.daa005 = ccg981
      }
      delete params.daz003
      console.log(params, "常用联系人添加入参")
      await commonApi.proxyApi(params).then(res => {
        console.log(res, "常用联系人添加")
      })
    },
    /**
     * @description: 获取字典数据
     * @param {*}
     * @return {*}
     * @author: T
     */    
    getDicData() {
      commonApi.proxyApi({ 
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["YES_NO", "DAA002"]
      }).then(res => {
        const { data } = res?.map
        this.dictData = data
      })
    }
  }
}
</script>

<style lang="less" scoped>
.none-data {
  height: 60px;
  font-size: 14px;
  text-align: center;
  line-height: 40px;
}
// 只要宽高比大于等于13/20，就会执行
@media (min-aspect-ratio: 13/20) {
  .botton-btn {
    display: none;
  }
}
/deep/.van-tabs {
  &__nav {
    .van-tab--active {
      color: @ylb_color;
    }
  }

  &__line {
    background-color: @ylb_color;
  }
} 
.content {
  padding-bottom: 70px;
}
.plr-32 {
  padding-left: 32px;
  padding-right: 32px;
}
.botton-btn {
  position: fixed;
  left: 0;
  bottom: 0;
  padding: 8px 8px 16px;
  width: 100%;
  background: #fff;
  box-shadow: 0px -1px 8px 0px rgba(186,186,186,0.32);
  z-index: 9;
  .row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
  }
  .van-button {
    flex: 1;
    &:last-child {
      margin-left: 12px;
    }
  }
  .cost-box {
    font-size: 24px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #FF3B30;
  }
  .outside-link,.escape-clause {
    padding-left: 8px;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #333333;
    
    line-height: 1.6;
    .link {
      color: @ylb_color;
    }
  }
  .escape-clause {
    // padding-left: 8px;
    // font-size: 14px;
    // font-family: PingFangSC, PingFang SC;
    // font-weight: 400;
    // color: #333333;
    margin-bottom: 16px;
    line-height: 1.6;
    // .link {
    //   color: @ylb_color;
    // }
  }
  
}

</style>