<!--
 * @Description: 暖新地图
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="service-map">
    <!-- 搜索 -->
    <div class="search-box flex-c-c">
      <img class="search-icon" src="@pic/life-service/<EMAIL>" alt="" />
      <van-field
        v-model="input"
        input-align="left"
        placeholder="请输入名称"
        :border="false"
        clearable
        @keyup.enter.native="handleSearch"
      />
      <span class="search-btn" @click="handleSearch">搜索</span>
    </div>

    <!-- 地图 -->
    <div :class="['map-container', showPopup ? 'map-container-short' : 'map-container-long']" id="map"></div>

    <!-- 图例 -->
    <div class="legend-container">
      <div class="title flex-c-c" @click="toggleLegend">
        <span>图例</span>
        <img :class="isLegendExpanded ? '' : 'arrow-down'" src="@/assets/imgs/common/arrow.svg" alt="">
      </div>
      <div :class="['legend-box', {'expanded': isLegendExpanded}]">
        <div class="legend-item"
          :class="{
            'legend-item-active': !legends.includes(item.value)
          }" v-for="item in YWLX00List" :key="item.value" @click="handleClickLegend(item)">
          <img class="legend-img" :src="require(`@/assets/imgs/trade-union-service/legend${item.value}${!legends.includes(item.value) ? '-active' : ''}.svg`)" alt="">
          <span class="legend-name">{{item.label
          }}</span>
        </div>
      </div>      
    </div>

    <!-- 弹窗 -->
    <van-popup
      :class="['info-popup', showPopup ? '' : 'info-popup-hide']"
      v-model="showPopup"
      position="bottom"
      :round="true"
      :overlay="true"
      :lock-scroll="false"
      :close-on-click-overlay="false"
    >
      <div class="title-box flex-c-c">
        <div
          :class="['title-box-left','title-box-active',]"
        >
          <span>暖新网点</span>
          <van-icon @click="closePopup" class="close-icon" name="close" />
        </div>
      </div>

      <div v-if="stageList.length > 0" class="list-box">
        <stage-box :stageInfo="stageData"></stage-box>
      </div>  
      <div v-else class="list-box">
        <y-empty ></y-empty>
      </div>
    </van-popup>
  </div>
</template>

<script>
/* eslint-disable no-undef */
import isEmpty from "lodash/isEmpty"

import { commonApi } from "@/api"
import {cloneDeep} from "lodash"
import {setWxConfig, openLocation, getLocation} from "@/utils/wechat"
import StageBox from "@/components/business/stage-box"

const PUBLIC_PATH = process.env.VUE_APP_PUBLIC_PATH  
export default {
  name: "service-map",
  components: {
    StageBox
  },
  data() {
    return {
      // 地图相关
      map: null, // 地图实例
      centerPoint: null, // 地图中心点
      markersArray: null, // 地图标记点数组
      infoWindow: null, // 信息窗口
      mapKey: process.env.VUE_APP_TMAP_KEY, //map key

      //信息弹窗
      showPopup: false, 

      // 搜索
      input: "",
      stageList: [], //驿站列表
      CAE026List: [], //所属区字典
      FWNR00List: [], //服务内容字典
      showLocation: false,
      activeLocation: "02",

      // 服务内容
      serviceList: [
        { 
          label: "休息",
          value: "01",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        },
        { 
          label: "饮水",
          value: "02",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        },        
        { 
          label: "充电",
          value: "04",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        },
        { 
          label: "急救药箱",
          value: "05",
          imgUrl: "@/assets/imgs/life-service/<EMAIL>"
        }
      ],

      // 图例
      isLegendExpanded: true,
      YWLX00List: [], //业务类型字典
      legends: [], //选中图例
      mapDatas: {}, //地图点位数据
      currentPoint: { //当前经纬度（本地测试默认位置）
        lat: 24.464656, 
        lng: 118.078316
      } 
    }
  },
  computed: {
    activeLocationName() {
      const data = this.CAE026List?.find(i => i.value === this.activeLocation) || {}      
      return data.label
    },
    stageData() {      
      return this.stageList?.[0] || {}
    }
  },
  created() {
    this.getPlatformList() //查询字典 
  },
  async mounted() {
    if (process.env.NODE_ENV === "production") { // 线上环境
      await setWxConfig() // 微信SDK签名配置    
      const {latitude="", longitude=""} = await getLocation() //获取当前位置 本地不方便测试 需部署到线上
      this.currentPoint = { //当前经纬度
        lat: latitude,
        lng: longitude
      } 
      this.reserMap() // 初始化地图实例   
    } else { // 开发环境      
      setWxConfig() // 微信SDK签名配置
      this.reserMap() // 初始化地图实例 
    }   

    this.mapDatas = await this.findDa12MapList() // 查询地图列表
    this.setStageList() // 设置点位数据列表
    this.markersPoint() // 地图描点
  },
  methods: {
    //查询字典
    async getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["CAE026", "FWNR00", "YWLX00"]
      }
      const res = await commonApi.proxyApi(params)
      const { data } = res.map
      const dictInfo = {
        CAE026: "CAE026List",
        FWNR00: "FWNR00List",
        YWLX00: "YWLX00List"
      }
      for (const key in dictInfo) {
        this[dictInfo[key]] = data[key].map((item) => {
          return { label: item.aaa103, value: item.aaa102 }
        })
      }
    },
    //初始化地图实例
    reserMap(zoom=13) {
      const {lat, lng} = this.currentPoint      
      this.centerPoint = new qq.maps.LatLng(lat, lng)
      const mapContainer = document.getElementById("map")
      this.map = null // 清除之前的地图实例      
      this.map = new qq.maps.Map(mapContainer, {
        center: this.centerPoint,
        zoom: zoom, 
        draggable: true,
        mapSign: false // 隐藏右下角图标
      })
    },
    // 查询地图列表
    async findDa12MapList() {
      const params = {
        serviceName: "xytHwyz_findDa12MapList",
        yzmc00: this.input //驿站名称 | 驿站地址
      }
      const res = await commonApi.proxyApi(params)  
      return res.map?.data?.datas || {}
    },
    // 设置点位数据列表
    setStageList() {
      if (isEmpty(this.mapDatas)) { // 无数据状态
        this.stageList = [] 
        return
      }

      let list = []
      this.legends.forEach(item => {
        if (this.mapDatas[item]?.length > 0) {
          list = [...list, ...this.mapDatas[item]]
        }        
      }) 
      list.forEach((item, v) => {    
        item = this.formatServiceList(item)        
      })      
      this.stageList = list   
    },    
    // 格式化服务内容数据
    formatServiceList(data) {
      const list = data?.fwnr00?.split(";") || []
      const serviceList = cloneDeep(this.serviceList)        
      data.serviceList = serviceList.map((item) => {
        return {...item, imgUrl: require(`@/assets/imgs/life-service/service${item.value}${list.includes(item.value) ? "-active" : ""}@2x.png`)}
      })
      return data
    },
    // 地图描点
    markersPoint() {        
      this.clearOverlays() // 1、清除标记
      
      this.map.setCenter(this.centerPoint) // 2、标记中心点
      new qq.maps.Marker({
        position: this.centerPoint,
        map: this.map,
        animation: null, // 可选：添加掉落动画        
        icon: new qq.maps.MarkerImage(
          `${PUBLIC_PATH}/map-marker/current-map-marker.svg`, // 图片的URL
          new qq.maps.Size(40, 40), // 图标大小
          new qq.maps.Point(0, 0), // 图标起点
          new qq.maps.Point(16, 32) // 图标锚点，决定图标与位置点的对齐方式
        ),
        shadow: null // 可选：添加阴影             
      })
      
      const markersArray = [] // 3、标记列表点           
      const iconUrlMap = {}
      this.YWLX00List.forEach(item => {
        iconUrlMap[item.value] = `${PUBLIC_PATH}/map-marker/legend${item.value}.svg`
      })
      this.stageList.forEach(item => {        
        const {zdwd00, zdjd00, daz012, ywlx00} = item
        
        const markerIcon = new qq.maps.MarkerImage( //标记点图标及样式
          iconUrlMap[ywlx00], // 图片的URL
          new qq.maps.Size(30, 30), // 图标的大小
          new qq.maps.Point(0, 0), // 图标的坐标
          new qq.maps.Point(30, 30) // 图标的锚点
        )

        const lat = zdwd00 && Number(zdwd00)
        const lng = zdjd00 && Number(zdjd00)        
        const marker = new qq.maps.Marker({
          id: daz012,
          daz012,
          position: new qq.maps.LatLng(lat, lng),
          map: this.map,
          icon: markerIcon
        })       
        markersArray.push(marker)
        
        qq.maps.event.addListener(marker, "click", this.handleClickMap) // 地图坐标点 点击事件
      })      
      this.markersArray = markersArray //标记点列表
    },    
    //清除地图标记点
    clearOverlays() {
      const {markersArray} = this
      if (markersArray?.length > 0) {
        for (const i in markersArray) {          
          markersArray[i].setMap(null)
        }
      }
    },
    // 地图点击事件    
    async handleClickMap(e) {
      // 收起图例
      this.isLegendExpanded = false

      // 移除信息窗口
      this.infoWindow && this.infoWindow.setMap(null)

      // 移动到指定坐标点
      const {position, daz012} = e.target
      const newCenter = new qq.maps.LatLng(position.lat, position.lng)
      this.map.panTo(newCenter)
      
      // 驿站网点信息
      const params = {serviceName: "xytHwyz_getDa12ById", daz012}
      const res = await commonApi.proxyApi(params)
      const { data } = res.map         
      const newData = this.formatServiceList(data)   
      this.stageList = [{...newData}]
      this.showPopup = true        

      // 信息窗口
      this.infoWindow = new qq.maps.InfoWindow({
        map: this.map,
        position: new qq.maps.LatLng(position.lat, position.lng),
        offset: { x: 0, y: -32 }, //设置信息窗相对position偏移像素，为了使其显示在Marker的上方
        enableCustom: false,
        maxHeight: 54,
        maxWidth: 150,
        minHeight: 0,
        minWidth: 0
      })  
      this.infoWindow.open() //打开信息窗
      this.infoWindow.setPosition(position)//设置信息窗位置

      this.infoWindow.setContent(`<div class="ref-card">${newData.yzmc00}</div>`)//设置信息窗内容 
      setTimeout(() => { //设置字体样式
        document.querySelector(".ref-card").style="font-size: 0.3rem; color: #333333;"
      }, 100)           
    },

    // 点击图例
    handleClickLegend(item) {
      this.input = "" // 清空搜索输入框
      
      const {value} = item
      if (this.legends.includes(value)) {
        const index = this.legends.findIndex((val) => val === value)
        this.legends.splice(index, 1)
      } else {
        this.legends.push(value)
      }      
      
      this.setStageList() // 设置点位数据列表
      this.markersPoint() // 地图描点
    },
    // 切换图例展开状态
    toggleLegend() {
      this.isLegendExpanded = !this.isLegendExpanded

      if (this.isLegendExpanded) {
        this.showPopup = false
      }
    },    
    
    // 搜索
    async handleSearch() {
      this.showPopup = false
      this.legends = [] // 清空图例选中状态

      const mapDatas = await this.findDa12MapList() // 查询地图列表
      let list = [] // 设置点位数据列表
      for (const key in mapDatas) {
        list = [...list, ...mapDatas[key]]
      }
      this.stageList = list
      this.markersPoint() // 地图描点
    },
    // 关闭弹窗
    closePopup() {
      this.infoWindow && this.infoWindow.setMap(null)
      this.showPopup = false
    },
    // 到这去
    handleNavigate(item) {      
      const {zdwd00: lat, zdjd00: lng, yzdz00: address, yzmc00: name} = item      
      openLocation({lat, lng, address, name}) //微信查看地图SDK
    }
  }
}
</script>

<style lang="less" scoped>
.service-map {
  background: #4c4c4c;
  position: relative;
  .search-box {
    position: absolute;
    width: 305px;
    height: 36px;
    background: #ffffff;
    border-radius: 18px;
    top: 16px;
    left: 50%;
    transform: translate(-50%);
    z-index: 99999;
    padding: 0 16px;
    .search-icon {
      width: 16px;
      height: 14px;
    }
    .van-field {
      flex: 1;
      padding: 0 10px;
      /deep/.van-icon-clear {
        font-size: 14px;
      }
      /deep/.van-field__control::-webkit-input-placeholder {
        font-size: 14px;
      }
    }
    .search-btn {
      font-size: 14px;
      color: #bd1a2d;
      display: inline-block;
      height: 100%;
      line-height: 36px;
    }
  }
  .map-container {
    width: 100%;
    &-short {
      height: calc(100vh - 260px);
    }
    &-long {
      height: calc(100vh);
    }
  }
  ::v-deep .map-container {
    & > div > div:nth-child(2) {
      display: none;
    }
    .smnoprint {
      display: none;
    }
  }
  .legend-container {
    position: absolute;
    top: 72px;
    right: 16px;
    width: 68px;
    height: auto;  
    border-radius: 8px;
    background: #ffffff;    
    .title {
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: center;
      margin: 14px 0;
      & > img {
        margin-left: 4px;
        transition: transform 0.3s ease-in-out;
        &.arrow-up {
          transform: rotate(0deg);
        }
        &.arrow-down {
          transform: rotate(180deg);
        }
      }      
    }
    .legend-box {
      max-height: 0; /* 默认收起时高度为0 */
      overflow: hidden; /* 隐藏超出部分 */
      transition: max-height 0.3s ease-in-out; /* 添加过渡效果 */
      .legend-item {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-bottom: 16px;
        .legend-img {
          width: 30px;
          height: 30px;
        }
        .legend-name {
          margin-top: 2px;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
          text-align: center;
        }
        &-active {
          .legend-name {
            color: #999999;
          }
        }
        &-hide {
          display: none;
        }
      }
    }
    .legend-box.expanded {
      max-height: calc(100vh - 140px); /* 展开时设置一个足够大的高度 */
      overflow-y: auto; /* 展开时允许垂直滚动 */
    }    
  }
  .info-popup {    
    overflow: hidden;
    &-hide {
      display: none;
    }
    .title-box {
      width: 100%;
      height: 44px;
      font-size: 16px;
      color: #666666;
      line-height: 22px;
      text-align: center;
      line-height: 44px;
      &-left {
        flex: 1;
        height: 100%;
        position: relative;
        .close-icon {
          position: absolute;
          right: 8px;
          top: 8px; 
          color: #666666;
        }
      }
      &-right {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        position: relative;
        .van-icon {
          font-size: 14px;
          margin-left: 4px;
          transform: rotate(-90deg);
          transition: all 0.7s;
          padding-bottom: 2px;
        }
        .location-list {
          width: 160px;
          background: #fff;
          border-radius: 4px;
          z-index: 99999;
          box-shadow: 0px -1px 8px 0px rgba(186, 186, 186, 0.32);
          height: 270px;
          overflow: scroll;
          position: absolute;
          top: 44px;
          left: 50%;
          transform: translate(-50%);
          & > p {
            color: #666666;
          }
          .active {
            color: #bd1a2d;  
          }
        }
      }
      &-active {
        color: #bd1a2d;        
      }
      .icon-active {
        .van-icon {
          font-size: 14px;
          margin-left: 4px;
          transform: rotate(90deg);
          transition: all 0.7s;
        }
      }
    }
    .content-title {
      width: 100%;
      line-height: 44px;
      text-align: center;
      font-weight: bold;
      font-size: 16px;
      color: #bd1a2d;
      border-bottom: 1px solid #ebebeb;
    }
    .list-box {
      overflow: auto;
      .item {
        padding: 8px 16px 16px;
        &:not(:last-child) {
          border-bottom: 1px solid #ebebeb;
        }
        &-top {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          &-left {
            flex: 1;
            .name {
              font-weight: bold;
              font-size: 16px;
              color: #333333;
              line-height: 22px;
              margin-bottom: 6px;
            }
            .text {
              font-size: 12px;
              color: #666666;
              line-height: 18px;
            }
            .hour-icon {
              margin-top: 10px;
              & > img {
                width: 13px;
                height: 14px;
              }
              & > span {
                font-size: 12px;
                color: #999999;
                line-height: 18px;
                margin-left: 4px;
              }
            }
          }
          &-right {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-left: 12px;
            & > img {
              width: 32px;
              height: 32px;
            }
            & > span {
              margin-top: 4px;
              font-size: 10px;
              color: #999999;
              line-height: 14px;
              text-align: center;
            }
          }
        }
        &-bottom {
          .service-title {
            font-size: 12px;
            color: #666666;
            line-height: 18px;
            margin-top: 12px;
          }
          .service-list {
            margin-top: 12px;
            .service {
              width: 52px;
              height: 52px;
              border-radius: 4px;
              border: 1px solid #eeeeee;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              &:not(:first-child) {
                margin-left: 10px;
              }
              & > img {
                width: 16px;
                height: 16px;
              }
              & > span {
                font-size: 12px;
                color: #666666;
                line-height: 18px;
                text-align: center;
                margin-top: 2px;
              }
            }
            .other-service {
              font-size: 12px;
              color: #bd1a2d;
              line-height: 18px;
              text-align: left;
            }
          }
        }
      }
    }
  }
  /deep/.van-overlay {
    background: rgba(0, 0, 0, 0);
    z-index: -1 !important;
  }
}
</style>

<style>
/* .csssprite {
  width: 32px !important;
  height: 32px !important;
  min-width: unset !important;
  min-height: unset !important;
  max-width: unset !important;
  max-height: unset !important;
}
.ref-card {
  width: 100%;
  height: 100%;
  background:red;
} */
/* .qq-map-info-window {
    background-color: #f0f0f5; 
    border: 1px solid #ccc;
    border-radius: 5px; 
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.qq-map-info-window .title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    padding: 5px 10px;
    border-bottom: 1px solid #ccc;
}

.qq-map-info-window .content {
    font-size: 14px;
    color: #666;
    padding: 10px;
} */
</style>
