<!--
 * @Description: 工作台-订单详情
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <div class="card-box ylb-container mt12">
      <div class="card-title">订单详情</div>
      <div class="content-box mt24">
        <van-tabs v-model="active">
          <van-tab v-for="(item, key) in tabList" :title="item.text" :name="item.id" :key="key">
          </van-tab>
        </van-tabs>
       
      </div>
    </div>

    <view-box :dictData="dictData" :formList="formList" @handlePay="handlePay" @handleCancel="handleCancel"></view-box>
  </div>
</template>

<script>
import ViewBox from "./view-box"
import {commonApi} from "@/api"

export default {
  components: {
    ViewBox
  },
  data() {
    return {
      active: 0,
      tabList: [
        {id: "", type: "all", text: "全部"},
        {id: "001", type: "pay", text: "待支付"},
        {id: "002_1", type: "ensure", text: "保障中"},
        {id: "002_2", type: "stop", text: "已终止"}
      ],

      daz005: "", //查询主键
      orderType: "", //订单状态

      formList: [],
      dictData: { // 字典数据
        YES_NO: [], // 是否
        GZPT00: [], // 工作平台
        AAB301_XM: [] // 工作地区
      }
    }
  },
  watch: {
    active(val) {
      this.orderType = val
      this.formList = []

      if (this.daz005) {
        this.querySalesDa02ListByStatus(this.daz005)
      }
    }
  },
  created() {
    this.getDicData()
    this.$bus.$on("getAction", this.onAction)
    this.$once("hook:beforeDestroy", () => {
      this.$bus.$off("getAction")
    })
  },
  methods: {
    onAction(data) {
      const {daz005} = data
      this.daz005 = daz005
      this.querySalesDa02ListByStatus(daz005) //查询-掌上分销订单详情
    },
    async getDicData() {
      const res = await commonApi.proxyApi({ 
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["YES_NO", "AAB301_XM", "GZPT00"]
      })
      const { data } = res.map || {}
      this.dictData = data
    },

    querySalesDa02ListByStatus(daz005) {
      const params = {
        serviceName: "xytPerson_querySalesDa02ListByStatus",
        daz005,
        tbzt00: this.orderType
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "查询-掌上分销订单详情")
        this.formList = res.map.data || []
      })
    },
    handlePay() {},
    handleCancel() {}
  }
}
</script>
<style lang="less" scoped>
.ylb-container .content-box {
  padding: 0;
}
.card-box {
  border-radius: 12px 12px 0 0;
  .van-tabs {
    padding: 0 20px;
  }
}
</style>