<!--
 * @Description: 技能培训-首页
 * @Author: hwx
 * @date: 2024/5/23 11:29
 * @LastEditors: hwx
-->
<template>
  <div class="skill-train-container">
    <div class="header-box">
      <img :src="require('@pic/skill-train/header-bg.png')" alt="">
    </div>
    <div class="skill-train-box">
      <div class="business-container">
        <y-title content="技能提升" fontWeight="bold" font-cont-size="14" />
        <div class="business-box flex-c-s mt16">
          <div class="business-item flex-c-c-c mb16" v-for="(item,index) in businessList" :key="index" @click="handleJump(item)">
            <img :src="item.imgUrl" alt="">
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>

export default {
  name: "skill-train",

  data() {
    return {
      businessList: [
        {
          imgUrl: require("@pic/skill-train/1.png"),
          title: "技能补贴申请",
          path: "/skill-subsidies"
        },
        {
          imgUrl: require("@pic/skill-train/2.png"),
          title: "新就业形态云课堂",
          path: "/on-line-study"
        },
        {
          imgUrl: require("@pic/skill-train/3.png"),
          title: "培训记录查询",
          path: "/skill-record-query"
        },
        {
          imgUrl: require("@pic/skill-train/4.png"),
          title: "工种培训机构情况",
          path: "/skill-job-organ-query"
        },
        {
          imgUrl: require("@pic/skill-train/5.png"),
          title: "证书信息查询",
          path: "/skill-certificate-information"
        }
      ]
    }
  },
  methods: {
    handleJump(item) {
      if (!item.path){
        this.$toast("功能建设中，敬请期待!")
        return
      }

      this.$router.push(item.path)
    }
  }
}
</script>

<style scoped lang="less">
.skill-train-container{
  background-color: #F6F6F6;
  .header-box {
    width: 100%;
    & > img {
      width: 100%;
    }
  }
  .skill-train-box {
    padding: 24px 16px;
    min-height: calc(100vh - 202px);
    .business-container {
      background-color: @white_bg_color;
      padding: 8px 16px 6px;
      border-radius: 4px;
      .business-box .business-item {
        min-width: 33%;
        max-width: 34%;
        flex: 1;
        & > img {
          width: 44px;
        }
        .item-title {
          height: 40px;
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
          margin-top: 12px;
          text-align: center;
        }
      }
    }
  }
  .skill-train-end{
    padding-top: 0;
    .business-container{
      .business-box{
        padding-bottom: 8px;
      }
    }
  }
}
</style>