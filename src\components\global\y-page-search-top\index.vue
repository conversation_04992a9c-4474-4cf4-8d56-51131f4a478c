<!--
 * @Description: 顶部页面路由搜索
 * @Version: 1.0.0
 * @Autor: xyDideo
-->
<template>
  <div class="container">
    <van-search v-model="searchValue" shape="round" placeholder="请输入路由或名称等关键词搜索" show-action>
      <span slot="action" @click="onSearch()" class="text-right">搜索</span>
    </van-search>
    <van-overlay :show="show" @click="show = false">
      <div class="wrapper" @click.stop class-name="overlay-box">
        <!-- 模糊搜索结果 -->
        <van-panel class="info-list mg-16 radius-12 y-box-shadow">
          <template #header>
            <van-cell class="card-list radius-12">
              <template #title class="title-line">
                <y-title content="搜索结结果" class="y-card-title" :moreText="resultLength ? resultLength + '个结果' : ''" />
              </template>
            </van-cell>
          </template>
          <template #footer>
            <div class="van-panel-footer">
              <van-cell is-link :to="item.path" :label="'path: ' + item.path" class="mb-12" v-for="(item, index) in searchResult" :key="index">
                <template #title>
                  <h4 v-html="item.meta.title" :key="searchValue" v-mark="{replaceText: searchValue, replaceContent: `<span class='keyword'>${searchValue}</span>`}"></h4>
                </template>
                <template #label>
                  <h4 v-html="'path: ' + item.path" :key="searchValue" v-mark="{replaceText: searchValue, replaceContent: `<span class='keyword'>${searchValue}</span>`}"></h4>
                </template>
              </van-cell>
              <!-- 却省 -->
              <van-empty class="custom-image" :image="require('./imgs/<EMAIL>')" description="暂无结果" v-if="!resultLength" />
            </div>
          </template>
        </van-panel>
      </div>
    </van-overlay>

  </div>
</template>

<script>
import Vue from "vue"
import { Overlay } from "@ylz/vant"
Vue.use(Overlay)
export default {
  components: {},
  name: "y-page-search-top",
  props: {
    lists: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  filters: {},
  directives: {
    mark(el, bindding) {
      const reg = new RegExp(bindding.value.replaceText, "g")
      el.innerHTML = el.innerHTML.replace(reg, bindding.value.replaceContent)
    }
  },
  data() {
    return {
      searchResult: [],
      searchValue: "",
      show: false
    }
  },
  watch: {
    searchValue(val) {
      // 过滤路由返回查找结果
      this.searchResult = this.lists.filter(
        (item) => item.meta.title.includes(val) || item.path.includes(val)
      )
      this.show = val ? true : false
    }
  },
  computed: {
    resultLength() {
      return this.searchResult.length
    }
  },
  created() {},
  mounted() {},
  methods: {
    onSearch() {
      this.show = true
    }
  },
  destroyed() {}
}
</script>

<style scoped lang="less">
.container {
  .mb-12 {
    margin-bottom: 12px;
  }
  ::v-deep .info-list {
    .van-cell {
      &__title {
        .keyword {
          color: @main_color;
          font-weight: bold;
        }
      }
      &::after {
        border: none;
      }
    }
    .van-panel__footer {
      max-height: 60vh;
      overflow: scroll;
    }
    // 缺省页
    .custom-image {
      width: 180px;
      height: 120px;
      margin: 20px auto;
    }
    // 标题右侧样式调整
    .y-title .more,
    .y-title .more-flag {
      right: 0;
    }
  }

  // 遮照
  .van-overlay {
    height: calc(100vh - 45px);
    top: 45px;
  }
}
</style>