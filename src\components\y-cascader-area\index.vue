<template>
  <div class="y-cascader-area">
    <van-field
      v-model="displayValue"
      :name="name"
      :label="label"
      :placeholder="placeholder"
      :required="required"
      :rules="rules"
      readonly
      is-link
      v-bind="$attrs"
      v-on="$listeners"
      @click="handleClickField"
    />
    
    <van-popup v-model="showPicker" round position="bottom">
      <van-cascader
        v-model="cascaderValue"
        :title="title"
        :options="areaOptions"
        @close="showPicker = false"
        @finish="onFinish"
      />
    </van-popup>
  </div>
</template>

<script>
import cityData from "@/assets/data/city_code.json"

export default {
  name: "y-cascader-area",
  model: {
    prop: "value",
    event: "change"
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ""
    },
    name: {
      type: String,
      default: ""
    },
    label: {
      type: String,
      default: "所在地区"
    },
    placeholder: {
      type: String,
      default: "请选择"
    },
    required: {
      type: Boolean,
      default: false
    },
    rules: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: "请选择所在地区"
    }
  },
  data() {
    return {
      showPicker: false,
      cascaderValue: "",
      displayValue: "",
      areaOptions: cityData
    }
  },
  watch: {
    value: {      
      handler(newVal) {
        if (newVal) {
          this.parseValueToText(newVal)
          this.cascaderValue = newVal
        } else {
          this.displayValue = ""
          this.cascaderValue = ""
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化 解析区划编码
    parseValueToText(code) {
      const findPath = (options, targetCode, path = []) => {
        for (const option of options) {
          if (option.value === targetCode) {
            return [...path, option]
          }
          if (option?.children?.length > 0) {
            const result = findPath(option.children, targetCode, [...path, option])
            if (result) { return result }
          }
        }
        return null
      }

      const selectedOptions = findPath(this.areaOptions, code)
      if (selectedOptions) {
        this.displayValue = selectedOptions.map(opt => opt.text).join("/")
      }
    },
    // 选择省市区
    handleClickField() {
      if (this.disabled) {
        return
      }

      this.showPicker = true
    },
    // 选择完成
    onFinish({ selectedOptions }) {      
      this.displayValue = selectedOptions.map(option => option.text).join("/")
      const code = selectedOptions[selectedOptions.length - 1].value
      this.$emit("change", code)
      this.showPicker = false
    }
  }
}
</script>

<style lang='less' scoped>
.y-cascader-area {
  ::v-deep .van-icon-arrow {
    display: none;
  }
}
</style>