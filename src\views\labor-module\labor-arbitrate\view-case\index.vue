<!--
 * @Description: 结案查看
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div>
    <!-- 申请表单 -->
    <van-form ref="baseForm" class="base-form" disabled>
      <van-field
          v-model="formData.sqrName"
          name="sqrName"
          label="预申请人"
          placeholder="请输入"
        />    
      
      <van-field
          v-model="formData.bsqrName"
          name="bsqrName"
          label="	被申请人"
          placeholder="请输入"
        />

      <van-field
          v-model="formData.abb025"
          name="abb025"
          label="结案日期"
          placeholder="请输入"
        />

      <van-field
          v-model="formData.aba004"
          name="aba004"
          label="仲裁请求"
          placeholder="请输入"
        />

      <van-field
        class="van-field-textarea"
        v-model="formData.abb028"
        name="abb028"
        label="处理结果"
        type="textarea"
        >
      </van-field>
    </van-form>    
  </div>
</template>

<script>
import {commonApi} from "@/api"

export default {
  data() {
    return {
      formData: {
        sqrName: "", //申请人
        bsqrName: "", //被申请人
        abb025: "", //结案日期
        aba004: "", //仲裁请求
        abb028: "" //处理结果
      }
    }
  },
  computed: {
    bcz006() {
      return this.$route.query.bcz006
    },
    abb001() {
      return this.$route.query.abb001
    }
  },
  created() {
    this.findBc05ByPage()
    this.getBc06ById()

  },
  methods: {
    // 查询详情
    getBc06ById() {
      const {bcz006} = this
      commonApi.proxyApi({
        serviceName: "xytBc06_getBc06ById",
        bcz006
      }).then((res) => {
        console.log("查询详情", res)
        const {data} = res.map
        this.formData = {...this.formData, ...data}  
        const abb025 = this.dayFormatFn(data.abb025, "date")
        this.formData.abb025 = abb025
      })
    },
    findBc05ByPage() {
      const {abb001} = this
      const params = {
        serviceName: "xytBc05_findBc05ByPage",
        slzt00: "2",
        abb001,
        page: 1,
        size: 1
      }
      commonApi.proxyApi(params).then((res) => {
        const {bsqrName, sqrName} = res.map.data.rows[0]
        this.formData = {...this.formData, ...{bsqrName, sqrName}}
      })
    }
  }
}
</script>
<style lang="less" scoped>
.uploader-title {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
  line-height: 18px;
  padding: 16px 0 0 16px;
}
.uploader-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: 20px;
  /deep/.van-uploader {
    .van-uploader__input-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      
      .uploader-box {
        margin: 20px 0 0;
        border-radius: 8px;
        border: 1px dashed #CECECE;
        width: 180px;
        height: 112px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        & > img {
          width: 98px;
          height: 64px;
        }
        & > span {
          font-size: 14px;
          color: #303133;
          line-height: 16px;
        }
      }
    }
    .van-uploader__preview {
      margin: 20px 0 0;
      border-radius: 8px;
      border: 1px dashed #CECECE;
      width: 180px;
      height: 112px;
      display: flex;
      justify-content: center;
      align-items: center;
      & > img {
        width: 100%;
      }
    }
  }
}
.uploader-container-alone {
  padding: 8px 16px;
}

</style>