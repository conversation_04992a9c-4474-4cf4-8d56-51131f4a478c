<!--
 * @Description: 工作台
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="work-platform">
    <header-block></header-block>

    <div class="content-box">
      <data-analysis></data-analysis>

      <order-details></order-details> 
    </div>    
  </div>
</template>

<script>
import HeaderBlock from "./components/header-block.vue"
import DataAnalysis from "./components/data-analysis.vue"
import OrderDetails from "./components/order-details.vue"

export default {
  name: "work-platform",
  components: {
    HeaderBlock,
    DataAnalysis,
    OrderDetails
  },
  data() {
    return {}
  }
}
</script>
<style lang="less" scoped>
.work-platform {
  background: @main_bg_color;
  min-height: calc(100vh - 90px);
  .content-box {
    padding: 12px 16px 130px;
  }
}
</style>