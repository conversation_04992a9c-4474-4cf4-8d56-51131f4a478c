<!--
 * @Description: 自定义弹窗
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <van-popup v-model="show" z-index="999" get-container="body" overlay-class="dialog-wrapper" round>
    <div class="dialog-wrapper" @click.stop>
      <div class="dialog-header">
        <div class="title">{{ title }}</div>
        <van-icon name="cross" color="#979797" size="16" @click="show=false"/>
      </div>
      <div class="dialog-content">
        <slot></slot>
      </div>

      <div class="ylb-btn bottom-btn">
        <van-button round block @click="show=false">取消</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: "ylb-dialog",
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: "标题"
    }
  },
  computed: {
    show: {
      get() {
        console.log(this.visible, "visibel")
        return this.visible
      },
      set() {
        this.$emit("update:visible", false)
      }
    }
  }

}
</script>

<style lang="less" scoped>
.dialog-wrapper {
  position: relative;
  padding: 17px 14px;
  min-height: 200px;
  width: 312*100/375vw;
  
  .dialog-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
    padding-bottom: 27px;
  }
  .dialog-content {
    max-height: 500px;
    overflow: auto;
  }
  /deep/.bottom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 41px;
    .van-button {
      flex: 1;
      background: @ylb_color;
      color: #fff;
    }
  }
}
</style>