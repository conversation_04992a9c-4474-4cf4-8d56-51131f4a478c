<!--
 * @Description: 在线学习
 * @Author: hwx
 * @date: 2024/10/08 15:18
 * @LastEditors: hwx
-->
<template>
  <div class="on-line-study-container">
    <p class="title">骑手学习天地</p>
    <p class="sub-title">知识共享 技能提升</p>
    <van-sticky>
      <div class="tabs-container">
        <y-title content="课程列表" />
      </div>
    </van-sticky>

    <template v-if="videoList.length > 0">
        <van-list
          v-model="loading"
          :finished="finished"
          :finished-text="videoList?.length > 4 ? '没有更多了' : ''"
          :immediate-check="false"
          @load="onLoad"
        >
          <van-grid :column-num="2">
            <van-grid-item v-for="(subItem, subkey) in videoList" :key="subkey + 'videoMap'" @click="handleCard(subItem)">
              <div class="image-wrap">
                <van-image v-if="subItem.sptp00" :src="`data:image/jpeg;base64,${subItem.sptp00}`" />
              </div>
              <p class="item-title text-ellipsis-2">{{ subItem.qsspmc }}</p>
              <div class="item-sub-title">
                <span class="text-ellipsis">{{ subItem.sply00 }}</span>
                <span class="count">
                  <van-icon name="eye-o" />
                  {{ subItem.js0000 }}
                </span>
              </div>
            </van-grid-item>
          </van-grid>
        </van-list>
    </template>
    <y-empty v-else />
  </div>
</template>

<script>
import { commonApi } from "@/api"
import YEmpty from "@/components/global/y-empty"
import detect from "@/utils/detect"
import { isEmpty as _isEmpty } from "lodash"
export default {
  name: "rider-online-study",
  components: {YEmpty},
  data(){
    return {
      searchForm: {
        page: 1,
        size: 12
      },
      total: 0,
      videoList: [],
      loading: false,
      finished: false,
      cf05id: "" //安全警示名单记录id
    }
  },
  mounted() {
    const {cf05id} = this.$route.query
    this.cf05id = cf05id
    this.findSpxxHmdByPageFn()
  },
  methods: {
    // 加载列表
    onLoad(){
      this.finished = true
      this.searchForm.page += 1
      this.findSpxxHmdByPageFn()
    },
    // 查询列表
    findSpxxHmdByPageFn(){
      this.loading = true
      commonApi.proxyApi({
        serviceName: "xytQsgl_findSpxxHmdByPage",
        ...this.searchForm,
        cf05id: this.cf05id
      }).then(res => {
        console.log(res, "res666")
        
        const { rows = [], total = 0 } = res?.map?.data
        if (Number(total) === 0){
          this.finished = true
          this.videoList = []
        }

        if (_isEmpty(rows)){
          this.finished = true
          this.isEmpty = true
        }

        this.videoList = [...this.videoList, ...rows]

        this.finished = this.videoList.length >= Number(total) // 根据结果修改当前的结束状态
      }).finally(() => {
        this.loading = false
      })
    },
    // 点击查看视频
    handleCard(item){
      const {qssp01, qsspmc} = item
      commonApi.proxyApi(
        {
          serviceName: "xytQsgl_getZxxxURL",
          qssp01,
          qsspmc,
          dylx00: "1" //PC端'' 移动端1
        }
      ).then((res) => {
        const url = res.msg
        if (!res.msg){
          this.$toast("无对应视频!")
          return
        }

        if (detect.isIOS){
          window.location.href = url
        } else {
          window.open(url)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="less">
.on-line-study-container{
  position: relative;
  min-height: 100vh;
  background-image: url("~@pic/on-line-study-modules/bg.png");
  background-repeat: no-repeat;
  background-size: contain;
  padding: 20px 16px 40px;
  .title{
    position: absolute;
    top: 34px;
    left: 26px;
    font-size: 30px;
    font-weight: bold;
    color: #3674E8;
    line-height: 42px;
    text-align: left;
  }
  .sub-title{
    position: absolute;
    top: 82px;
    left: 26px;
    font-size: 14px;
    color: #666666;
    line-height: 20px;
    text-align: left;
  }
  .tabs-container{
    position: relative;
    margin-top: 136px;
    padding-left: 12px;
    background: #FFFFFF;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    overflow: hidden; 
    font-weight: bold;   
  }

  /deep/.van-grid{
    width: 100%;
    background-color: #FFF;
    .van-grid-item{
      border: none;
      margin-bottom: 10px;
      &__content{
        padding-bottom: 0;
        box-shadow: 0 2px 4px 0 rgba(0,0,0,0.04);
        border-radius: 6px;
        .image-wrap{
          width: 100%;
          height: 88px;
          background: #E8F4FF;
          box-shadow: inset 0 0 4px 0 #D9ECFD;
          border-radius: 6px 6px 0 0;
          overflow: hidden;
          .van-image{
            width: 100%;
            height: 100%;
          }
        }
        .item-title{
          width: 100%;
          font-size: 14px;
          color: #333;
          //height: 50px;
          line-height: 40px;
          max-height: 40px;
          min-height: 40px;
          font-weight: 600;
          text-align: left;
          //padding-top: 8px;
        }
        .item-sub-title{
          display: flex;
          justify-content: space-between;
          width: 100%;
          font-size: 12px;
          color: #666;
          line-height: 20px;
          padding-top: 8px;
          padding-bottom: 8px;
          .count{
            display: flex;
            align-items: center;
            .van-icon{
              margin-right: 4px;
            }
          }
        }
        &.van-hairline{
          &::after{
            border: none;
          }
        }
      }
    }
    &.van-hairline--top{
      &::after{
        border: none;
      }
    }
  }
  .van-sticky--fixed{
    .tabs-container{
      margin-top: 0;
      .van-tab__pane{
        padding: 0 12px;
      }
    }
  }

  .sheet-container{
    .van-action-sheet__header{
      padding-top: 16px;
      padding-left: 16px;
      text-align: left;
      font-weight: 600;
      font-size: 16px;
      color: #333;
      line-height: 22px;
      .van-action-sheet__close{
        top: 16px;
        color: #979797;
        font-size: 18px;
      }
    }
    .van-action-sheet__content{
      padding-bottom: 60px;
      overflow-y: auto;
      .content{
        display: flex;
        flex-flow: wrap;
        .menu-button{
          width: 160px;
          height: 40px;
          line-height: 40px;
          background: #EEEEEE;
          border-radius: 22px;
          min-width: 40%;
          text-align: center;
          margin: 10px auto;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          &.active{
            background: #F8E8EA;
            color: #BD1A2D;
          }
          &:nth-child(2n){
            margin-left: 0;
          }
          &.transparent{
            opacity: 0;
          }
        }
      }
      .footer-container{
        position: fixed;
        bottom: 0;
        width: 100%;
        background-color: #fff;
        .van-divider {
          margin-top: 0;
          margin-bottom: 8px;
        }
        .footer{
          display: flex;
          align-items: center;
          padding-bottom: 8px;
          .van-button{
            width: 160px;
            height: 40px;
            margin: auto;
          }
        }
      }
    }
  }
}
</style>