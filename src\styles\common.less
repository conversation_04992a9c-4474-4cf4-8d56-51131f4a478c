/*
 * @Description: 页面通用样式
 * @Version: v1.0.0
 * @Autor: xyDideo
 * @Date: 2020-06-01 09:16:39
 * @LastEditors: hwx <EMAIL>
 * @LastEditTime: 2024-06-18 09:43:07
 */
@import "./animate.less";
@import "./normalize.less";
@import "./theme/theme-params.less";

.tac {
  text-align: center !important;
}

.ml-4 {
  margin-left: @space_base;
}

.ml-8 {
  margin-left: @space_xs_8;
}

.ml-16 {
  margin-left: @space_md_16;
}

.ml-24 {
  margin-left: @space_lg_24;
}

.mt-6 {
  margin-top: 6px * @ratio;
}

.mt-12 {
  margin-top: 12px * @ratio;
}

.mt-14 {
  margin-top: 14px * @ratio;
}

.mt-24 {
  margin-top: 24px * @ratio;
}

.mb-8 {
  margin-bottom: 8px * @ratio;
}

.mb-10 {
  margin-bottom: 10px * @ratio;
}

.mb-20 {
  margin-bottom: 20px * @ratio;
}
.mg-16 {
  margin: 16px * @ratio;
}
.radius-12 {
  border-radius: 12px * @ratio;
}
.radius-8 {
  border-radius: 8px * @ratio;
}
.y-box-shadow {
  box-shadow: 0 0 12px 0 rgba(96, 137, 180, 0.2);
}

.ft-12 {
  font-size: 12px * @ratio;
}

.ft-24 {
  font-size: 24px * @ratio;
}

.ft-26 {
  font-size: 26px * @ratio;
}

.ft-28 {
  font-size: 28px * @ratio;
}

.ft-30 {
  font-size: 30px * @ratio;
}

.ft-32 {
  font-size: 32px * @ratio;
}

.ft-34 {
  font-size: 34px * @ratio;
}

.ft-36 {
  font-size: 36px * @ratio;
}

.ft-38 {
  font-size: 38px * @ratio;
}

.ft-40 {
  font-size: 40px * @ratio;
}

.ft-44 {
  font-size: 44px * @ratio;
}

.ft-46 {
  font-size: 46px * @ratio;
}

.ft-48 {
  font-size: 48px * @ratio;
}

.ft-w {
  font-weight: bold;
}

.pd-20 {
  padding: @space_lg_24;
}

.pt-20 {
  padding-top: @space_lg_24;
}

.pl-20 {
  padding-left: @space_lg_24;
}

.pr-20 {
  padding-right: @space_lg_24;
}

.pb-16 {
  padding-bottom: 16px;
}
.pb-20 {
  padding-bottom: @space_lg_24;
}

.flex {
  display: flex;
  align-items: center;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.lh-22 {
  line-height: 22px * @ratio;
}

.lh-24 {
  line-height: 24px * @ratio;
}

.lh-26 {
  line-height: 26px * @ratio;
}

.lh-28 {
  line-height: 28px * @ratio;
}

.lh-30 {
  line-height: 30px * @ratio;
}

.lh-32 {
  line-height: 32px * @ratio;
}

.lh-34 {
  line-height: 34px * @ratio;
}

.lh-36 {
  line-height: 36px * @ratio;
}

.lh-38 {
  line-height: 38px * @ratio;
}

.lh-40 {
  line-height: 40px * @ratio;
}

.lh-42 {
  line-height: 42px * @ratio;
}

.lh-45 {
  line-height: 45px * @ratio;
}

.lh-48 {
  line-height: 45px * @ratio;
}

.lh-50 {
  line-height: 50px * @ratio;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.cf:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
}

.cf {
  zoom: 1;
}

* {
  box-sizing: border-box;
}

.divider-no-border {
  height: 20px * @ratio;
  display: block;
  clear: both;
  width: 100%;
  background-color: @main_bg_color;
}

.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.clearfix {
  *zoom: 1;
  /*ie6清除浮动的方式 *号只有IE6-IE7执行，其他浏览器不执行*/
}

// 一行省略号
.xz-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 两行省略号
.xz-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.width-120 {
  width: 120px * @ratio;
}

// 公共css
.notes-btn {
  width: 90%;
  margin: auto;
  padding-top: 30px * @ratio;
  padding-bottom: 30px * @ratio;
}
// btitle 页面内部使用，非组件 class
.btitle {
  padding: @space_xl_32 0 @space_sm_12 @space_lg_24;
  color: @main_text_color;
  font-size: 30px * @ratio;
  display: block;
  font-weight: normal;
  position: relative;
  &:after {
    content: "";
    position: absolute;
    width: 10px * @ratio;
    height: 10px * @ratio;
    background: @blue_color;
    top: 78px * @ratio;
    left: 20px * @ratio;
  }
}
.btitle-gray {
  background: @main_bg_color;
}
.onetitle {
  font-size: 36px * @ratio;
  color: @main_text_color;
}

.line {
  height: 20px * @ratio;
  background: @main_bg_color;
}

.margin-20 {
  margin: 20px * @ratio;
}

.color-c {
  color: @four_text_color;
}

.color-main {
  color: @main_color;
}
.color-danger {
  color: @danger_color;
}
.color-success {
  color: @success_color;
}
.color-warn {
  color: @warn_color;
}
.color-main-text {
  color: @main_text_color;
}

.color-999 {
  color: @third_text_color;
}

.color-333 {
  color: @main_text_color;
}

.tc {
  text-align: center;
}
.tr {
  text-align: right;
}
.color-fff {
  color: @white_bg_color;
}
.y-lines {
  height: 16px * @ratio;
  background: @main_bg_color;
}

//结果页面
.result-p {
  margin-top: 40px * @ratio;

  p {
    text-align: center;
  }

  .title1 {
    font-size: 40px * @ratio;
    line-height: 60px * @ratio;
  }

  .title2 {
    font-size: 28px * @ratio;
    line-height: 50px * @ratio;
    color: @second_text_color;
    padding: 20px * @ratio;
  }

  .success_color {
    color: @success_color;
  }
}

/* -----------flex布局------------- */
.flex {
  display: flex;
  align-items: center;
}

.jcfe {
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-c-c {
  // 水平垂直居中
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.flex-c-sa,
.flex-c-a {
  // 垂直居中，水平环绕
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-around;
}

.flex-c-sb,
.flex-c-b {
  // 垂直居中，水平两端对齐
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

.flex-c-e {
  // 垂直居中，水平右对齐
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.flex-c-s {
  // 垂直居中，水平左对齐
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.flex-s-c {
  // 垂直居上，水平居中
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  justify-content: center;
}

.flex-s-e {
  // 自上而下，水平两端对齐
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  justify-content: space-between;
}

.flex-c-c-c {
  // 垂直对齐
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/******mr10表示margin-right:10px,其他样式依次类推******/
@list: 0, 2, 5, 6, 8, 10, 12, 16, 18, 20, 24, 30;
each(@list, {
  .mr@{value} {
    margin-right: @value * 1px;
  }
  .mt@{value} {
    margin-top: @value * 1px;
  }
  .mb@{value} {
    margin-bottom: @value * 1px;
  }
  .ml@{value} {
    margin-left: @value * 1px;
  }
  .pl@{value} {
    padding-left: @value * 1px;
  }
  .pt@{value} {
    padding-top: @value * 1px;
  }
  .pb@{value} {
    padding-bottom: @value * 1px;
  }
  .pr@{value} {
    padding-right: @value * 1px;
  }
});

/* -----------基础表单------------- */
.base-form {
  .van-cell {
    input,
    .van-field__error-message {
      text-align: right;
    }
  }

  .van-cell-group::after {
    border: none;
  }

  .y-select {
    position: relative;
    &::after {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      right: 16px;
      bottom: 0;
      left: 16px;
      border-bottom: 1px solid @four_text_color;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
    }
    .van-cell--required::before {
      // left: -8px;
    }
  }

  .y-select.last-select-cell::after {
    display: none;
  }
}
input:disabled::-webkit-input-placeholder {
  -webkit-text-fill-color: rgba(255, 255, 255, 0);
}

.show-placeholder {
  input:disabled::-webkit-input-placeholder {
    -webkit-text-fill-color: @four_text_color;
  }
}

/* -----------按钮样式一------------- */
.button-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 38px 16px;
  background-color: @second_border_color;
  .van-button {
    width: 164px;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
    line-height: 44px;
    &--info {
      border-radius: 22px;
      color: @main_color;
    }
  }
}

/* -----------按钮样式二------------- */
.button-box-more {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  background-color: @second_border_color;
  padding: 40px 16px 16px;
  .van-button {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
    line-height: 44px;
    &--info {
      border-radius: 22px;
      color: @main_color;
    }
    &:not(:last-child) {
      margin-bottom: 16px;
    }
  }
}

/* -----------按钮样式三------------- */
.button-box-only {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 38px 16px;
  background-color: @second_border_color;
  .van-button {
    width: 164px;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
    line-height: 44px;
    &--info {
      border-radius: 22px;
      color: @main_color;
    }
  }
}

/* -----------页面容器一------------- */
.page-container {
  padding: 16px;
  background-color: @second_border_color;
  .page-box {
    padding: 0;
    background-color: @white_text_color;
    margin-bottom: 16px;
    border-radius: 4px;
    padding: 4px 0 8px;
    .y-title {
      padding-left: 18px !important;
    }
  }
  .button-box {
    padding: 38px 16px;
    background-color: @second_border_color;
    .van-button {
      width: 164px;
      height: 44px;
      font-size: 16px;
      font-weight: 500;
      line-height: 44px;
      &--info {
        border-radius: 22px;
        color: @main_color;
      }
    }
  }
}

/* -----------页面容器二------------- */
.common-container {
  .y-title {
    margin: 0 16px;
  }
  .van-cell-group {
    padding: 0 16px;
  }
}

/* -----------底部分割线------------- */
.border-bottom-wide {
  border-bottom: 16px solid #f6f6f6;
}

.y-select::after {
  border-bottom: 0.5px solid #cecece !important;
}
.y-select-last::after {
  border-bottom: none !important;
}

.van-cell::after {
  border-bottom: 0.5px solid #cecece !important;
}
.van-cell-border-bottom::after {
  border-bottom: none !important;
}

.business-cell .van-cell__value {
  color: @main_text_color;
}

.van-field__body {
  & > textarea {
    text-align: right;
  }
  & > .van-field__button {
    width: 58px;
    height: 30px;
    .search-button {
      font-size: 14px;
      text-align: center;
      color: @white_text_color;
      background-color: @main_color;
      border-radius: 2px;
      padding: 0 4px;
    }
  }
}

.van-field-textarea {
  display: flex;
  flex-direction: column;
  padding: 16px;
  .van-cell__value {
    margin-top: 10px;
    .van-field__body > textarea {
      width: 100%;
      height: 104px;
      text-align: left;
      background: #f6f6f6;
      padding: 12px;
    }
    .van-field__error-message {
      display: none;
    }
  }
}

.van-field--disabled {
  .van-field__label {
    color: @main_text_color !important;
  }
  .van-field__value .van-field__body .van-field__control {
    color: #909399 !important;
  }
}

.alone-name {
  position: relative;
  ::before {
    position: absolute;
    content: "*";
    left: 8px;
    top: 6px;
    color: @danger_color;
  }
  ::after {
    border-bottom: none !important;
    .van-cell__title {
      width: 100%;
    }
    .van-cell__value {
      display: none;
    }
  }
  .van-cell__value {
    display: none;
  }
}

.laber-wider .van-field__label {
  width: 50%;
}

.van-cell.label-width .van-field__label {
  line-height: 20px;
}

.y-select.label-width .van-cell .van-field__label {
  line-height: 22px;
}

.writing-hidden {
  height: 30px;
  .van-cell__value {
    & > span {
      display: inline-block;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.common-required-select .van-cell {
  position: relative;
  &::before {
    position: absolute;
    left: 8px;
    color: #ff3b30;
    content: "*";
    top: 8px;
  }
}

.info-container {
  background: @background_gray_color;
  min-height: 100vh;
  padding-top: 1px;
}

.info-container-details {
  min-height: unset;
}

.info-button {
  color: #1658a0 !important;
  background: #e2f0ff;
  border: none;
}

.separate-box {
  height: 16px;
  background: #f6f6f6;
}

/* ----------------------------------------------
 * 文本溢出... / 文本对齐
 * ---------------------------------------------- */

// 创建显示... 函数
.textEllipsis(@r) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: @r;
  -webkit-box-orient: vertical;
}

// 单行显示...
.text-ellipsis,
.ellipsis {
  // 样式调用
  .textEllipsis(1);
}

// 2行显示...
.text-ellipsis-2,
.ellipsis-2 {
  // 样式调用
  .textEllipsis(2);
}

// 3行显示...
.text-ellipsis-3,
.ellipsis-3 {
  // 样式调用
  .textEllipsis(3);
}

// 文字两端对齐
.text-justify {
  text-align: justify;
  text-justify: inter-ideograph; // 兼容ie浏览器
}

// 文字两端对齐 适配ios部分机型失效，但是使用的人要自定义宽度、高度
.text-justify-inline {
  display: inline-block;
  width: 50px * @ratio; // 注意：使用自行调整宽度
  text-align: justify;
  // 设置固定高度，隐藏掉伪类
  overflow: hidden;
  height: 18px * @ratio; // 注意：使用自行调整高度
  // 创建伪类往下顶，使得text-align: justify;有效果
  &::after {
    content: "";
    display: inline-block;
    width: 100%;
  }
}

// 文字居中
.text-center,
.tc {
  text-align: center;
}

// 文字左居
.text-left,
.tl {
  text-align: left;
}

// 文字右居
.text-right,
.tr {
  text-align: right;
}

// 文本加粗
.fwb {
  font-weight: bold;
}

/********************文件上传**************************/
.uploader-container {
  .van-uploader__wrapper--disabled {
    opacity: 1;
  }
}
.uploader-container-alone {
  .van-uploader__wrapper--disabled {
    opacity: 1;
    .van-uploader__upload {
      display: none;
    }
  }
}

/********************益鹭保**************************/
.ylb-container {
  .van-tabs {
    &__nav {
      .van-tab--active {
        color: @ylb_color;
      }
    }

    &__line {
      background-color: @ylb_color;
    }
  }
  .content-box {
    padding-bottom: 60px;
  }
}

.ylb-page-wrapper {
  background-color: #f5f6f6;
  .y-title {
    padding-left: 24px !important;
    padding-right: 16px !important;
    margin-bottom: 0 !important;
  }
  .page-container {
    padding: 0 16px;
    background-color: #fff;
  }
}

.ylb-dialog-alert .van-dialog__footer .van-button {
  background: @ylb_color;
  color: @white_text_color;
}

.ylb-dialog-confirm
  .van-dialog__footer
  .van-dialog__confirm
  .van-button__content
  .van-button__text {
  color: @ylb_color;
}

.show-cell-group {
  .van-cell-group::after {
    border: none;
  }
  .van-cell__title > span {
    color: @six_text_color;
  }
  .van-cell__value > span {
    color: @main_text_color;
  }
  &::after {
    border: none;
  }
}

/* -----------卡片样式------------- */
.card-box {
  padding: 20px 16px 8px;
  background: @white_text_color;
  box-shadow: 0px 0px 8px 0px @white_shadow_color;
  border-radius: 12px;
  .card-title {
    font-weight: bold;
    font-size: 16px;
    color: @main_text_color;
    line-height: 22px;
  }
}
