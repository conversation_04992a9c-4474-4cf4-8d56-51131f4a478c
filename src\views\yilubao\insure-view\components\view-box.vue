<!--
 * @Description: 查看盒子
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <div class="content-box" v-if="formList.length > 0">
      <y-view-container :viewStatus="item.tbzt00" v-for="(item, index) in formList" :key="index" @handleCancel="handleCancel(item)" @handlePay="handlePay(item)">
        <template slot="title">
          <!-- 订单号 -->
          <span>{{ item.applicationNo }}</span>
        </template>
        <template slot="content">
          <van-cell-group>
            <van-cell title="操作时间" :value="optionData(item)" />
            <van-cell title="被保人" :value="da02VOData(item)['aac003']" />
            <van-cell title="身份证号" :value="da02VOData(item)['aac002']" />
            <van-cell title="手机号码" :value="da02VOData(item)['aae005']" />
            <van-cell title="保障期限">
              <template slot="extra" v-if="da02VOData(item)['bxqsrq']">
                {{ `${da02VOData(item)['bxqsrq']}至${da02VOData(item)['bxjzrq']}` }}
              </template>
            </van-cell>
            <van-cell class="money-cell" title="保费" :value="item['bfje00']" />
            <van-cell title="保单号" title-class="cell-title" :value="item['policyNo']" />
            <van-cell title="工作地区" :value="formatData(da02VOData(item)['aab301'], 'AAB301_XM')" />
            <van-cell title="用工平台" :value="formatData(da02VOData(item)['gzpt00'], 'GZPT00')" />
            <van-cell title="工作详细地址" :value="da02VOData(item)['ccd032']" />
            <van-cell title="用工时段" :value="da02VOData(item)['ygsd00']" />
            <van-cell title="用工名称" :value="da02VOData(item)['ygmc00']" />
            <van-cell title="有无社保" :value="formatData(da02VOData(item)['ywsb00'], 'YES_NO')" />
            <van-cell v-if="item.ywymc" title="业务员" :value="item.ywymc" />
            <van-cell v-if="item.rygh00" title="工号" :value="item.rygh00" />
            <van-cell v-if="item.da07aab004" title="保险公司" title-class="cell-title" :value="item.da07aab004" />
          </van-cell-group>
        </template>
      </y-view-container>
    </div>

    <y-empty v-else></y-empty>
  </div>
  
</template>

<script>
import { dayFormat } from "@/utils/dayjs"
export default {
  props: {
    formList: {
      type: Array,
      default: () => ([])
    }
  },
  computed: {
    optionData() {
      return (item) => {
        const time = this.da02VOData(item)["createTime"]
        if (!time) {
          return ""
        }
        return dayFormat(time, "YYYY-MM-DD HH:mm:ss")
      }
    },
    da02VOData() {
      return (item) => {
        const da02List= item?.da02VOList || []
        return da02List&&da02List.length >0 ? da02List[0]: {}
      }
    },
    formatData() {
      return (code, type) => {
        if (!code) {
          return ""
        }
        const { aaa103 } = this.$attrs.dictData[type]?.find(item => item.aaa102 === code) || {}
        return aaa103 || ""
      }
    }
  },
  methods: {
    handlePay(data) {
      this.$emit("handlePay", data)
    },
    handleCancel(data) {
      this.$emit("handleCancel", data)
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep.cell-title {
  flex: 0 0 30px;
}
.content-box {
  padding: 0 16px 60px;
  /deep/.money-cell .van-cell__value > span {
     color: @maney_color;
  }
}
</style>