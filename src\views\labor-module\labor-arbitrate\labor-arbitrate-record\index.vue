<!--
 * @Description: 劳动仲裁--记录
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="labor-arbitrate-record">

    <!-- 顶部tab -->
    <business-tabs :tabList="tabList" :showIcon="false" handleName="预约" @handleChangeTabs="handleChangeTabs" @handleAdd="handleAdd"></business-tabs>

    <!-- 列表 -->
    <div class="info-container">
      <van-list
        v-model="loading"
        :finished="finished"
        :finished-text="list?.length > 0 ? '没有更多了' : ''"
        @load="onLoad"
      >
        <div v-if="list?.length > 0">
          <info-box v-for="item in list" :key="item.cce010" title="受理状态" :moreText="getPlatformName(item.abb292).text" 
           :colorMore="getPlatformName(item.abb292).color" customMoreText>

            <template #cells>
              <van-cell title="被申请人" :value="item.bsqrName" :border="false" />
              <van-cell title="编号" :value="item.abb001" :border="false" />
              <van-cell title="申请日期" :value="dayFormatFn(item.abb277, 'date')" :border="false" />
              <van-cell title="受理机构" :value="getDictName(organList, item.zcy000)" :border="false" />
            </template>

            <template #buttons>
              <template v-if="item.abb292 === '000'">
                <van-button type="warning" class="info-button" @click="handleEdit(item)">编辑</van-button>
                <van-button type="warning" @click="handleDelete(item.bcz005)">删除</van-button>
              </template>     

              <template v-else>
                <van-button type="primary" @click="handleDetails(item.bcz005)">查看详情</van-button>
              </template>    
              
              <template v-if="item.abb292 === '003'">
                <van-button type="primary" @click="handleViewCase(item)">结案查看</van-button>
              </template>
            </template>

          </info-box>
        </div>         

        <y-empty v-else></y-empty>
      </van-list>
    </div>

    <!-- 选择仲裁委员会 -->
    <van-popup class="organ-popup" v-model="showOrganPopup" position="bottom">
      <div class="organ-list">
        <p v-for="item in organList" :key="item.value" @click="handleSelectOrgan(item)">{{item.label}}</p>
      </div>
    </van-popup>

    <van-popup class="commit-popup" v-model="showCommitPopup" position="center">
      <div class="commit-box">
        <div class="title">申请须知</div>
        <div class="content">
          <p>本入口为{{organName}}。</p>
          <p>仲裁事项线上预申请通道，在您提交预申请材料后，本委将在3个工作日内进行预审核，若审核通过，预申请人需在3个工作日内携带相关材料到本委立案窗口现场核实，逾期未到现场核实的，视为放弃预申请。预申请人提交预申请材料应当遵守诚实信用原则，对上传材料的合法性、真实性负责。</p>
        </div>
        <van-radio-group v-model="checkCommit">
          <van-radio name="check" :disabled="timer > 0">我已仔细阅读申请须知</van-radio>
          <div v-if="timer > 0" class="timer">倒计时{{timer}}S</div>
        </van-radio-group>        
      </div>
    </van-popup>
  </div>
</template>

<script>
import BusinessTabs from "@/components/business/business-tabs"
import InfoBox from "@/components/business/info-box"
import {
  status_one_color, status_two_color, status_three_color
} from "@/styles/theme/theme-params.less"

import { commonApi } from "@/api"
import {getDictName} from "@utils/common"

const ACCEPT_STATUS = { //人民调解状态
  "000": {
    text: "待提交",
    color: status_one_color
  },
  "001": {
    text: "已登记",
    color: status_two_color
  },
  "002": {
    text: "受理中",
    color: status_two_color
  },
  "003": {
    text: "已结案",
    color: status_three_color
  }
}    

export default {
  name: "labor-arbitrate-record",
  components: {
    BusinessTabs,
    InfoBox
  },
  data() {
    return {
      tabList: [
        {title: "正在受理", number: "0"},
        {title: "已结案", number: "0"}
      ],

      acceptType: "1", //1正在受理，2已结案

      // 信息列表
      titleObj: {
        title: "受理状态",
        platformName: "已结案"
      },

      list: [],
      loading: false,
      finished: false,
      searchParams: {
        page: 0,
        size: 3
      },

      // 用户信息
      userInfo: {
        ...this.$sessionUtil.getItem("userInfo")
      },

      // 字典列表
      platformList: [], //平台字典列表
      yesNoList: [], //是否同步参会字典列表

      // 仲裁委员会
      showOrganPopup: false,
      organId: "", //id
      organName: "", //名称
      organList: [], //列表

      // 申请须知
      showCommitPopup: false,
      checkCommit: "",

      timer: 5,
      setTimer: null
    }
  },  
  watch: {
    checkCommit(val) {
      if (val) {
        setTimeout(() => {
          this.handleCommit()

          this.showCommitPopup = false 
        }, 100)        
      }
    }
  },
  created() {
    this.getCountFn() // 获取总条数
    this.getPlatformList() //查询字典列表    
  },
  methods: { 
    setCountDown() {
      this.checkCommit = ""
      this.timer = 5
      clearInterval(this.setTimer)
      
      this.setTimer = setInterval(() => {
        this.timer--
        if (this.timer === 0) {
          clearInterval(this.setTimer)
        }
      }, 1000)      
    },
    // 查看字典对应名称
    getDictName,
    // 获取受理状态
    getPlatformName(abb292) {
      return ACCEPT_STATUS[abb292]
    }, 
    //查询字典列表
    getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["ZCY000"]
      }
      commonApi.proxyApi(params).then((res) => {
        const {data} = res.map
        const dictInfo = {
          "ZCY000": "organList"
        }
        for (const key in dictInfo) {
          this[dictInfo[key]] = data[key].map((item) => {
            return {label: item.aaa103, value: item.aaa102}
          })
        }

        console.log(this.organList, "organList#######")
      })
    },
    // 获取总条数 1正在受理，2已结案
    getCountFn() {
      this.getCount("1")
      this.getCount("2")
    },    
    getCount(slzt00) {
      const params = {
        serviceName: "xytBc05_findBc05ByPage",
        slzt00
      }
      commonApi.proxyApi(params).then((res) => { 
        const {total=0} = res.map.data
        if (slzt00 === "1") { //1正在受理
          this.tabList[0].number = total
        } else { //2已结案
          this.tabList[1].number = total
        }
      })
    },
    // 切换tab
    handleChangeTabs(index) {
      this.acceptType = index === 0 ? "1" : "2" //1正在受理，2已结案
      this.list = []
      this.searchParams.page = 1
      this.findBc05ByPage()
    }, 
    // 获取列表数据
    onLoad() {
      this.searchParams.page++
      this.findBc05ByPage()
    },
    // 查询列表信息
    findBc05ByPage() {
      const {acceptType: slzt00} = this
      const params = {
        serviceName: "xytBc05_findBc05ByPage",
        slzt00,
        ...this.searchParams
      }
      commonApi.proxyApi(params).then((res) => {
        const {rows=[], total=0} = res.map.data
        this.list = [...this.list, ...rows]

        this.loading = false

        if (this.list.length === total) {
          this.finished = true
        }
      }).catch(() => {
        this.loading = false
        this.finished = true
      })
    }, 
    
    // 新增预约
    handleAdd() {
      this.showOrganPopup = true
    },
    // 选择仲裁委员会
    handleSelectOrgan(data) {
      console.log(data, "data")
      const {label, value} = data
      this.organId = value
      this.organName = label
      this.showOrganPopup = false
      this.showCommitPopup = true    
      this.setCountDown()  
    },
    // 确定申请须知
    handleCommit() {
      const {organId} = this
      console.log(organId, "organId")
      this.$router.push({path: "/labor-arbitrate-handle", query: {pageType: "add", organId}})
    },

    // 查看详情
    handleDetails(bcz005) {
      console.log(bcz005, "查看详情")
      this.$router.push({path: "/labor-arbitrate-handle", query: {pageType: "details", bcz005}})
    },
    // 结案查看
    handleViewCase(data) {
      const {abb001} = data
      const bcz006 = data.bc06.bcz006
      this.$router.push({path: "/add-arbitrate/view-case", query: {bcz006, abb001}})
    },
    // 编辑
    handleEdit(item) {
      console.log(bcz005, "编辑")
      const {zcy000: organId, bcz005} = item
      this.$router.push({path: "/labor-arbitrate-handle", query: {pageType: "edit", organId, bcz005}})
    },
    // 删除
    handleDelete(bcz005) {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定删除",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        commonApi.proxyApi({
          serviceName: "xytBc05_deleteBc05ByIds",
          ids: [bcz005]
        }).then((res) => {
          console.log("已删除！", res)
          this.$toast("已删除！")
          this.onLoad()     
        }).catch((err) => {
          console.error(err)
        })
      })  
    }
  }
}
</script>

<style lang="less" scoped>
.labor-arbitrate-record {
  .info-container {
    background: @background_gray_color;  
    min-height: calc(100vh - 44px);
    /deep/.info-box {
      .y-title .content .more {
        position: relative;
        &::before {
          content: ' ';
          width: 8px;
          height: 8px;
          border-radius: 4px;
          background: @main_color;
          position: absolute;
          top: 50%;
          left: -25%;
          transform: translate(0,-50%);
        }
      }
      .van-cell-group .van-cell {
        .van-cell__title {
          flex: auto;
          width: 30%;
        }
        .van-cell__value {
          flex: auto;
          width: 70%;
        }
      }
    }
    /deep/.handle-cell .van-cell__value {
      color: @main_color !important;
    }    
  }
  /deep/.organ-popup {
    height: 380px;
    .organ-list > p {
      font-size: 14px;
      color: #303133;
      line-height: 20px;
      text-align: center;
      margin: 30px;
    }
  }
  /deep/.commit-popup {
    width: 80%;
    border-radius: 12px;
    .commit-box {
      padding: 16px;
      
      .title {
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        text-align: center;
      }
      .content {
        margin-top: 20px;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-bottom: 20px;
        text-indent: 2em;
      }
      .timer {
        font-size: 14px;
        text-align: center;
        margin-top: 8px;
        color: #FA7E00;
      }
      .van-radio {
        display: flex;
        justify-content: center;
        align-items: center;
        .van-radio__label {
          font-size: 14px;
        }
      }      
    }
  }
}
 
</style>