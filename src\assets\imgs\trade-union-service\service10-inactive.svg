<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组</title>
    <g id="app-首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="补充图标" transform="translate(-554.000000, -1046.000000)" fill-rule="nonzero">
            <g id="编组-8备份-6" transform="translate(535.000000, 1036.000000)">
                <g id="编组" transform="translate(19.000000, 10.000000)">
                    <path d="M7.9997866,0 C3.5908133,0 0,3.59090909 0,8 C0,12.4090909 3.5908133,16 7.9997866,16 C12.4087599,16 15.9995732,12.4090909 15.9995732,8 C16.0450265,3.59090909 12.4542132,0 7.9997866,0 Z" id="路径" fill="#CECECE"></path>
                    <path d="M4.74727654,3 L4.74727654,10.5720985 C4.74727653,11.1242673 5.03688418,11.6380096 5.50245771,11.9140289 L5.50245771,11.9140289 L7.33379614,13 L4.32077597,13 C3.59256064,13 3,12.3885632 3,11.6370978 L3,11.6370978 L3,4.36290218 C3,3.6113065 3.59242305,3 4.32077597,3 L4.32077597,3 L4.74727654,3 Z M5.49511331,2.14740039 C5.80739192,1.95146235 6.18074346,1.95082578 6.49379028,2.1457453 L6.49379028,2.1457453 L8.26823439,3.22003069 L8.26823439,13 L8.23609748,13 L5.70253083,11.4801115 C5.26887457,11.2105853 5,10.7080723 5,10.1687653 L5,10.1687653 L5,3.07590992 C5,2.68899912 5.18513935,2.34193798 5.49511331,2.14740039 Z M12.7708591,3.2260145 C13.4485511,3.2260145 14,3.82350459 14,4.55811311 L14,4.55811311 L14,11.6679014 C14,12.4023826 13.4486791,13 12.7708591,13 L12.7708591,13 L9.09764841,13 L9.09764841,3.2260145 Z" id="形状结合" fill="#FFFFFF"></path>
                </g>
            </g>
        </g>
    </g>
</svg>