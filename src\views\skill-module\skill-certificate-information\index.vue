<!--
 * @Description: 新就业形态劳动者技能证书信息
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="skill-record-query">
    <div class="record-box" v-if="list.length > 0">
      <div class="record-item" v-for="(item,index) in list" :key="index">
        <div class="item-header flex-c-s">
          <p>{{item.gzmc00}}</p>
        </div>

        <van-form class="base-form" disabled>
          <van-cell-group class="show-cell-group">
            <van-field label="姓名" v-model="item.aac003" name="aac003"/>
            <van-field label="身份证号" v-model="item.aac002" name="aac002"/>
            <van-field label="工种名称" v-model="item.gzmc00" name="gzmc00"/>
            <y-select-dict v-model="item.jnjdlx" label="证件类型" dict-type="JZBT_ZSLX00" disabled />
            <y-select-dict v-model="item.dj0000" label="证书等级" dict-type="JZBT_DJBM00" disabled />
            <van-field label="证书编号" v-model="item.zsbh00" name="zsbh00" />
            <van-field label="发证日期" v-model="item.fzrq00" name="fzrq00" />
          </van-cell-group>
        </van-form>
      </div>
    </div>

    <y-empty v-else tips="暂无技能证书信息" />
  </div>
</template>

<script>
import { commonApi } from "@/api"
export default {
  name: "skill-record-query",
  data() {
    return {
      list: []
    }
  },
  computed: {
    userInfo() {
      const {xm0000: aac003, zjhm00: aac002} = this.$sessionUtil.getItem("userInfo")
      return {aac003, aac002}
    }
  },
  created() {
    this.getXytpxqk()
  },
  methods: {
    getXytpxqk(){
      const {aac002} = this.userInfo
      commonApi.proxyApi({
        serviceName: "xytJnpx_findJnzsxx",
        aac002,
        "page": 1,
        "size": 100
      }).then((res) => {
        console.log("培训记录", res)
        const { rows } = res.map?.data
        this.list = rows || []
      })
    }
  }
}
</script>
<style lang="less" scoped>
.skill-record-query {
  padding: 16px;
  background: #F6F6F6;
  min-height: 100vh;
  .record-box {
    .record-item {
      margin-bottom: 16px;
      .item-header {
        width: 100%;
        height: 48px;
        background-image: url("~@/assets/imgs/skill-record-query/<EMAIL>");
        background-size: 100% 100%;
        padding-left: 16px;
        & > p {
          font-weight: 500;
          font-size: 16px;
          color: #FFFFFF;
          line-height: 16px;
        }
      }

    }
  }
}
</style>