<!--
 * @Description: 人民调解
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-protect">
    <div class="header-box">
      <img :src="require('@pic/labor-protect/<EMAIL>')" alt="">
    </div>
    <div class="info-container">
      <div class="business-container">
        <y-title content="业务办理" fontWeight="bold"></y-title>
        <div class="business-box flex-c-sb mt30 pl16 pr16">
          <div class="business-item flex-c-c-c" v-for="(item,index) in businessList" :key="index" @click="handleJump(item)">
            <img :src="item.imgUrl" alt="">
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>

      <div class="business-container mt20">
        <y-title content="地图服务" fontWeight="bold"></y-title>
        <div class="business-box flex-c-sb mt30 pl16 pr16">
          <div class="business-item flex-c-c-c" v-for="(item,index) in mapServiceList" :key="index" @click="handleJump(item)">
            <img :src="item.imgUrl" alt="">
            <div class="item-title">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: "labor-protect",
  data() {
    return {
      businessList: [
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "人民调解",
          path: "/labor-protect-record"
        },
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "劳动监察",
          path: "/labor-supervision-record"
        },
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "劳动仲裁",
          path: "/labor-arbitrate-record"
        }
      ],
      mapServiceList: [
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "调解组织",
          path: "/map-service",
          type: "mediate"
        },
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "法律援助",
          path: "/map-service",
          type: "law"
        },
        {
          imgUrl: require("@pic/labor-protect/<EMAIL>"),
          title: "仲裁机构",
          path: "/map-service",
          type: "arbitration"
        }
      ]
    }
  },
  methods: {
    // 跳转办理
    handleJump(item) {
      const { path, type="" } = item
      const urlParams = type ? {path, query: {type}} : path
      this.$router.push(urlParams)
    }
  }
}
</script>

<style lang="less" scoped>
.labor-protect {
  .header-box {
    width: 100%;
    & > img {
      width: 100%;
    }
  }
  .info-container {
    padding: 24px 16px;
    min-height: calc(100vh - 202px);
    .business-container {
      background-color: @white_bg_color;
      padding: 8px 18px 24px;
      border-radius: 4px;
      .business-box .business-item {
        & > img {
          width: 44px;
        }
        .item-title {
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
          margin-top: 12px;
          text-align: center;
        }
      }
    }
  }
  /deep/.organ-popup {
    .organ-list > p {
      font-size: 14px;
      color: #303133;
      line-height: 20px;
      text-align: center;
      margin: 30px;
    }
  }
}

</style>