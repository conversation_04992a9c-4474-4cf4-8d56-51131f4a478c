<!--
 * @Description: 全部服务
 * @Author: wujh
 * @date: 2024/5/11 16:11
 * @LastEditors: wujh
-->
<template>
  <div class="service-container">
    <div class="card-container" v-for="(item, key) in menus" :key="key">
      <y-title
        :content="item.title"
        fontWeight="bold"
        pleft="18"
        font-cont-size="14"
        m-bottom="0"
      />
      <van-divider />
      <div class="card-body">
        <div
          v-for="(subItem, subKey) in item.children"
          :key="key + '_' + subKey"
          class="menu-item"
          :class="
            subItem.occupyRow
              ? 'menu-item occupy-row-item'
              : 'menu-item'
          "
          @click="menuClickFn(subItem)"
        >
          <wx-open-launch-weapp
            v-if="subItem.toSmallProgram"
            id="launch-btn"
            :appid="subItem.miniconfig.appid"
            :path="subItem.miniconfig.path"
          >
            <script type="text/wxtag-template">
              <style>
                .btn {
                  background: #F8E8EA;
                  color: #BD1A2D;
                  line-height: 34px;
                  font-size: 14px;
                  text-align: center;
                  border: none
                }
              </style>
              <button class="btn">{{subItem.title}}</button>
            </script>
          </wx-open-launch-weapp>
          <span v-else>{{ subItem.title }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {commonApi} from "@api"

import { isValidUrl } from "@/utils/str-util"
import { setWxConfig } from "@/utils/wechat"

export default {
  name: "service-all",
  data() {
    return {
      menus: [
        {
          title: "我要备案",
          children: [
            {
              title: "信息备案查看",
              path: "/info-filings"
            }
          ]
        },
        {
          title: "我要维权",
          children: [
            {
              title: "人民调解",
              path: "/labor-protect-record"
            },
            {
              title: "劳动监察",
              path: "/labor-supervision-record"
            },
            {
              title: "劳动仲裁",
              path: "/labor-arbitrate-record"
            }
          ]
        },
        {
          title: "我要投保",
          children: [
            {
              title: "益鹭保投保",
              path: "/yilubao"
            }
          ]
        },
        {
          title: "掌上分销",
          children: [
            {
              title: "掌上分销",
              path: "/palmar-distributor"
            }
          ]
        },
        {
          title: "工会服务",
          children: [
            // {
            //   title: "总览",
            //   // path: "https://weixin.xmgh.org/xmzgh/m/gh/services/map.html?type=0"
            //   path: "https://weixin.xmgh.org/xmzgh/m/gh/services/?TYPE=0&COORDINATE="
            // },
            // {
            //   title: "服务站点",
            //   path: "https://weixin.xmgh.org/xmzgh/m/gh/services/?TYPE=4&COORDINATE="
            // },
            // {
            //   title: "工会爱心驿站",
            //   path: "https://weixin.xmgh.org/xmzgh/m/gh/services/?TYPE=1&COORDINATE="
            // },
            // {
            //   title: "福建爱心驿站",
            //   path: "https://weixin.xmgh.org/xmzgh/m/gh/services/?TYPE=3&COORDINATE="
            // },
            // {
            //   title: "特惠商家",
            //   path: "https://weixin.xmgh.org/xmzgh/m/gh/services/?TYPE=2&COORDINATE="
            // },
            // {
            //   title: "共享职工之家",
            //   path: "https://weixin.xmgh.org/xmzgh/m/gh/services/?TYPE=5&COORDINATE="
            // },       
            {
              title: "暖心驿站",
              path: "/love-station",
              query: {
                businessType: "006"
              }
            },     
            {
              title: "环卫爱心驿站",
              path: "/love-station"
            },
            {
              title: "近邻图书馆",
              path: "/nearby-library"
            }
            // {
            // title: "文明办爱心餐",
            // path: "https://axdt.xmnncloud.cn/frontend/"
            // },
            // {
            // title: "职工书屋",
            // path: "https://content.dzzgsw.com/weixin/visit.html?appid=xmszgh"
            //},
          //   {
          //     title: "职工学堂",
          //     path: "https://i.xmgh.org/jyh5/home/<USER>"
          //   }
          ]
        },
        {
          title: "技能提升",
          children: [
            {
              title: "技能补贴申请",
              path: "/skill-subsidies"
            },
            {
              title: "新就业形态云课堂",
              path: "/on-line-study"
            },
            {
              title: "培训记录查询",
              path: "/skill-record-query"
            },
            {
              title: "工种培训机构情况",
              path: "/skill-job-organ-query"
            },
            {
              title: "证书信息查询",
              path: "/skill-certificate-information"
            }
          ]
        },
        {
          title: "求职招聘",
          children: [
            {
              title: "热招岗位",
              path: "/post-recruit"
            },
            {
              title: "求职招聘",
              toSmallProgram: true, // 跳转小程序标识
              miniconfig: {
                // 跳转目标小程序参数配置
                appid: "wxa91c3e3e1a4be1f4",
                path: "/pages/home/<USER>"
              }
            }
          ]
        },
        {
          title: "骑手服务",
          children: [
            {
              title: "骑手服务",
              path: "/rider"
            }
          ]
        },
        {
          title: "职业伤害保险",
          children: [{ title: "职业伤害保险查看" }]
        },
        {
          title: "工伤保险",
          children: [
            {
              title: "工伤保险待遇查看",
              path: "https://app.hrss.xm.gov.cn/SBServer/gsbx/dyxx/list"
            },
            {
              title: "工伤保险缴费情况",
              path: "https://app.hrss.xm.gov.cn/SBServer/info/index"
            },
            {
              title: "工伤保险定期待遇认证",
              path: "https://app.hrss.xm.gov.cn/SBServer/gs/index"
            },
            {
              title: "工伤保险协议机构查询",
              path: "https://app.hrss.xm.gov.cn/SBServer/web/gsbxxyjgcx"
            },
            {
              title: "工伤待遇模拟计算器",
              path: "https://app.hrss.xm.gov.cn/SBServer/gsbx/dyjsq/index",
              occupyRow: true
            },
            {
              title: "出具劳动能力鉴定结论书",
              path: "https://app.hrss.xm.gov.cn/SBServer/gsbx/ldnljd/jlsdy",
              occupyRow: true
            },
            {
              title: "出具工伤保险待遇通知单",
              path: "https://app.hrss.xm.gov.cn/SBServer/toNeedFaceOrPwd",
              occupyRow: true
            }
          ]
        }
        // {
        //   title: "其他政务服务",
        //   children: [
        //     {
        //       title: "积分入学查询申请",
        //       path: "https://gxyq.xmedu.cn/oxford-ijysso-front/index_wx/index.html#/"
        //     },
        //     {
        //       title: "保障房查询申请",
        //       path: "https://zfbz.szjj.xm.gov.cn:9083/mobile/#/pages/wfw/wfw"
        //     },
        //     {
        //       title: "居住证办理",
        //       path: "https://www.ixiamen.org.cn/residence/wx/"
        //     }
        //   ]
        // }
      ]
    }
  },
  created() {
    setWxConfig() // 微信SDK签名配置
  },
  methods: {
    async menuClickFn(item) {
      const { toSmallProgram, path, query={} } = item
      if (!path) { // 未开发功能
        this.$toast("功能建设中，敬请期待!")
        return
      }

      if (toSmallProgram) { // 跳转小程序        
        return
      }

      if (isValidUrl(path)) { // 跳转外部链接
        window.open(path)
        return
      }

      if (path === "/palmar-distributor") { //掌上分销 校验是否是分销业务员
        const res = await commonApi.proxyApi({serviceName: "xytPerson_getDa05ByCurrentUser"})
        const {isSalesman} = res.map.data || {}
        if (isSalesman === "0") {
          this.$dialog.alert({
            title: "提示",
            message: "非承保公司业务人员，暂无权限!",
            theme: "round-button"
          })
          return
        }
      }

      this.$router.push({path, query}) // 跳转路由
    }
  }
}
</script>

<style scoped lang="less">
.service-container {
  min-height: 100vh;
  padding: 16px;
  background-color: #f6f6f6;
  .card-container {
    margin-bottom: 16px;
    .card-body {
      display: flex;
      flex-flow: wrap;
      justify-content: space-between;
      padding: 6px 10px 18px;
      border-radius: 2px;
      background-color: #fff;
      .menu-item {
        flex: 1;
        width: 0;
        max-width: calc(50% - 6px);
        min-width: 40%;
        margin-top: 12px;
        background: #f8e8ea;
        border-radius: 4px;
        color: #bd1a2d;
        text-align: center;
        line-height: 34px;
        font-size: 14px;
        &.occupy-row-item {
          max-width: unset;
          flex: unset;
          width: 100%,
        }
      }
    }
    .van-divider {
      margin: 0;
      border-color: #eeeeee;
    }
  }
}
</style>
