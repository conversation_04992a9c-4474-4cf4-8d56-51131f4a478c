<!--
 * @Description: 查看盒子
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <div class="content-box" v-if="formList.length > 0">
      <y-view-container v-for="(item, index) in formList" :key="index" :viewStatus="item.tbzt00" :showBtn="false" pageSize="mini" @handleCancel="handleCancel(item)" @handlePay="handlePay(item)">
        <template slot="title">
          <!-- 订单号 -->
          <span>{{ item.applicationNo }}</span>
        </template>
        <template slot="content">
          <van-cell-group>
            <van-cell title="操作时间" :value="item.createTime" />
            <van-cell title="投保人" :value="item.dac003" />
            <van-cell title="被保人数" :value="item.da02VOList?.length" />
            <van-cell title="保障期限">
              <template slot="extra" v-if="item.bxqsrq">
                {{ `${item.bxqsrq}至${item.bxjzrq}` }}
              </template>
            </van-cell>

            <van-cell class="money-cell" title="保费" :value="item.bfje00" />   
            <van-cell title="手机号码" :value="item.aae005" /> 
            <van-cell title="保单号" title-class="cell-title" :value="item.policyNo" />
            <van-cell title="工作地区" :value="formatData(item.aab301, 'AAB301_XM')" />
            <van-cell title="用工平台" :value="formatData(item.gzpt00, 'GZPT00')" />
            <van-cell title="工作详细地址" :value="item.ccd032" />
            <van-cell title="用工时段" :value="item.ygsd00" />
            <van-cell title="用工名称" :value="item.ygmc00" />
            <van-cell title="有无社保" :value="formatData(item.ywsb00, 'YES_NO')" />
            <van-cell v-if="item.ywymc" title="业务员" :value="item.ywymc" />
            <van-cell v-if="item.rygh00" title="工号" :value="item.rygh00" />
            <van-cell v-if="item.da07aab004" title="保险公司" title-class="cell-title" :value="item.da07aab004" />
          </van-cell-group>
        </template>
      </y-view-container>
    </div>

    <div v-else class="card-box mt12">
      <y-empty></y-empty>
    </div>    
  </div>
  
</template>

<script>
export default {
  props: {
    formList: {
      type: Array,
      default: () => ([])
    }
  },
  computed: {
    formatData() {
      return (code, type) => {
        if (!code) {
          return ""
        }
        const { aaa103 } = this.$attrs.dictData[type]?.find(item => item.aaa102 === code) || {}
        return aaa103 || ""
      }
    }
  },
  methods: {
    handlePay(data) {
      this.$emit("handlePay", data)
    },
    handleCancel(data) {
      this.$emit("handleCancel", data)
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep.cell-title {
  flex: 0 0 30px;
}
.content-box {
  /deep/.money-cell .van-cell__value > span {
     color: @maney_color;
  }
}
</style>