<!--
 * @Description: 编辑
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="page-container">
    <van-cell-group inset >
      <y-select-dict v-model="formData.aab301" label="工作地区" :required="true" :rules="formRules.aab301" dict-type="AAB301_XM" is-link />
      <y-select-dict v-model="formData.gzpt00" label="用工平台" :required="true" :rules="formRules.gzpt00" dict-type="GZPT00" is-link />
    
      <van-field
        v-model="formData.ccd032"
        name="ccd032"
        label="工作详细地址"
        placeholder="请输入"
        type="textarea"      
        input-align="left"
        :autosize="{ maxHeight: 70, minHeight: 50 }"
        :required="required"
        :rules="formRules.ccd032"        
      />
      <time-selection v-model="formData.ygsd00"></time-selection>
      <!-- 校验只能填中文 -->
      <van-field
        v-model="formData.ygmc00"
        name="aac003"
        label="用工名称"
        placeholder="请输入"
        :required="required"
        :rules="formRules.aac003"
      />
      <y-select-dict v-model="formData.ywsb00" label="是否有社保" :rules="formRules.ywsb00" dict-type="YES_NO" is-link />
    </van-cell-group>
  </div>
</template>

<script>
import timeSelection from "@/views/yilubao/components/time-selection"

export default {
  model: {
    prop: "formData"
  },
  props: {
    formData: {
      type: Object,
      require: true
    },
    formRules: {
      type: Object,
      require: true
    }
  },
  components: {
    timeSelection
  },
  data() {
    return {
      required: false
    }
  },
  methods: {
    
  }
}
</script>

<style lang="less" scoped>
/deep/.van-field__control--custom {
  display: flex;
  justify-content: flex-end;
}
</style>