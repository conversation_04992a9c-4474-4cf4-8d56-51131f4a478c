<!--
 * @Description: 益鹭保
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="yilubao">
    <!-- 投保/投保查看/理赔查看 -->
    <component :is="componentName"></component>
    <!-- 底部tab -->
    <van-tabbar v-model="active" @change="handleChange">
      <van-tabbar-item v-for="(item , key) in iconList" :key="key" @click="handleClickTab">
        <span>{{ item.name }}</span>
        <template #icon="props">
          <img :src="props.active ? item.active : item.inactive" />
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
// pages
import InsureAgainst from "./insure-against/index"
import InsureView from "./insure-view/index"
import ClaimsCheck from "./claims-check"

// icons
import insureAgainstActive from "@pic/yilubao/icons/<EMAIL>"
import insureAgainst from "@pic/yilubao/icons/<EMAIL>"
export default {
  components: {
    ClaimsCheck,
    InsureAgainst,
    InsureView
  },
  data() {
    return {
      componentName: "InsureAgainst",
      active: 0,
      iconList: [
        {
          name: "我要投保",
          inactive: insureAgainst,
          active: insureAgainstActive
        }
      ]
    }
  },
  methods: {
    handleChange(activeVal) {
      this.scrollToTop(0, 0, "auto") //滚动到顶部
      if (activeVal === this.iconList.length - 1) {
        return
      }

      const components = ["InsureAgainst", "InsureView", "ClaimsCheck"]
      this.componentName = components[activeVal]
    },
    handleClickTab() {
      console.log(this.active)
      console.log(this.iconList.length)
      // if (this.active === this.iconList.length - 1) {
      //   this.$toast("功能建设中,敬请期待！")
      //   return
      // }
    }
  }
}
</script>

<style lang="less" scoped>
.yilubao {
  padding-bottom: 80px;
}
  /deep/ .van-tabbar-item {
    color: #333333;
    &--active {
      font-weight: 500;
    }
  }
  /deep/ .van-tabbar {
    height: 89.98px;
    .van-tabbar-item__icon {
      width: 24px;
      height: 24px;
      img {
        height: 24px;
      }
    }
  }

</style>
