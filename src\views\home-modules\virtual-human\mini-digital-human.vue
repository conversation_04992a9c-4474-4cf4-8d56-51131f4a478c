<template>
  <div class="mini-digital-human">
    <div class="virtual-human-top" @click="showConfirmDialog = true"></div>
    <!-- 消息展示区 -->
    <MessageBox
      ref="messageBox"
      :messages="messages"
      :isStreaming="isStreaming"
      :lastStatus="lastStatus"
      :guessQuestions="guessQuestions"
      :virtualHuman="true"
      @sendGuessQuestion="sendGuessQuestion"
      @handleVolume="handleVolume"
    ></MessageBox>
    <!-- 输入框区域 -->
    <SearchBox
      ref="searchBox"
      :messageBoxRef="$refs.messageBox"
      :virtualHuman="true"
      :messages="messages"
      :isPlaying="isPlaying"
      :forcePaused="forcePaused"
      :switchMode="true"
      @sendMessage="val => messages = val"
      @update:isStreaming="val => isStreaming = val"
      @update:lastStatus="val => lastStatus = val"
      @toNewDigitalHuman="showConfirmDialog = true"
      @guessQuestions="val => guessQuestions = val"
      @getVoiceData="val => sentMessage(val)"
      @stopAnswer="stopAnswer"
    ></SearchBox>
    <div v-if="showConfirmDialog" class="confirm-container">
      <div class="confirm-box">
        <img src="~@pic/home/<USER>/confirm.png" class="confirm-bg">
        <img src="~@pic/home/<USER>/confirm-text.png" class="confirm-text">
        <div class="confirm-btn" @click="confirmSwitch">确认切换</div>
        <div class="close-btn" @click="showConfirmDialog = false"></div>
      </div>
    </div>
    <div id="wrapper" class="digital-human-wrapper" style="display: none;"></div>
  </div>
</template>

<script>
import digitalHumanMixin from "./mixins"
import MessageBox from "@/views/home-modules/components/message-box/index.vue"
import SearchBox from "@/views/home-modules/components/search-box/index.vue"
import { setMessages } from "@/utils/cookie"

export default {
  mixins: [digitalHumanMixin],
  components: {
    MessageBox,
    SearchBox
  },
  data() {
    return {
      isStreaming: false,
      lastStatus: false,
      isPlay: true,
      showConfirmDialog: false,
      guessQuestions: [],
      type: "new"
    }
  },
  watch: {
    messages: {
      handler(newVal) {
        if (newVal && newVal.length) {
          setMessages(this.type, JSON.stringify(newVal))
        }
      },
      deep: true
    }
  },
  methods: {
    setGuessQuestions() {
      this.guessQuestions = []
    },
    confirmSwitch() {
      window.speechSynthesis && window.speechSynthesis.cancel()
      this.showConfirmDialog = false
      this.$emit("changeLongVoice", true)
    }    
  }
}
</script>

<style lang="less" scoped>
.mini-digital-human {
  height: 100%;
  background: transparent url("~@pic/home/<USER>/virtual-human-bg.png") no-repeat center / 100% 100%;
  padding-top: 30px;

  .virtual-human-top {
    width: 50%;
    height: 25px;
    position: absolute;
    top: 0;
    left: 25%;
    clip-path: polygon(0 0, 100% 0, 50% 100%);
    background: transparent url("~@pic/home/<USER>/virtual-human-top.png") no-repeat center / auto 25px;
  }
}

.confirm-container {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  z-index: 99;

  .confirm-box {
    width: 100%;
    position: relative;

    .confirm-bg {
      width: 100%;
    }

    .confirm-text {
      width: 70%;
      position: absolute;
      top: 42px;
      left: 60px;
    }
    
    .confirm-btn {
      font-size: 20px;
      color: #0194FF;
      padding: 8px 20px;
      position: absolute;
      bottom: 80px;
      left: calc(50% - 20px);
      border-radius: 25px;
      border: 0.5px solid rgba(95,255,255,0.75);
      background: linear-gradient(180deg,#ffffff, #d5fff7 80%, #90ffeb 100%);
      box-shadow: 0px 0px 20px 0px rgba(65,255,198,0.42), 0px 0px 15px 0px rgba(65,255,198,0.47);
    }

    .close-btn {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      position: absolute;
      bottom: 2px;
      left: calc(50% - 15px);
    }
  }
}
</style>