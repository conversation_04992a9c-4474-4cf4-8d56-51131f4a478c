<!--
 * @Description: 劳动监察--撤回申请
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="revoke-box">    
    
    <van-form class="base-form" @failed="onFailed" @submit="handleSubmit">
      <y-title content="撤回申请"/>
      <van-cell-group inset>

        <van-field
          v-model="formData.aac003"
          name="aac003"
          label="申请人姓名"
          placeholder="请输入"
          disabled
          :required="true"
        />

        <y-select-dict class="revoke-select-dict" :required="true" v-model="formData.aac004" :filterabled="false"
        dict-type="AAC004" label="性别" is-link disabled />

        <van-field                   
          v-model="formData.abb919"
          name="abb919"
          label="年龄"
          placeholder="请输入"
          disabled
          :required="true"
        />

        <van-field
          v-model="formData.abb281"
          name="abb281"
          label="联系电话"
          placeholder="请输入"
          :required="true"
          :rules="formRules.abb281"
        />   

        <van-field
          v-model="formData.aac147"
          name="aac147"
          label="证件号码"
          placeholder="请输入"
          disabled
          :required="true"
        />

        <van-field                   
          v-model="formData.aac151"
          name="aac151"
          label="户籍地址"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aac151"
        />

        <van-field                   
          v-model="formData.abf013"
          name="abf013"
          label="联系地址"
          placeholder="请输入"
          :required="true"
          :rules="formRules.abf013"
        />

        <van-field                   
          v-model="formData.aab004"
          name="aab004"
          label="被申请单位"
          placeholder="请输入"
          disabled
          :required="true"
        />

        <van-field                   
          v-model="formData.abb922"
          name="abb922"
          label="申请时间"
          placeholder="请输入"
          disabled
          :required="true"
        />

        <van-field                   
          v-model="formData.aab513"
          name="aab513"
          label="单位电话"
          placeholder="请输入"
          :required="true"
        />

        <van-field                   
          v-model="formData.aae006"
          name="aae006"
          label="单位地址"
          placeholder="请输入"
          :required="true"
        />
      <div class="separate-box"></div>
        
      </van-cell-group>

      <y-title content="撤回理由" />
      <van-cell-group inset >
        <van-field
          class="van-field-textarea"
          v-model="formData.abf012"
          name="abf012"
          label="撤回理由"
          placeholder="请输入撤回理由"
          type="textarea"
          :required="true"
          :rules="formRules.abf012"
        />
      </van-cell-group>
      
      <div class="button-box mt18">
        <van-button plain type="info" @click="handleCancle" native-type="button">
          取 消
        </van-button>
        <van-button round block type="primary" native-type="submit">
          提 交
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script>
import {commonApi} from "@/api"
import {checkMobile} from "@utils/check"

export default {
  name: "labor-supervision-revoke",
  data() {
    return {
      formData: {
        aac003: "", //申请人姓名 
        aac004: "", //性别 
        abb919: "", //年龄 
        abb281: "", //联系电话 
        aac147: "", //证件号码 
        aac151: "", //户籍地址 
        aab004: "", //被申请单位 
        abb922: "", //申请时间 
        aab513: "", //单位电话 
        aae006: "", //单位地址 

        abf001: "", //申请人姓名
        aac004cn: "", //性别
        abf003: "", //年龄
        abf004: "", //联系电话 
        abf006: "", //证件号码 
        abf005: "", //户籍地址 
        abf013: "", //联系地址 
        abf007: "", //被申请单位
        abf011: "", //申请时间 
        abf009: "", //单位电话 
        abf010: "", //单位地址 
        abf012: "" //撤回理由
      },
      formRules: {
        abb281: [
          { required: true, message: "请输入" },
          {
            validator: checkMobile,
            message: "请输入正确的联系电话",
            trigger: "onBlur"
          }
        ],
        abf012: [{ required: true, message: "请输入" }],
        abf013: [{ required: true, message: "请输入" }],
        aab513: [{ required: true, message: "请输入" }],
        aae006: [{ required: true, message: "请输入" }],
        aac151: [{ required: true, message: "请输入" }]
      }
    }
  },
  computed: {
    
  },
  created() {
    this.getBaseInfo()
  },
  methods: { 
    getBaseInfo() {
      const {id, aae391} = this.$route.query
      const params = {
        serviceName: "jftjsq_enterCheck",
        id,
        aae391
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "******res")
        const {objMap={}} = res.map
        objMap.aac004 = objMap.aac004 === "0" ? "2" : "1" //表不一样 字典需转化
        this.formData = {...this.formData, ...objMap}
      })
    },
    formatterTime(time) {
      return time.substring(0, 10)
    },   
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },
    // 取消
    handleCancle() {
      this.$router.go(-1)
    },
    // 提交撤回
    handleSubmit() { 
      this.$dialog.confirm({
        title: "提示",
        message: "您确定撤回？",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        this.submitFn()
      })      
    },
    // 提交
    submitFn() {
      const {id, aae391, type} = this.$route.query
      // 表结构不一样 字段需要转化     
      const { aac003, aac004, abb919, abb281, aac147, aac151, aab004, abb922, aab513, aae006, abf013} = this.formData
      const otherParams = {
        abf001: aac003,
        aac004cn: aac004,
        abf002: aac004,
        abf003: abb919,
        abf004: abb281,
        abf006: aac147,
        abf005: aac151,
        abf007: aab004,
        abf011: abb922,
        abf009: aab513,
        abf010: aae006,

        id,
        aae391,
        abz115: id,
        abf000: id,
        abf008: this.dayFormatFn(new Date(), "YYYYMMDD"),

        type, // 撤回类型 type：撤诉1 撤回2
        abf013,
        abf014: type === "1" ? abf013 : "",
        ly0000: type === "1" ? "撤诉理由" : "撤回理由",
        mc0000: type === "1" ? "撤诉申请" : "撤回申请",
        aac004n: aac004 === "1" ? "男" : "女" 
      }
      const params = {
        serviceName: "jftjsq_xztjNetWithdraw",
        ...this.formData,
        ...otherParams
      }

      commonApi.proxyApi(params).then(res => {
        console.log(res, "撤回res")
        const {flag, msg} = res.map
        if (!flag) {
          this.$dialog.alert({
            title: "提示",
            message: msg,
            theme: "round-button"
          })
          return
        }

        this.$router.go(-1)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.revoke-box {
  overflow-x: hidden;
  .base-form {
    padding: 16px;
    .separate-box {
      position: relative;
      width: 120%;
      margin-left: -10%;
    }
    .button-box {
      padding: 0;
      background-color: @white_text_color;
    }
  }
}

</style>