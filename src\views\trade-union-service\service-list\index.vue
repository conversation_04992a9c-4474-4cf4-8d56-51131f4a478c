<!--
 * @Description: 暖新地图
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="trade-union-service">
    <search @search="handleSearch"></search>

    <selecte-condition @select="handleSelect"></selecte-condition>

    <search-list :searchsearchParams="searchParams"></search-list>
  </div>
</template>

<script>
import {setWxConfig, getLocation} from "@/utils/wechat"

import Search from "./comonents/search"
import SelecteCondition from "./comonents/selecte-condition"
import SearchList from "./comonents/search-list"

export default {
  name: "trade-union-service",
  components: {
    Search,
    SelecteCondition,
    SearchList
  },
  data() {
    return {
      searchParams: {        
        fwnr00: [], //服务内容
        ywlx00: [], //主管部门
        cae026: [], //归属区
        userlon: "118.078316",
        userlat: "24.464656",
        yzmc00: ""
      }
    }
  },
  async created() {
    // 微信SDK签名配置
    await setWxConfig()
    // 获取当前经纬度
    const {latitude="", longitude=""} = await getLocation()
    this.searchParams = Object.assign({}, this.searchParams, {userlon: String(longitude), userlat: String(latitude)})
    console.log(this.searchParams, "this.searchParams666")    
  },
  methods: {
    handleSearch(value) {
      this.searchParams.yzmc00 = value
    },
    handleSelect(data) {
      const {selectServices: fwnr00, selectDepartments: ywlx00, selectAreas: cae026} = data
      this.searchParams = Object.assign({}, this.searchParams, {fwnr00, ywlx00, cae026})  
    }
  }
}
</script>

<style lang="less" scoped>
.trade-union-service {
  padding: 14px 0 0;
}
</style>