<!--
 * @Description: 信息备案--步骤
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div :class="['info-record','info-container',businessType === 'details' ? 'info-container-details' : '']">

    <van-form class="base-form" :disabled="!required" @submit="handleSubmit" @failed="onFailed">

    <van-cell-group inset>
      <div v-show="required" class="option-box">
        <y-title  pleft="12" content="选择用工类型" fontContSize="16" fontWeight="bold" />
        <van-tabs type="card" v-model="formData.yglx00" @change="changePtbm ">
          <van-tab v-for="item in typeList" :disabled="tabDisabled(item)" :title="item.text" :name="item.value" :key="item.value" />
        </van-tabs>
      </div>
        <y-select-dict
          v-if="formData.yglx00 === '001'"
          v-model="formData.ptbm00"
          :rules="formRules.ptbm00"
          :disabled="!required"
          :dictType="filterRules"
          label="平台名称"
          is-link
        />

        <y-select
          v-if="formData.yglx00 === '001'"
          :class="['y-select', required ? 'common-required-select' : '']"
          label="录用企业名称"
          v-model="formData.aab001"
          :columns="aab001List"
          :format="format"
          :disabled="!required"
          :rules="formRules.aab001"
        />

        <y-select-dict v-if="formData.yglx00 === '001'" v-model="formData.aca111"
          :rules="formRules.aca111"
          :disabled="!required"
          dict-type="ACA111" label="岗位"
          is-link
        />
        <y-select-dict v-show="isPersonal" v-model="formData.aca111"
          :rules="formRules.aca111"
          :disabled="!required"
          dict-type="ACA111_GRLG" label="岗位"
          is-link
        />

        <y-select-dict v-model="formData.ccd028" :filterabled="false"
        :rules="formRules.ccd028"
        :disabled="!required"
        dict-type="CCD028" label="签约方式" is-link />

        <van-field
          v-model="formData.ccd006"
          name="ccd006"
          label="签约起始时间"
          placeholder="请选择"
          :required="required"
          :rules="formRules.ccd006"
          :readonly="true"
          @click="pickerShow = true"
        />

        <y-select-dict v-show="false" v-model="formData.sftbch" :filterabled="false" :disabled="!required"
        dict-type="YES_NO" label="是否同步参会" is-link />

      </van-cell-group>

      <div class="button-box" v-if="showBtn">
        <van-button @click="handleBack" plain type="info" native-type="button">
          上一步
        </van-button>
        <van-button round block type="primary" native-type="submit">
          完 成
        </van-button>
      </div>
    </van-form>

    <!-- 日期选择弹出层 -->
    <van-popup v-model="pickerShow" round position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        title="选择日期"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="handleConfirmPicker"
        @cancel="pickerShow = false"
        />
    </van-popup>

    <van-popup v-model="showPtmc" position="bottom">
      <van-picker show-toolbar :columns="ptmcList" @confirm="handleConfirmPtmc" @cancel="showPtmc=false"> </van-picker>
    </van-popup>

  </div>
</template>

<script>
import {commonApi} from "@api"

const INIT_SELECT_LIST = [{aab004: "暂无数据", aab001: ""}]

export default {
  name: "info-record",
  model: {
    prop: "formData",
    event: "updateFormData"
  },
  props: {
    formData: {
      type: Object,
      required: true
    },
    businessType: { //业务页面business 详情页面details
      type: String,
      default: "business"
    },
    showBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showPtmc: false,
      ptmcList: [],
      showYglx: false,
      typeList: [{ text: "平台用工", value: "001" }, { text: "个人零工", value: "003" }],
      show: true,
      formRulesInfo: {
        yglx00: [{ required: true, message: "请选择" }],
        ptbm00: [{ required: true, message: "请选择" }],
        aab001: [{ required: true, message: "请选择" }],
        aca111: [{ required: true, message: "请选择" }],
        ccd028: [{ required: true, message: "请选择" }],
        ccd006: [{ required: true, message: "请选择" }],
        sftbch: [{ required: true, message: "请选择" }]
      },

      // 平台类别字典过滤
      filterRules: {
        type: "PTBM00",
        filters: ["999"]
      },

      aab001List: INIT_SELECT_LIST,
      format: {
        name: "aab004",
        value: "aab001"
      },

      pickerShow: false,
      currentDate: new Date(),
      minDate: new Date(new Date().getFullYear() - 120, 1, 1),
      maxDate: new Date()
    }
  },
  watch: {
    "formData.ptbm00": {
      handler(val) {
        if (!val) {
          this.formData.aab001 = ""
          this.aab001List = INIT_SELECT_LIST

          return
        }

        this.selectPlatformAllCompany(val)
      }
    },
    "formData.aab001": { //选择单位携带上 企业类型 1 平台企业 2 合作企业
      handler(val) {
        const activeInfo = this.aab001List.filter(item => item.aab001 === val)?.[0] || {}
        this.formData.aab019 = activeInfo.aab019
      }
    }
  },
  computed: {
    // 查看模式标签禁止切换
    tabDisabled() {
      return (item) => {
        return !this.required && this.formData.yglx00 !== item.value
      }
    },
    required() {
      return this.businessType === "business"
    },
    formRules() {
      return this.businessType === "business" ? this.formRulesInfo : {}
    },
    maxDateValue() {
      return this.dayFormatFn(new Date(), "date")
    },
    companyInfo() { // 录用企业信息 提交时使用
      const {aab001} = this.formData

      return aab001 ? this.aab001List.find(item => (item.aab001 === aab001)) : {}
    },
    isPersonal() {
      return this.formData.yglx00 === "003"
    }
  },
  created() {
    this.getDicData()
  },
  methods: {
    /**
     * @description: 修改平台类型值
     * @param {*}
     * @return {*}
     * @author: T
     */
    changePtbm(data) {
      this.$set(this.formData, "yglx00", data)
      this.$set(this.formData, "aca111", "")

      if (data === "001") { // 平台用工
        this.$set(this.formData, "ptbm00", "")
        this.$set(this.formData, "ptbm00Text", "")
        this.formData.aab001 = ""
        this.aab001List = INIT_SELECT_LIST
      } else { //个人零工
        this.$set(this.formData, "ptbm00", "999")
        this.$set(this.formData, "aab001", "")

      }
    },
    /**
     * @description: 选择平台企业
     * @param {*}
     * @return {*
    },
    /**
     * @description: 获取字典数据
     * @param {*}
     * @return {*}
     * @author: T
     */
    getDicData() {
      commonApi.proxyApi({
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["PTBM00"]
      }).then(res => {
        const { PTBM00 } = res?.map?.data
        const filterRes = PTBM00.filter(item => {
          const { aaa102, aaa103} = item
          if (aaa102 !== "999") {
            item.text = aaa103
            item.value = aaa102
            return item
          }
        })
        this.ptmcList = filterRes
      })
    },
    // 确认选择用工类型
    handleConfirmYglx(data) {
      this.showYglx = false
      const { text: yglx00Text, value: yglx00 } = data
      this.$set(this.formData, "yglx00", yglx00)
      this.$set(this.formData, "yglx00Text", yglx00Text)
      if (yglx00 === "003") {
        this.$set(this.formData, "ptbm00", "999")
      } else {
        this.$set(this.formData, "ptbm00", "")
      }
    },
    handleConfirmPtmc(data) {
      this.showPtmc = false
      const { text: ptbm00Text, value: ptmc00 } = data
      this.$set(this.formData, "ptbm00Text", ptbm00Text)
      this.$set(this.formData, "ptbm00", ptmc00)
    },
    onFailed() {
      this.$toast("请完善表单信息！")
    },
    formatDate(date) {
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
    },
    // 选择时间
    changeDate(val) {
      this.formData.ccd006=val
    },
    // 选择平台名称
    selectPlatformAllCompany(ptbm00) {
      const params = {
        serviceName: "xytPerson_selectPlatformAllCompany",
        ptbm00
      }
      commonApi.proxyApi(params).then((res) => {
        const {rows} = res.map.data
        this.aab001List = rows?.length > 0 ? rows : INIT_SELECT_LIST
      })
    },
    // 选择日期
    handleConfirmPicker(val) {
      this.formData.ccd006 = this.dayFormatFn(val, "other")
      this.pickerShow = false
      this.currentDate = new Date()
    },
    // 上一步
    handleBack() {
      this.$emit("handleNext", 0)
    },
    // 完 成
    handleSubmit() {
      this.$dialog.confirm({
        title: "提示",
        message: `确认提交？`,
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        // 调用提交接口
        const { cce006, cce007 } = this.companyInfo || {}
        const params = {
          serviceName: "xytPerson_saveAndRecheckCe08Web",
          ...this.formData,
          sftbch: "0", //TODO是否同步与会 默认0否 等待后期功能开发再开放
          ...{ cce006, cce007 }
        }
        commonApi.proxyApi(params).then((res) => {
          this.$emit("handleSubmit")
        })
      }).catch(() => {})
      
    }
  }
}
</script>

<style lang='less' scoped>
::v-deep.option-box {
  .y-title {
    // padding: 0 7px;
    margin-left: 7px;
  }
  background-color: @white_bg_color;
}

</style>

