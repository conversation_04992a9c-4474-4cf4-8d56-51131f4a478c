<?xml version="1.0" encoding="UTF-8"?>
<svg width="30px" height="30px" viewBox="0 0 30 30" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 14@2x</title>
    <g id="app-首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="补充图标" transform="translate(-684.000000, -836.000000)" fill="#28BFC9" fill-rule="nonzero">
            <g id="编组-14" transform="translate(684.000000, 836.000000)">
                <path d="M23.4870898,5.46094548 C24.8725933,5.46094548 26,6.66027104 26,8.13483069 L26,8.13483069 L26,22.4061148 C26,23.8804189 24.8728551,25.08 23.4870898,25.08 L23.4870898,25.08 L15.9774145,25.08 L15.9774145,5.46094548 Z M6.22115616,5.76 L6.22115616,20.3892943 C6.22115614,21.4560844 6.75505643,22.4486346 7.61335505,22.9819039 L7.61335505,22.9819039 L10.9894818,25.08 L5.4348897,25.08 C4.09240312,25.08 3,23.8987042 3,22.446873 L3,22.446873 L3,8.393127 C3,6.94104416 4.09214948,5.76 5.4348897,5.76 L5.4348897,5.76 L6.22115616,5.76 Z M8.61223166,3.29587279 C9.25066792,2.9025717 10.0139644,2.90129392 10.6539712,3.29255056 L10.6539712,3.29255056 L14.2817237,5.44893433 L14.2817237,25.08 L14.2160215,25.08 L9.03628526,22.0291693 C8.14969912,21.4881567 7.6,20.4794761 7.6,19.3969398 L7.6,19.3969398 L7.6,5.15964465 C7.6,4.38300915 7.97850711,3.68636277 8.61223166,3.29587279 Z" id="形状结合"></path>
            </g>
        </g>
    </g>
</svg>