<!--
 * @Description: 保险协议
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="ylb-container">
    <van-sticky v-if="btnVisible">
      <div class="marsk"></div>
      <van-tabs v-model="activeTab">
        <van-tab v-for="(item, key) in tabList" :name="item.id" :title="item.text" :key="key"></van-tab>
      </van-tabs>
    </van-sticky>
    <div class="content">
      <agreement-detail :btnVisible="btnVisible" :current.sync="activeTab" :tab-info="tabInfo" @readFinish="readFinish"></agreement-detail>
    </div>
  </div>
</template>

<script>
import AgreementDetail from "./detail/index"
export default {
  components: {
    AgreementDetail
  },
  model: {
    prop: "active",
    event: "change"
  },
  props: {
    active: {
      type: [String, Number],
      default: "0"
    },
    btnVisible: {
      type: <PERSON><PERSON>an,
      default: true
    }
  },

  data() {
    return {
      activeTab: "",
      tabList: [
        {id: "0", type: "first", text: "投保须知及声明"},
        {id: "1", type: "second", text: "保险条款"},
        {id: "2", type: "third", text: "理赔指引"},
        {id: "3", type: "four", text: "免责告知"}
      ]
    }
  },
  watch: {
    active: {
      handler(val) {
        this.activeTab = val
      },
      immediate: true
    }
    
  },
  computed: {
    tabInfo() {
      const tabItem = this.tabList.find(item => item.id === this.activeTab)  
      return tabItem || {id: 0, text: "投保须知及声明"}
    }
  },
  methods: {
    readFinish(index) {
      this.scrollToTop(0, 0, "auto")
      this.$nextTick(() => {
        if (+index < this.tabList.length) {
          this.activeTab = index
          this.$emit("change", index)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.ylb-container {
  padding-bottom: 60px;
  .content {
    background-color: #fff;
  }
  .marsk {
    position: absolute;
    top: 0;
    left: 0;
    height: 60px;
    width: 100%;
    z-index: 99;
    background: transparent;
  }
}

</style>
