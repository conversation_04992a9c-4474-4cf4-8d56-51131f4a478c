<!--
 * @Description: 纠纷案由弹窗
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div>
    <van-popup class="business-popup" v-model="showAppeal" :close-on-click-overlay="false">
      <van-collapse v-model="appealActiveNames" @change="handleChangeGroup">
        <van-checkbox-group v-model="resultList">
          <van-collapse-item v-for="(item,index) in typeList" :key="index" :title="item.aba002" :name="item.aba002">
            <van-checkbox v-for="(i,v) in item.children" :key="v" :name="i.aba002">{{ i.aba002 }}</van-checkbox>
          </van-collapse-item>
        </van-checkbox-group>

        <div class="check-message" v-if="isCheckFail">
            {{ checkFailMessage }}
        </div>

        <div class="business-popup-button">
          <van-button round plain type="info" @click="showAppeal=false">关 闭</van-button>    
          <van-button class="confirm-button" round block type="primary" @click="handleConfirmReason">确 定</van-button>
        </div>
      </van-collapse>      
    </van-popup>
  </div>
</template>

<script>
import { commonApi } from "@api"

export default {
  props: {
    isShowReasonPopup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 选择弹窗信息
      appealActiveNames: [],
      
      resultList: [], //选择列表

      wagesMonthOne: "", //月份
      wagesMoneyOne: "", //元
      wagesMonthOneRules: [],
      wagesMoneyOneRules: [],
      
      wagesMonthTwo: "", //月份
      wagesMoneyTwo: "", //元
      wagesMonthTwoRules: [],
      wagesMoneyTwoRules: [],

      wagesMonthThree: "", //月份
      wagesMoneyThree: "", //元
      wagesMonthThreeRules: [],
      wagesMoneyThreeRules: [],

      wagesMoneyFour: "", //元
      wagesMoneyFourRules: [],
      
      peopleNumber: "", //童工人数
      peopleNumberRules: [],

      // 表单校验
      isCheckFail: false, //校验失败
      checkFailMessage: "表单验证不通过", //校验失败文案
      mounthRules: [{ required: true, message: "请输入月数" }],
      moneyRules: [{ required: true, message: "请输入金额" }],

      typeList: []
    }
  },
  computed: {
    showAppeal: {
      get() {
        return this.isShowReasonPopup
      },
      set(val) {
        this.$emit("update:isShowReasonPopup", val)
      }
    }
  },
  created() {
    this.getList()
    
  },
  methods: {
    // 获取外层列表
    async getList(abz150="") {
      const params = {
        serviceName: "jftjsq_getAnqing",
        abz150 //为空查询全部
      }
      const res = await commonApi.proxyApi(params)

      //查询全部
      if (!abz150) {
        this.typeList = res.map?.list || []
        return
      }

      // 查询子集
      this.typeList.forEach(item => {
        if (item.abz150 === abz150) {
          console.log(res.map?.list, "res.map?.list00")
          item.children = res.map?.list || []
        }
      })  
      this.$forceUpdate()
      console.log(this.typeList, "this.typeList00")    
    },
    // 点击选项
    handleChangeGroup(data) {
      if (this.appealActiveNames.length > data.length) {
        return
      }

      const list = this.typeList.filter(item => (item.aba002 === data[data.length - 1]))
      console.log(list, "*********list")
      if (list[0].children?.length > 0) {
        return
      }

      const {abz150} = list[0]
      this.getList(abz150)
    },
    // 确定选择
    handleConfirmReason() {
      let resultIds = "" 
      let resultText = ""      
       
      const newList = this.typeList.flatMap(item => (item.children))
      const lastList = newList.filter(item => item !== undefined && item !== null && item !== "")
      this.resultList.forEach(item => {
        const reasonInfo = lastList.find(i => (i.aba002 === item))
        resultIds += `${reasonInfo.abz150},`
        resultText += `${item}，`
      })

      resultIds = resultIds.substring(0, resultIds.length - 1)
      resultText = resultText.substring(0, resultText.length - 1)
      
      this.$emit("handleConfirmReason", {resultIds, resultText})
    }
  }
}
</script>
<style lang="less" scoped>
.business-popup {
  width: 92%;
  border-radius: 8px;
  max-height: calc(100vh - 80px);
  .business-popup-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 0;
    .confirm-button {
      margin-left: 16px;
    }
  }
  
  .checkbox-case {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .checkbox-line {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-left: 8px;
      color: @main_text_color;
      .van-cell {
        width: 20px;
        border-bottom: 1.5px solid @main_color;
        padding: 0;  
        margin: 0 4px;
        line-height: 18px;
        &.money-field {
          width: 40px;
        }
      }
    }
    &-other{
      height: 20px;
    }
  }
  /deep/ .van-checkbox, .van-radio {
    height: auto;
    margin: 14px 0;
  }

  .wages-form {
    /deep/.van-field__error-message {
      display: none;
    }
  }

  .check-message {
    font-size: 14px;
    color: @main_color;
    text-align: center;
  }
}
</style>