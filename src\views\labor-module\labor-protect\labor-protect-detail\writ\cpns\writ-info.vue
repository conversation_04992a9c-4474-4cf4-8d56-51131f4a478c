<!--
 * @Description: 文书/笔录
 * @Version: 0.1
 * @Autor: yc
-->

<template>
  <div class="apply-info">
    <van-form class="base-form">
      <y-title content="基本信息" moreText :colorMore="colorMore" :moreType="3" />
      <van-cell-group inset class="border-bottom-wide">
        <y-select-dict v-model="formData.wscllx" :rules="formRules.wscllx" disabled :required="required"
          dict-type="WSCLLX" label="材料类型" is-link />

        <van-field v-model="formData.aab004" name="aab004" label="被申请单位名称" placeholder="请输入" :required="required"
          :rules="formRules.aab004" />

        <van-field v-model="formData.bsqfr" name="bsqfr" label="被申请人联系人" placeholder="请输入" :required="required"
          :rules="formRules.bsqfr" />

        <van-field v-model="formData.bsqphone" name="bsqphone" label="被申请单位联系方式" placeholder="请输入" :required="required"
          :rules="formRules.bsqphone" />

        <van-field v-model="formData.sqrname" name="sqrname" label="申请人名字" placeholder="请输入" :required="required"
          :rules="formRules.sqrname" />

        <van-field v-model="formData.sqrphone" name="sqrphone" label="申请人联系方式" placeholder="请输入" :required="required"
          :rules="formRules.sqrphone" />

        <y-select-dict v-model="formData.sqrsex" :filterabled="false" :rules="formRules.sqrsex" dict-type="AAC004"
          label="申请人性别" :required="required" is-link />

        <van-field v-model="formData.sqrsfz" name="sqrsfz" label="申请人身份证号" placeholder="请输入" :required="required"
          :rules="formRules.sqrsfz" />

        <y-select-dict v-model="formData.dlrsex" :filterabled="false"  dict-type="AAC004"
          label="代理人性别"  is-link />

        <van-field v-model="formData.dlrsfzh" name="dlrsfzh" label="代理人身份证号" placeholder="请输入"
          :rules="formRules.dlrsfzh" />

        <van-field v-model="formData.dlrxm" name="dlrxm" label="代理人姓名" placeholder="请输入"
          :rules="formRules.dlrxm" />

        <van-field v-model="formData.currentDate" name="currentDate" label="支付金额日期" placeholder="请选择"
          :required="required" :rules="formRules.currentDate" :readonly="true" @click="handleSelectDate()" />

        <van-field v-model="formData.paymoney" name="paymoney" label="支付金额数目" placeholder="请输入" :required="required"
          :rules="formRules.paymoney" />
      </van-cell-group>

      <div class="button-box-more">
        <van-button plain type="info" @click="handleSave" native-type="button">
          保 存
        </van-button>
        <van-button plain type="info" @click="handleCancel" native-type="button">
          返 回
        </van-button>
      </div>
    </van-form>
    <van-popup v-model="pickerShow" round position="bottom">
      <van-datetime-picker v-model="currentDate" type="date" title="选择日期" :min-date="minDate" :max-date="maxDate"
        @confirm="handleConfirmPicker" @cancel="pickerShow = false" />
    </van-popup>
  </div>
</template>

<script>
import {
  five_text_color
} from "@/styles/theme/theme-params.less"
import { validateIdCard, checkMobile } from "@utils/check"

export default {
  name: "writ-info",
  model: {
  },
  props: {

  },
  data() {
    return {
      // 标题
      colorMore: five_text_color,
      currentDate: new Date(),
      formData: {
        // zjcl00: "", //编号
        wscllx: "001", //文书
        aab004: "",
        bsqfr: "",
        bsqphone: "",
        dlrsex: "",
        dlrsfzh: "",
        dlrxm: "",
        payday: "",
        paymoney: "",
        paymonth: "",
        payyear: "",
        sqrname: "",
        sqrphone: "",
        sqrsex: "",
        sqrsfz: ""
        // yszjcl: "" //材料内容
      },

      // 表单
      formRules: {
        aab004: [{ required: true, message: "请输入被申请单位名称" }],
        bsqfr: [{ required: true, message: "请输入被申请单位联系人" }],
        bsqphone: [
          { required: true, message: "请输入被申请单位联系方式" },
          {
            validator: checkMobile,
            message: "请输入正确的被申请单位联系方式",
            trigger: "onBlur"
          }
        ],
        // dlrsex: [{ required: true, message: "请选择代理人性别" }],
        // dlrsfzh: [
        //   { required: true, message: "请输入代理人身份证号" },
        //   {
        //     validator: validateIdCard,
        //     message: "请输入正确的代理人身份证号",
        //     trigger: "onBlur"
        //   }
        // ],
        // dlrxm: [{ required: true, message: "请输入代理人姓名" }],
        // currentDate: [{ required: true, message: "请选择支付日期" }],
        payday: [{ required: true, message: "请选择支付金额日" }],
        paymoney: [{ required: true, message: "请输入支付金额数目" }],
        paymonth: [{ required: true, message: "请选择支付金额月" }],
        payyear: [{ required: true, message: "请选择支付金额年" }],
        sqrname: [{ required: true, message: "请输入申请人名字" }],
        sqrphone: [
          { required: true, message: "请输入申请人联系方式" },
          {
            validator: checkMobile,
            message: "请输入正确的申请人联系方式",
            trigger: "onBlur"
          }
        ],
        sqrsex: [{ required: true, message: "请选择申请人性别" }],
        sqrsfz: [
          { required: true, message: "请输入申请人身份证号" },
          {
            validator: validateIdCard,
            message: "请输入正确的代理人身份证号",
            trigger: "onBlur"
          }
        ]
      },
      required: true,
      wscllx: "",
      isShowAddWrit: false,
      isLoading: false,
      // 日期选择

      pickerShow: false,
      minDate: new Date(new Date().getFullYear() - 100, 0, 1),
      maxDate: new Date()
    }
  },
  watch: {
    searchValue: {
      handler(val) {
        if (val) {
          this.filterData(val)
        }
      },
      immediate: true
    }
  },
  computed: {
  },
  async created() {
  },
  methods: {
    formatterTime(time) {
      return this.dayFormatFn(String(time), "YYYY-MM-DD HH:mm")
    },
    // 表单校验失败
    onFailed(errorInfo) {
      this.$toast("请完善表单信息！")
    },
    // 点击选择日期
    handleSelectDate() {
      this.maxDate = new Date()

      this.pickerShow = true
    },
    // 保存 存储数据 返回
    handleSave() {
      this.$emit("handleSave", this.formData)
    },

    // 取消
    handleCancel() {
      this.$router.go(-1)
    },
    handleConfirmPicker(val) {
      const [year, month, day] = this.dayFormatFn(val, "YYYY-MM-DD").split("-")
      this.formData.currentDate = this.dayFormatFn(val, "date")
      this.formData.payday = day
      this.formData.paymonth = month
      this.formData.payyear = year
      this.pickerShow = false
      // this.currentDate = new Date()
    }
  }
}
</script>

<style lang="less" scoped>
.search-field {
  /deep/.van-field__control {
    text-align: left;
  }
}

.btn-box {
  // height: 46px;
  padding: 9px 14px 9px 0px;
  box-sizing: border-box;

  .van-button {
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 28px;
    width: 80px;
    height: 28px;
    padding: 0;
    border-radius: 14px;
    margin-left: 12px;
  }
}

.top-div {
  height: 30px;

  .right-box {
    position: absolute;
    right: 0;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    .van-button {
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 28px;
      width: 80px;
      height: 28px;
      padding: 0;
      border-radius: 14px;
      margin-left: 12px;
    }
  }
}

.base-form {
  .right-box {
    position: absolute;
    right: 0;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;

    &>.point {
      width: 8px;
      height: 8px;
      border-radius: 4px;
      display: inline-block;
      margin-bottom: 1px;
    }

    &>.text {
      margin-left: 6px;
    }
  }
}

.evaluate-popup {
  width: 80% !important;
  border-radius: 8px;

  .evaluate-box {
    .evaluate-title {
      text-align: left;
      font-size: 16px;
      margin: 16px 0;
      padding-left: 16px;
      font-weight: 600;
    }

    /deep/.y-title {
      margin-bottom: 0px !important;
    }

    .evaluate-button {
      padding: 12px 0;

      .van-button--primary {
        margin-left: 8px;
      }
    }

    .online-revoke-icon {
      display: flex;
      justify-content: center;
      color: #32ae57;
      font-size: 65px;
    }

    .online-revoke-icon {
      display: flex;
      justify-content: center;
      color: #32ae57;
      font-size: 65px;
    }

    .online-revoke-text {
      font-size: 12px;
      text-align: center;
      margin-bottom: 16px;
      margin-top: 4px;
    }

    /deep/.van-radio-group {
      padding: 4px 16px;
      font-size: 14px;
    }

    /deep/.van-field__control {
      text-align: right;
    }
  }
}
</style>