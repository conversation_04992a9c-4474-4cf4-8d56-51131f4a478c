import _ from 'lodash';
import CryptoJS from 'crypto-js';
const sm2 = require("@/utils/sm-crypto").sm2
import {AxiosRequestConfig, AxiosResponse} from 'axios';

/**
 * 空值验证工具函数
 *
 * 用于检查值是否为 null 或 undefined
 *
 * @param val - 需要检查的值
 * @returns 如果值为 null 或 undefined 返回 true，否则返回 false
 *
 * @example
 * ```typescript
 * validateValue(null)      // true
 * validateValue(undefined) // true
 * validateValue("")        // false
 * validateValue(0)         // false
 * validateValue(false)     // false
 * ```
 */
export function validateValue(val) {
    return val === null || val === undefined;
}

/**
 * 请求数据加密主函数
 *
 * 对 axios 请求配置进行完整的加密处理，包括：
 * 1. 使用 SM2 加密 AES 密钥并设置到请求头
 * 2. 加密请求参数（params）
 * 3. 加密请求数据（data）
 *
 * @param config - axios 请求配置对象
 * @param aesKey - AES 加密密钥（16位字符串）
 *
 * @throws {Error} 当加密过程中发生错误时抛出异常
 *
 * @example
 * ```typescript
 * const config = {
 *   url: '/api/users',
 *   method: 'POST',
 *   params: { page: 1 },
 *   data: { name: 'test' }
 * };
 * encryptRequestData(config, '1234567890123456');
 * ```
 */
export function encryptRequestData(config, aesKey) {
    // 参数验证
    if (!config) {
        throw new Error('请求配置对象不能为空');
    }
    if (!aesKey || typeof aesKey !== 'string' || aesKey.length !== 16) {
        throw new Error('AES 密钥必须是16位字符串');
    }

    try {
        // 确保请求头对象存在
        if (!config.headers) {
            config.headers = {};
        }

        // 使用 SM2 加密 AES 密钥并设置到 X-Secret 请求头
        const encryptedAesKey = encryptRESKey(aesKey);
        config.headers['X-Secret'] = encryptedAesKey;

        // 加密请求参数（如果存在）
        if (config.params && Object.keys(config.params).length > 0) {
            encryptParamsWithAES(config, aesKey);
        }

        // 加密请求数据（如果存在）
        if (config.data) {
            encryptDataWithAES(config, aesKey);
        }
    } catch (error) {
        throw new Error(`请求数据加密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * 响应数据解密主函数
 *
 * 对 axios 响应数据进行完整的解密处理，包括：
 * 1. 从响应头获取加密的 AES 密钥
 * 2. 使用 SM2 解密 AES 密钥
 * 3. 使用 AES 解密响应数据
 *
 * @param response - axios 响应对象
 * @returns 解密后的响应数据
 *
 * @throws {Error} 当解密过程中发生错误时抛出异常
 *
 * @example
 * ```typescript
 * const decryptedData = decryptResponseData(response);
 * ```
 */
export function decryptResponseData(response) {
    // 参数验证
    if (!response) {
        throw new Error('响应对象不能为空');
    }

    try {
        // 从响应头获取加密的 AES 密钥（使用小写）
        const encryptedAesKey = response.headers['x-aes-key'];
        if (!encryptedAesKey) {
            // 如果没有密钥，可能响应未加密或发生错误，直接返回原始数据
            console.warn('响应头中缺少加密的 AES 密钥，跳过解密。');
            return response.data;
        }

        // 使用 SM2 解密 AES 密钥
        const decryptedAesKey = decryptRESKey(encryptedAesKey);
        // 解密响应数据
        const decryptedData = decryptAESData(decryptedAesKey, response.data);

        // 尝试解析为 JSON 对象，使用自定义解析器保持大整数精度
        try {
          return parseJSONWithBigInt(decryptedData);
        } catch (parseError) {
            // 如果不是 JSON 格式，直接返回解密后的字符串
            return decryptedData;
        }
    } catch (error) {
        throw new Error(`响应数据解密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * SM2 算法加密 AES 密钥
 *
 * 使用国密 SM2 算法对 AES 密钥进行加密，确保密钥传输安全。
 * SM2 是一种基于椭圆曲线密码学的公钥密码算法。
 *
 * @param aesKey - 待加密的 AES 密钥（16位字符串）
 * @returns SM2 加密后的密钥字符串
 *
 * @throws {Error} 当公钥未配置或加密失败时抛出异常
 *
 * @example
 * ```typescript
 * const encryptedKey = encryptRESKey('1234567890123456');
 * console.log(encryptedKey); // 输出加密后的密钥
 * ```
 */
export function encryptRESKey(aesKey) {
    // 参数验证
    if (!aesKey || typeof aesKey !== 'string' || aesKey.length !== 16) {
        throw new Error('AES 密钥必须是16位字符串');
    }

    // 获取 SM2 公钥
    const publicKey = process.env.VUE_APP_PUBLICKEY;
    if (!publicKey) {
        throw new Error('SM2 公钥未配置，请在环境变量中设置 VUE_APP_PUBLICKEY');
    }

    try {
        // 使用 SM2 算法加密 AES 密钥
        // 参数说明：
        // - aesKey: 待加密的 AES 密钥
        // - publicKey: SM2 公钥
        // - 1: 加密模式，1 表示 C1C3C2 模式
        const encryptedKey = sm2.doEncrypt(aesKey, publicKey, 1);

        if (!encryptedKey) {
            throw new Error('SM2 加密结果为空');
        }

        return encryptedKey;
    } catch (error) {
        throw new Error(`SM2 加密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * SM2 算法解密 AES 密钥
 *
 * 使用国密 SM2 算法对加密的 AES 密钥进行解密。
 * 注意：前端通常不保存私钥，此方法主要用于测试或特殊场景。
 *
 * @param encryptedAesKey - SM2 加密的 AES 密钥
 * @returns 解密后的 AES 密钥（16位字符串）
 *
 * @throws {Error} 当私钥未配置或解密失败时抛出异常
 *
 * @example
 * ```typescript
 * const aesKey = decryptRESKey(encryptedKey);
 * console.log(aesKey); // 输出解密后的密钥
 * ```
 */
export function decryptRESKey(encryptedAesKey) {
    // 参数验证
    if (!encryptedAesKey || typeof encryptedAesKey !== 'string') {
        throw new Error('加密的 AES 密钥不能为空');
    }

    // 获取 SM2 私钥（通常前端不保存私钥，此方法主要用于测试）
    const privateKey = process.env.VUE_APP_PRIVATEKEY_MODEL;
    if (!privateKey) {
        throw new Error('SM2 私钥未配置，前端通常不保存私钥');
    }

    try {
        // 使用 SM2 算法解密 AES 密钥
        // 参数说明：
        // - encryptedAesKey: 加密的 AES 密钥
        // - privateKey: SM2 私钥
        // - 1: 解密模式，1 表示 C1C3C2 模式

        // 兼容性处理：如果密文以 "04" 开头（来自BouncyCastle等标准库），sm-crypto需要去掉它才能正确解密
        let keyToDecrypt = encryptedAesKey;
        if (encryptedAesKey.startsWith('04')) {
            keyToDecrypt = encryptedAesKey.substring(2);
        }

        const decryptedKey = sm2.doDecrypt(keyToDecrypt, privateKey, 1);

        if (!decryptedKey || decryptedKey.length !== 16) {
            throw new Error('SM2 解密结果无效，密钥长度不正确');
        }

        return decryptedKey;
    } catch (error) {
        throw new Error(`SM2 解密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * 使用 AES 加密请求参数
 *
 * 对 axios 请求中的 params 参数进行 AES 加密处理。
 * 每个参数值都会被单独加密，空值会被设置为空字符串。
 *
 * @param config - axios 请求配置对象
 * @param aesKey - AES 加密密钥（16位字符串）
 *
 * @example
 * ```typescript
 * const config = {
 *   params: { page: 1, size: 10, keyword: 'test' }
 * };
 * encryptParamsWithAES(config, '1234567890123456');
 * // 结果：config.params 中的所有值都被加密
 * ```
 */
function encryptParamsWithAES(config, aesKey) {
    if (!config.params || typeof config.params !== 'object') {
        return;
    }

    const encryptedParams = {};

    try {
        // 遍历所有参数并加密
        for (const key in config.params) {
            if (Object.prototype.hasOwnProperty.call(config.params, key)) {
                const value = config.params[key];

                // 如果参数值不为空，则进行加密
                if (!validateValue(value)) {
                    const stringValue = String(value);
                    encryptedParams[key] = encryptAESData(aesKey, stringValue);
                } else {
                    // 空值设置为空字符串
                    encryptedParams[key] = '';
                }
            }
        }

        // 更新请求参数
        config.params = encryptedParams;
    } catch (error) {
        throw new Error(`请求参数加密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * 使用 AES 加密请求数据
 *
 * 对 axios 请求中的 data 数据进行 AES 加密处理。
 * 如果 data 是对象，会先转换为 JSON 字符串再加密。
 * 同时设置 Content-Type 为 application/json。
 *
 * @param config - axios 请求配置对象
 * @param aesKey - AES 加密密钥（16位字符串）
 *
 * @example
 * ```typescript
 * const config = {
 *   data: { name: 'test', age: 25 }
 * };
 * encryptDataWithAES(config, '1234567890123456');
 * // 结果：config.data 被加密，Content-Type 设置为 application/json
 * ```
 */
function encryptDataWithAES(config, aesKey) {
    if (!config.data) {
        return;
    }

    try {
        // 确保请求头对象存在
        if (!config.headers) {
            config.headers = {};
        }

        // 设置 Content-Type
        config.headers['Content-Type'] = 'application/json';

        // 处理请求数据
        let contentToEncrypt;

        if (_.isObject(config.data)) {
            // 如果是对象，转换为 JSON 字符串
            contentToEncrypt = JSON.stringify(config.data);
        } else if (typeof config.data === 'string') {
            // 如果已经是字符串，直接使用
            contentToEncrypt = config.data;
        } else {
            // 其他类型转换为字符串
            contentToEncrypt = String(config.data);
        }

        // 加密数据
        const encryptedData = encryptAESData(aesKey, contentToEncrypt);
        config.data = encryptedData;
    } catch (error) {
        throw new Error(`请求数据加密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * AES 数据加密核心函数
 *
 * 使用 AES-ECB 模式对数据进行加密，采用 PKCS7 填充方式。
 * 返回十六进制格式的加密结果。
 *
 * @param key - AES 加密密钥（16位字符串）
 * @param content - 待加密的内容
 * @returns 十六进制格式的加密结果
 *
 * @throws {Error} 当密钥格式错误或加密失败时抛出异常
 *
 * @example
 * ```typescript
 * const encrypted = encryptAESData('1234567890123456', 'Hello World');
 * console.log(encrypted); // 输出十六进制加密结果
 * ```
 */
export function encryptAESData(key, content) {
    // 参数验证
    if (!key || typeof key !== 'string' || key.length !== 16) {
        throw new Error('AES 密钥必须是16位字符串');
    }

    if (!content || typeof content !== 'string') {
        throw new Error('加密内容不能为空且必须是字符串');
    }

    try {
        // 将密钥转换为 CryptoJS 格式
        const cryptoKey = CryptoJS.enc.Utf8.parse(key);

        // 使用 AES-ECB 模式加密
        // 参数说明：
        // - content: 待加密的内容
        // - cryptoKey: 解析后的密钥
        // - mode: ECB 模式（电子密码本模式）
        // - padding: PKCS7 填充
        const encryptedResult = CryptoJS.AES.encrypt(content, cryptoKey, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });

        // 将加密结果转换为十六进制字符串
        const hexResult = encryptedResult.ciphertext.toString(CryptoJS.enc.Hex);

        if (!hexResult) {
            throw new Error('AES 加密结果为空');
        }

        return hexResult;
    } catch (error) {
        throw new Error(`AES 加密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * AES 数据解密核心函数
 *
 * 使用 AES-ECB 模式对数据进行解密，采用 PKCS7 填充方式。
 * 输入十六进制格式的加密数据，返回解密后的原始内容。
 *
 * @param key - AES 解密密钥（16位字符串）
 * @param encryptedContent - 十六进制格式的加密内容
 * @returns 解密后的原始内容
 *
 * @throws {Error} 当密钥格式错误或解密失败时抛出异常
 *
 * @example
 * ```typescript
 * const decrypted = decryptAESData('1234567890123456', 'a1b2c3d4...');
 * console.log(decrypted); // 输出解密后的原始内容
 * ```
 */
export function decryptAESData(key, encryptedContent) {
    // 参数验证
    if (!key || typeof key !== 'string' || key.length !== 16) {
        throw new Error('AES 密钥必须是16位字符串');
    }

    if (!encryptedContent || typeof encryptedContent !== 'string') {
        throw new Error('加密内容不能为空且必须是字符串');
    }

    try {
        // 将密钥转换为 CryptoJS 格式
        const cryptoKey = CryptoJS.enc.Utf8.parse(key);

        // 将十六进制字符串转换为 CryptoJS 格式
        const encryptedData = CryptoJS.enc.Hex.parse(encryptedContent);

        // 创建 CipherParams 对象
        const cipherParams = CryptoJS.lib.CipherParams.create({
            ciphertext: encryptedData
        });

        // 使用 AES-ECB 模式解密
        // 参数说明：
        // - cipherParams: 加密参数对象
        // - cryptoKey: 解析后的密钥
        // - mode: ECB 模式（电子密码本模式）
        // - padding: PKCS7 填充
        const decryptedResult = CryptoJS.AES.decrypt(cipherParams, cryptoKey, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });

        // 将解密结果转换为字符串
        const decryptedString = decryptedResult.toString(CryptoJS.enc.Utf8);

        if (!decryptedString) {
            throw new Error('AES 解密结果为空');
        }

        return decryptedString;
    } catch (error) {
        throw new Error(`AES 解密失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
}

/**
 * 检查响应是否需要解密
 *
 * 根据响应头判断响应数据是否需要解密处理。
 *
 * @param response - axios 响应对象
 * @returns 如果需要解密返回 true，否则返回 false
 *
 * @example
 * ```typescript
 * if (isResponseEncrypted(response)) {
 *   const decryptedData = decryptResponseData(response);
 * }
 * ```
 */
export function isResponseEncrypted(response) {
    if (!response || !response.headers) {
        return false;
    }
    // 检查响应头，统一使用小写（最佳实践）
    return response.headers['x-encrypted'] === 'true' && !!response.headers['x-aes-key'];
}

/**
 * 生成随机 AES 密钥
 *
 * 生成一个16位的随机字符串作为 AES 密钥。
 * 包含大小写字母和数字，确保密钥的随机性和安全性。
 *
 * @returns 16位随机 AES 密钥
 *
 * @example
 * ```typescript
 * const aesKey = generateRandomAESKey();
 * console.log(aesKey); // 例如：'aB3kP9mN2xQ7vR4t'
 * ```
 */
export function generateRandomAESKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 16; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * 自定义JSON解析器，保持大整数精度
 * 
 * 解决JavaScript JSON.parse()在处理大整数时丢失精度的问题
 * 将所有数字字符串保持为字符串格式，避免转换为Number类型
 * 
 * @param jsonString - JSON字符串
 * @returns 解析后的对象，大整数保持字符串格式
 * 
 * @example
 * ```typescript
 * const data = parseJSONWithBigInt('{"id": "269564665818824704", "name": "test"}');
 * console.log(typeof data.id); // "string"
 * console.log(data.id); // "269564665818824704"
 * ```
 */
function parseJSONWithBigInt(jsonString) {
  // 更精确的正则表达式，只匹配JSON值中的数字，避免匹配键名
  // 匹配模式：冒号后跟空白字符，然后是数字（可能包含小数点和科学计数法）
  const numberPattern = /:\s*(-?\d+(?:\.\d+)?(?:[eE][+-]?\d+)?)(?=\s*[,}])/g;
  
  // 将匹配到的数字用引号包围，转换为字符串
  const processedString = jsonString.replace(numberPattern, (match, number) => {
      // 检查是否为科学计数法或小数
      if (number.includes('e') || number.includes('E') || number.includes('.')) {
          // 对于科学计数法和小数，保持原样
          return `: ${number}`;
      }
      
      // 对于整数，检查是否超过JavaScript安全整数范围
      // Number.MAX_SAFE_INTEGER = 9007199254740991 (2^53 - 1)
      // Number.MIN_SAFE_INTEGER = -9007199254740991
      const num = parseInt(number, 10);
      if (num > Number.MAX_SAFE_INTEGER || num < Number.MIN_SAFE_INTEGER) {
          // 大整数转换为字符串
          return `: "${number}"`;
      }
      
      // 安全范围内的整数保持原样
      return `: ${number}`;
  });
  
  // 使用标准JSON.parse解析处理后的字符串
  return JSON.parse(processedString);
}
