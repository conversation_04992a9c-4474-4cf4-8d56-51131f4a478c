<!--
 * @Description: 时间选择
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="y-select">
    <van-field
      readonly
      clickable
      label="用工时段"
      :value="activeValue"
      placeholder="选择用工时段"
      @click="showPicker = true"
    />
    <van-popup v-model="showPicker" round position="bottom">
      <van-time-picker
        ref="timePicker"
        show-toolbar
        title="选择用工时段"
        @cancel="showPicker = false"
        @confirm="onConfirm"
        @change="onChange"
        @timePickerMounted="timePickerMounted"
      />
    </van-popup>
  </div>
</template>

<script>
import VanTimePicker from "./van-time-picker"

export default {
  name: "time-selection",
  components: {VanTimePicker},
  model: {
    prop: "time",
    event: "change"
  },
  props: {
    time: {
      type: String
    }
  },
  data() {
    return {
      showPicker: false,
      activeValue: ""
    }
  },
  watch: {
    time: {
      immediate: true,
      handler(val) {
        if (val) {
          this.activeValue = val
        }
      }
    }
  },
  
  methods: {
    timePickerMounted() {
      this.$nextTick(() => {
        const time = this.time.split("-")
        const startDate = time[0].split(":")
        const endDate = time[1].split(":")
        this.$refs.timePicker.setValues([startDate[0], startDate[1], "-", endDate[0], endDate[1]])
      })
    },
    onConfirm(value, index) {
      // console.log(`当前值：${value}, 当前索引：${index}`);
      const startDate = value.slice(0, 2).join(":")
      const endDate = value.slice(3, 5).join(":")
      this.activeValue = startDate + "-" + endDate
  
      this.$emit("change", this.activeValue)
      this.showPicker = false
    },

    onChange(picker, value, index) {
      console.log(`当前值：${value}, 当前索引：${index}`)
    }
  }
}
</script>

<style scoped lang="less">

</style>
