<!--
 * @Description: canvas签名画布
 * @Version: 0.1
 * @Autor: yjm
 * @LastEditors: yjm
 * @Date: 2020-10-22 15:07:06
 * @LastEditTime: 2020-11-09 15:20:42
-->
<template>
  <div class="signature">
    <div class="basic-header">
      <div class="sign-block-show" @click="handleSignPop">
        <img :src="signImageShow" class="sign-image" v-if="signImage" />

        <span class="sign-text" v-if="!signImageShow">请进行签字确认</span>

        <div class="reset-sign" v-if="signImage">{{ signTipTextShow }}</div>

      </div>
      <van-popup
        v-model="signPopShow"
        position="bottom"
        @opened="showDraw = true"
      >
        <y-draw
          v-if="showDraw"
          @confirm="confirm"
          ref="draw"
          @cancel="cancel"
        />
      </van-popup>
    </div>
    <!-- 调用弹窗提示 -->
    <y-confirm-dialog
      :pop-show.sync="dialogShow"
      confirm-text="确 定"
      :content="contentTipText"
      @confirm="confirmGenerate"
    >
    </y-confirm-dialog>
  </div>
</template>
<script>
import { Popup } from "@ylz/vant"
import YConfirmDialog from "../y-confirm-dialog/index"
import YDraw from "./draw"

export default {
  name: "y-signature",
  components: { YConfirmDialog, YDraw, "van-popup": Popup },
  props: {
    signImage: {
      // 签名提示图片
      type: String,
      default: require("./imgs/default.jpg")
    },
    signTipText: {
      // 签名提示文案
      type: String,
      default: "去签名"
    },
    contentTipText: {
      // 签名提示内容
      type: String,
      default: "请确保本人进行签名！"
    },
    isNeedTip: {
      // 是否需要提示本人签名
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: "base64" // blob、base64
    }
  },
  data() {
    return {
      showDraw: false,
      // 是否展示弹窗
      dialogShow: false,
      // 签名绘制
      signPopShow: false,
      // canvas 配置信息
      signImageShow: "",
      signTipTextShow: this.signTipText
    }
  },

  methods: {
    confirmGenerate() {
      this.signPopShow = true
      this.dialogShow = false
    },
    handleSignPop() {
      if (this.isNeedTip) {
        this.dialogShow = true
      } else {
        this.confirmGenerate()
      }      
    },
    cancel() {
      this.signPopShow = false
    },
    confirm(img) {
      img = this.type == "blob" ? this.getDataURLtoBlob() : img

      this.signImageShow = img
      this.signPopShow = false
      this.signTipTextShow = "重新签名"
      this.$emit("confirm", img)
    },
    getPNGImage() {
      return this.$refs.draw.getPNGImage()
    },
    getDataURLtoBlob() {
      return this.$refs.draw.getDataURLtoBlob()
    }
  }
}
</script>
<style lang="less" scoped>
.signature {
  .basic-header {
    padding-top: 15px * @ratio;
  }
  .notes-btn {
    margin-top: 30px * @ratio;
  }

  .sign-block-show {
    /* prettier-ignore */
    width: 344PX * @ratio;
    /* prettier-ignore */
    height: 212PX * @ratio;
    margin: 0 auto;
    border-radius: 5px * @ratio;
    position: relative;
    border: 1px * @ratio dashed @four_text_color;
    .sign-text {
      position: absolute;
      top: 40%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 14px;
      font-weight: 400;
      color: #9D9EA7;
      line-height: 14px;
    }
    .sign-image {
      position: relative;
      /* prettier-ignore */
      height: 212PX * @ratio;
      display: block;
      margin: 0 auto;
      max-width: 100%;
    }
    .reset-sign {
      position: absolute;
      height: 40px * @ratio;
      bottom: 0;
      left: 0;
      line-height: 40px * @ratio;
      text-align: center;
      width: 100%;
      background: @light_blue_color;
      color: @main_color;
      font-size: 14px * @ratio;
    }
  }

  .van-popup {
    height: 100%;
  }
}
</style>
