<!--
 * @Description: 资讯详情
 * @Author: hwx
 * @date: 2024/11/05 17:21
 * @LastEditors: Please set LastEditors
-->
<template>
    <div class="typical-case-detail" >
      <div class='header-box'>
        <p class="title">{{ pageData.almc01 }}</p>
        <p class="case-info mt-14 flex-c-b">
          <span>发布单位: 厦门新就业形态</span>
        </p>
        <p class="case-info mt-6 flex-c-b">
          <span>发布时间:{{ pageData.fbsj00 }}</span>
          <span>浏览量:{{pageData.llcs00 }}</span>
        </p>        
      </div>

      <div class='content-box'>
        <p class="content-title mt-14">【申请人请求】</p>
        <p class="content-text mt-12">{{ pageData.alzz01 }}</p>
        <p class="content-title mt-14">【基本案情】</p>
        <p class="content-text mt-12">{{ pageData.alsq01 }}</p>
        <p class="content-title mt-14">【案例分析】</p>
        <p class="content-text mt-12">{{ pageData.alms02 }}</p>
        <p class="content-title mt-14">【处理结果】</p>
        <p class="content-text mt-12">{{ pageData.alms01 }}</p>
        <p class="content-title mt-14">【典型意义】</p>
        <p class="content-text mt-12">{{ pageData.alzw01 }}</p>
      </div>
    </div>
  </template>
  
<script>
import {commonApi} from "@api"

export default {
  name: "typicalCaseDetail",
  data(){
    return {
      pageData: {}
    }
  },
  props: {
    bcz007: {
      type: String,
      default: ""
    }
  },
  mounted(){
    this.getBc07ById()
    this.bc07NumberRecord()
  },
  methods: {
    //获取典型案例详情
    getBc07ById(){
      commonApi.proxyApi({
        serviceName: "xytDxal_getBc07ById",
        bcz007: this.bcz007
      }).then((res) => {
        this.pageData= res?.map?.data || {}
        console.log(res, "res查询典型案例详情")
      })
    },
    //记录浏览次数
    bc07NumberRecord(){
      commonApi.proxyApi({
        serviceName: "xytDxal_bc07NumberRecord",
        bcz007: this.bcz007
      })
    }
  }
}
</script>
  
<style scoped lang="less">
.typical-case-detail {
  .header-box {
    width: 100%;
    height: auto;
    background-image: url("~@/assets/imgs/home/<USER>");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    padding: 24px 16px 8px;
    .title {
      font-size: 16px;
      color: #FFFFFF;
      line-height: 22px;
      font-weight: bold;
    }
    .case-info {
      font-size: 12px;
      color: #FFFFFF;
      line-height: 17px;
    }
  }
  .content-box {
    padding: 10px 16px;
    font-size: 14px;
    color: #303133;
    line-height: 22px;
    .fit-text {
      color: #666666;
    }
    .font-bold {
      font-weight: bold;
    }
    .content-top > span {
      &:first-child {
        font-size: 14px;
        color: #303133;
        line-height: 22px;
      }
      &:last-child {
        font-size: 12px;
        color: #666666;
        line-height: 17px;
      }
    }
    .content-title {
      line-height: 20px;  
      font-weight: bold;
      &-center {
        text-align: center;
      }
    }
    .content-type {
      line-height: 30px;  
    }
    .content-text {
      text-indent: 2em;
    }
  }
} 

</style>