<!--
 * @Description: 重要提示
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="tips-box">
    <div v-if="showTitle" class="tips-title">{{ title }}：</div>
    <p v-for="(item,index) in tipsList" :key="index">
      {{ item }}
    </p>
  </div>
</template>

<script>
export default {
  name: "y-tips",
  props: {
    showTitle: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: "重要提示"
    },
    tipsList: {
      type: Array,
      default: () => ([])
    }
  }
}
</script>

<style lang="less" scoped>

.tips-box {
  width: 100%;
  background: @background_shallow_color;
  padding: 10px 16px;
  .tips-title {
    font-size: 14px;
    font-weight: 500;
    color: @light_red_color;
    line-height: 22px;
    font-weight: bold;
  }
  & > p {
    font-size: 14px;
    font-weight: 400;
    color: @light_red_color;
    line-height: 20px;
  }
}
</style>