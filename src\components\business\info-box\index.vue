<!--
 * @Description: 信息盒子
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="info-box">
    <!-- 标题 -->
    <y-title :content="title" :moreText="moreText" :colorMore="colorMore" fontWeight="bold">      
      <template v-if="customMoreText" #right-box>
        <span class="right-box">
          <span class="point" :style="{'background': colorMore}"></span>
          <span class="text" :style="{'color': colorMore}">{{moreText}}</span>
        </span>
      </template>
    </y-title>
    
    <!-- 内容 -->
    <van-cell-group :border="false">     
      <slot name="cells"></slot>
    </van-cell-group>

    <!-- 底部按钮 -->
    <div class="btn-box flex-c-e">
      <slot name="buttons"></slot>
    </div>
  </div>
</template>

<script>
import {
  main_text_color
} from "@/styles/theme/theme-params.less"

export default {
  name: "info-box",
  props: {
    title: {
      type: String,
      default: "平台名称"
    },
    customMoreText: {
      type: Boolean,
      default: false
    },
    moreText: {
      type: String,
      default: ""
    },    
    colorMore: {
      type: String,
      default: main_text_color
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="less" scoped>
.info-box {
  margin: 16px;
  padding: 0 16px;
  background: @white_text_color;
  /deep/.van-cell-group {
    border-bottom: 0.5px solid #EEEEEE;
    .van-cell {
    padding: 0;
    .van-cell__title {
      color: @six_text_color;
    }
    .van-cell__value {
      color: @main_text_color;
    }
  }
  }
  .btn-box {
    height: 46px;
    .van-button {
      font-size: 14px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 28px;
      width: 80px;
      height: 28px;
      padding: 0;
      border-radius: 14px;
      margin-left: 12px;
    }
  }
  /deep/.y-title {
    .content {
      position: relative;
    }
    .right-box {
      position: absolute;
      right: 0;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      
      & > .point {
        width: 8px;
        height: 8px;
        border-radius: 4px;
        display: inline-block;
        margin-bottom: 1px;
      }
      & > .text {
        margin-left: 6px;
      }
    }
  }
}
</style>