<!--
 * @Description:
 * @Author: wujh
 * @date: 2024/5/16 15:16
 * @LastEditors: wujh
-->
<template>
  <div class="card-container">

    <van-list
      v-model="loading"
      :finished="finished"
      :finished-text="list?.length > 0 ? '没有更多了' : ''"
      :immediate-check="false"
      @load="onLoad"
    >
      <van-form
        ref="formData"
        v-for="(item, index) in list"
        :key="index"
        input-align="right"
        readonly
      >
        <van-field v-model="item.ccd006" name="从业时间" label="从业时间" label-width="140px" readonly/>
        <van-field v-model="item.ccd007" name="解除劳动从业时间" label="解除劳动从业时间" label-width="140px" readonly/>
        <van-field v-model="item.aab004" name="从业单位" label="从业单位" label-width="140px" readonly type="textarea" autosize/>
      </van-form>
    </van-list>
  </div>
</template>

<script>
import {commonApi} from "@/api"
import { isEmpty as _isEmpty } from "lodash"

export default {
  name: "find-ce10",
  data(){
    return {
      list: [],

      loading: false,
      finished: false,
      queryInfo: {
        aac002: "",
        page: 1,
        total: 0,
        size: 20
      }
    }
  },
  mounted() {
    const { zjhm00 } = {...this.$sessionUtil.getItem("userInfo")}
    this.queryInfo.aac002 = zjhm00
    this.findCe10ByPageFn(this.queryInfo)
  },
  methods: {
    findCe10ByPageFn(queryInfo){
      commonApi.proxyApi({
        serviceName: "xytPerson_findPersonCeByPage",
        ...queryInfo
      }).then((res) => {
        const { rows = [], total = 0 } = res?.map?.data
        if (Number(total) === 0){
          this.finished = true
          this.list = []
        }

        if (_isEmpty(rows)){
          this.finished = true
          this.isEmpty = true
        }

        if (this.queryInfo.page + "" === "1"){
          this.list = rows
        } else {
          this.list = this.list.concat(rows)
        }

        this.finished = this.list.length >= Number(total) // 根据结果修改当前的结束状态
      }).catch((err) => {
        console.error(err)
      }).finally(() => {
        this.loading = false
      })
    },
    onLoad(){
      console.log("onLoad")
      this.finished = true
      this.queryInfo.page += 1
      this.findCe10ByPageFn()
    }
  }
}
</script>

<style scoped lang="less">
.card-container{
  overflow: auto;
  height: 100vh;
  background-color: #F6F6F6;
  padding: 6px 10px 6px;
  /deep/.van-form{
    border-radius: 8px;
    overflow: hidden;
    &+.van-form{
      margin-top: 12px;
    }
  }
}
</style>