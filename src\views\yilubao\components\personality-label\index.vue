<!--
 * @Description: 个性标签
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <span class="personality-label-wrapper">
    <span class="personality-label" :style="{color: color, borderColor: color}">{{ label }}</span>
  </span>
</template>

<script>
import {border_color} from "@/styles/theme/theme-params.less"
export default {
  name: "personality-label",
  props: {
    label: {
      type: String,
      default: ""
    },
    color: {
      type: String,
      default: border_color
    }
  }

}
</script>

<style lang="less" scoped>
.personality-label-wrapper {
  font-size: 0;
}
.personality-label {
  display: inline-block;
  font-size: 12px;
  padding: 4px 8px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  border-radius: 4px;
  border: 1px solid @border_color;
  margin-right: 8px;
}
</style>
