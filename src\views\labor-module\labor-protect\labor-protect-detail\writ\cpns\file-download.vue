<!--
 * @Description: 文书/笔录材料
 * @Version: 0.1
 * @Autor: yc
-->

<template>
  <div class="submit-material">
    <!-- 重要提示 -->
    <!-- <y-tips :tipsList="tipsList"></y-tips> -->

    <div class="iframe-box">
      <y-pdf :base64Pdf="iframeUrl" :convertToImage="true" />
    </div>

    <div class="button-box-more">
      <van-button v-if="pageType !== 'detail'" plain type="info" @click="handleBack">
        返回
      </van-button>
      <van-button v-if="viewType !== 'sign'" round block type="primary" native-type="submit" @click="handleDownloadPdf">
        保存
      </van-button>
      <van-button v-if="viewType == 'sign'" round block type="primary" native-type="submit" @click="handleSign">
        去签字
      </van-button>
    </div>

  </div>
</template>

<script>
import YPdf from "@/components/plugins/y-pdf"
// import {commonApi} from "@/api"
export default {
  name: "file-download",
  components: {
    YPdf
  },
  props: {
    writBase64Data: {
      type: String,
      default: ""
    },
    viewType: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      tipsList: ["上传材料后，务必点击保存或提交按钮，上传的材料才会保存。"],

      materialNum: "", //材料编号
      iframeUrl: "" //上传材料页面地址
    }
  },
  watch: {
    writBase64Data: {
      handler(val) {
        this.iframeUrl = val
      },
      immediate: true
    }
  },
  computed: {
    pageType() {
      return this.$route.query.pageType || ""
    }
  },
  created() {
    // this.getBc23Base64ByDzd999()
  },
  methods: {
    // 渲染结果
    rendered() {
      console.log("渲染完成")
    },
    errorHandler() {
      this.$notify.error("加载失败")
    },
    // 保存 存储数据 返回
    handleSave() {
      console.log(this.iframeUrl, "iframeUrl")
      console.log(this.writBase64Data, "base64")
      // this.$emit("handleSave", "material")
    },
    // 上一步
    handleBack() {
      this.$emit("handleNext", 0)
    },
    handleSign() {
      this.$emit("handleNext", 2)
    },
    // 下载文件
    handleDownloadPdf() {
      // 将 base64 数据转换为字节数组
      const base64 = this.iframeUrl
      const fileBase64 = base64.substring(base64.indexOf(",") + 1)
      if (!fileBase64) {
        this.handleApiError("未获取到该文件")
      }
      const byteCharacters = atob(fileBase64)
      const byteArray = new Uint8Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteArray[i] = byteCharacters.charCodeAt(i)
      }
      // 创建 Blob 对象
      const blob = new Blob([byteArray], { type: "application/pdf" })
      // 创建链接并下载文件
      const link = document.createElement("a")
      link.href = URL.createObjectURL(blob)
      link.download = "劳动争议调解书_" + Date.now().toString()
      link.click()
      // 释放 URL 对象
      URL.revokeObjectURL(link.href)

    }
  }
}
</script>

<style lang="less" scoped>
.iframe-box {
  overflow: hidden;

  .my-iframe {
    width: 200%;
    height: 200%;
    transform: scale(0.47);
    transform-origin: 0 0;
    margin-left: 12px;
  }
}
</style>