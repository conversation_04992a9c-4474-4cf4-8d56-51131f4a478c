<!--
 * @Description:
 * @Author: wujh
 * @date: 2024/5/16 15:16
 * @LastEditors: wujh
-->
<template>
  <div class="card-container">

    <template v-if="!isEmpty">
      <van-list
        v-model="loading"
        :finished="finished"
        :finished-text="list?.length > 0 ? '没有更多了' : ''"
        :immediate-check="false"
        @load="onLoad"
      >
        <van-form
          ref="formData"
          v-for="(item, index) in list"
          :key="index"
          input-align="right"
          readonly
        >
          <van-field v-model="item.zsbh00" name="证书编号" label="证书编号" label-width="140px" readonly/>

          <y-select-dict v-model="item.jzbtlx" label="补贴申报类型" dict-type="JZBT_BTLX00" label-width="140px" disabled placeholder="" />
          <van-field v-model="item.btzje0" name="补贴金额(元)" label="补贴金额(元)" label-width="140px" readonly/>
        </van-form>
      </van-list>
    </template>
    <div v-else class="empty-style">
      <img src="@/assets/imgs/common/<EMAIL>" alt="">
      <p class="tips">暂无数据</p>
    </div>
  </div>
</template>

<script>
import {commonApi} from "@/api"
import { isEmpty as _isEmpty } from "lodash"

export default {
  name: "find-ce10",
  data(){
    return {
      list: [],
      loading: false,
      isEmpty: false,
      finished: false,
      queryInfo: {
        aac002: "",
        page: 1,
        total: 0,
        size: 20
      }
    }
  },
  mounted() {
    const { zjhm00 } = {...this.$sessionUtil.getItem("userInfo")}
    this.queryInfo.aac002 = zjhm00
    this.searchDataFn(this.queryInfo) //
  },
  methods: {
    searchDataFn(queryInfo){
      commonApi.proxyApi({
        serviceName: "xytjzbt_queryList",
        ...queryInfo
      }).then((res) => {
        console.log("从业登记信息", { ...res })
        const { data = [], total = 0 } = res
        if (Number(total) === 0){
          this.finished = true
          this.isEmpty = true
          this.list = []
        }

        if (_isEmpty(data)){
          this.finished = true
        }

        if (this.queryInfo.page + "" === "1"){
          this.list = data
        } else {
          this.list = this.list.concat(data)
        }

        this.finished = this.list.length >= Number(total) // 根据结果修改当前的结束状态
      }).catch((err) => {
        console.error(err)
      }).finally(() => {
        this.loading = false
      })
    },
    onLoad(){
      console.log("onLoad")
      this.finished = true
      this.queryInfo.page += 1
      this.searchDataFn()
    }
  }
}
</script>

<style scoped lang="less">
.card-container{
  overflow: auto;
  height: 100vh;
  background-color: #F6F6F6;
  padding: 6px 10px 6px;
  /deep/.van-form{
    border-radius: 8px;
    overflow: hidden;
    &+.van-form{
      margin-top: 12px;
    }
  }
  .empty-style{
    width: 100%;
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
    &>img{
      width: 194px;
      height: 136px;
    }
    .tips{
      font-size: 16px;
      font-weight: 400;
      color: #909399;
      text-align: center;
    }
  }
}
/deep/.y-select{
  padding: 9px 16px;
  background-color: #fff;

  &::after {
    margin:0 16px;
    content: " ";
    position: absolute;
    box-sizing: border-box;
    pointer-events: none;
    right: 0;
    bottom: 0;
    left: 0;
    transform: scaleY(0.5);
    border-bottom: 1px solid rgba(51, 51, 51, 0.1);
  }
}
</style>