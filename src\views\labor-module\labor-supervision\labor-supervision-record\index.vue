<!--
 * @Description: 劳动监察--记录
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="labor-supervision-record">

    <!-- 顶部tab -->
    <business-tabs :tabList="tabList" @handleChangeTabs="handleChangeTabs" @handleAdd="handleAdd"></business-tabs>
    <div class="info-container">
      <van-list
        v-model="loading"
        :finished="finished"
        :finished-text="list?.length > 0 ? '没有更多了' : ''"
        @load="onLoad"
      >
        <div v-if="list?.length > 0">
          <info-box v-for="item in list" :key="item.cce010" title="受理状态" :moreText="getPlatformName(item.abb292).text" :colorMore="getPlatformName(item.abb292).color" customMoreText
          >

            <template #cells>
              <van-cell title="案件编号" :value="item.abb280" :border="false" />
              <van-cell title="业务类型" :value="getBusinessType(item.aae391)" :border="false" />
              <van-cell class="writing-hidden" title="单位名称" :value="item.aab004" :border="false" />
              <van-cell title="是否重复信访" :value="getDictName(yesNoList, item.abbaa1)" :border="false" />
              <van-cell title="经办时间" :value="dayFormatFn(item.aae036, 'date')" :border="false" />
            </template>

            <template #buttons>
              <template v-if="userInfo.abb292 === '0'">
                <van-button v-if="item.abb292 === '9'" type="warning" @click="handleToRevoke(item, '2')">撤回申请</van-button>
                <van-button type="warning" class="info-button" @click="handleUpload(item)">材料上传</van-button>                
              </template>
              <template v-else>
                <van-button v-if="item.abbaa1 === '0' && ['13','15','17','18'].includes(item.abb292) && !item.aae416" type="warning" @click="handleEvaluate(item)">评 价</van-button>
                <van-button type="primary" @click="handleViewCase(item)">结案查看</van-button>    
              </template>   

              <van-button type="primary" @click="handleDetails(item)">查看详情</van-button>   
            </template>

          </info-box>
        </div>         

        <y-empty v-else></y-empty>
      </van-list>
    </div>

    <!-- 评价弹窗 -->
    <van-popup class="evaluate-popup" v-model="isShowEvaluatePicker" position="center" :close-on-click-overlay="false">
      <div class="evaluate-box">
        <div class="evaluate-title">案件评价</div>
        <van-radio-group v-model="evaluateRadio">
          <van-radio name="1">非常满意</van-radio>
          <van-radio name="2">满意</van-radio>
          <van-radio name="3">一般</van-radio>
          <van-radio name="4">不满意</van-radio>
        </van-radio-group>

        <van-field
          class="van-field-textarea"
          v-model="evaluateReason"
          name="evaluateReason"
          label="评价理由"
          placeholder="请输入评价理由"
          type="textarea"
          :required="true"
        />

        <div class="evaluate-button flex-c-c">
          <van-button plain type="info" round @click="isShowEvaluatePicker = false" native-type="button">
            关 闭
          </van-button>

          <van-button @click="handleSubmitEvaluate" round block type="primary">
            提 交
          </van-button>
        </div>        
      </div>      
    </van-popup>

  </div>
</template>

<script>
import BusinessTabs from "@/components/business/business-tabs"
import InfoBox from "@/components/business/info-box"
import {
  status_one_color, status_two_color, status_three_color
} from "@/styles/theme/theme-params.less"

import { commonApi } from "@/api"
import {getDictName} from "@utils/common"

const ACCEPT_STATUS = {
  "受理中": {
    data: ["0", "2", "4", "5", "6", "7", "8", "9", "10", "14", "16", "19", "20", "21", "22", "23", "24", "25", "26"],
    text: "受理中",
    color: status_two_color
  },
  "已登记": {
    data: ["1"],
    text: "已登记",
    color: status_one_color
  },
  "不受理": {
    data: ["3"],
    text: "不受理",
    color: status_three_color
  },
  "已结案": {
    data: ["13", "15", "17", "18"],
    text: "已结案",
    color: status_three_color
  }
}

const BUSINESS_TYPE = {
  "1": "投诉",
  "2": "举报",
  "3": "行政调解"
}

export default {
  name: "labor-supervision-record",
  components: {
    BusinessTabs,
    InfoBox
  },
  data() {
    return {
      tabList: [
        {title: "正在受理", number: "0"},
        {title: "已结案", number: "0"}
      ],

      // 信息列表
      titleObj: {
        title: "受理状态",
        platformName: "已结案"
      },

      list: [],
      loading: false,
      finished: false,

      // 用户信息
      userInfo: {
        ...this.$sessionUtil.getItem("userInfo"),
        
        abb292: "0", //0正在受理 1已结案
        aaa028: "2", //数据来源（0 经办 1 单位 2 个人 3 主管部门）
        source: "002" //渠道来源（001 PC端 002 移动端）
      },

      // 字典列表
      platformList: [], //平台字典列表
      yesNoList: [], //是否同步参会字典列表

      // 评价
      isShowEvaluatePicker: false,
      evaluateParams: {}, //评价所需参数
      evaluateRadio: "", //满意程度
      evaluateReason: "" //评价理由
    }
  },
  mounted() {
    this.getCountFn() // 获取总条数
    this.getPlatformList() //查询字典列表
  },

  methods: { 
    // 查看字典对应名称
    getDictName,
    // 获取受理状态
    getPlatformName(abb292) {   
      if (!abb292) {
        return ""
      }

      let data = {}  
      for (const key in ACCEPT_STATUS) {         
        if (ACCEPT_STATUS[key].data.includes(abb292)) {
          data = ACCEPT_STATUS[key]
          break
        }
      }
      return data
    }, 
    // 获取业务类型
    getBusinessType(aae391) { 
      return BUSINESS_TYPE[aae391]
    },
    //查询字典列表
    getPlatformList() {
      const params = {
        serviceName: "xytCommon_getAa10ByAaa100s",
        aa10List: ["YES_NO", "AAC149"]
      }
      commonApi.proxyApi(params).then((res) => {
        const {data} = res.map
        const dictInfo = {
          "YES_NO": "yesNoList",
          "AAC149": "platformList"
        }
        for (const key in dictInfo) {
          this[dictInfo[key]] = data[key].map((item) => {
            return {label: item.aaa103, value: item.aaa102}
          })
        }
      })
    },
    // 获取总条数
    getCountFn() {
      this.getCount("0") // 获取总条数
      this.getCount("1")
    },    
    getCount(abb292) {
      const {zjhm00: aac002 } = this.userInfo
      const params = {
        serviceName: "jftjsq_queryJdcxList",
        aac002,
        abb280: "", // 案件编号
        abb292 //1已结案 0正在受理
      }
      commonApi.proxyApi(params).then((res) => { 
        const {totalCount} = res.map
        if (abb292 === "0") { //0正在受理
          this.tabList[0].number = totalCount
        } else { //1已结案
          this.tabList[1].number = totalCount
        }
      })
    },
    // 切换tab
    handleChangeTabs(index) {
      console.log(index, "index")
      this.userInfo.abb292 = index === 0 ? "0" : "1" //切换在职状态
      this.list = []
      this.findBc01ByPage()
    }, 
    // 获取列表数据
    onLoad() {
      this.findBc01ByPage()
    },
    // 查询列表信息
    findBc01ByPage() {
      const {zjhm00: aac002, abb292 } = this.userInfo
      const params = {
        serviceName: "jftjsq_queryJdcxList",
        aac002, 
        abb280: "", // 案件编号
        abb292 //0正在受理 1已结案
      }
      commonApi.proxyApi(params).then((res) => {
        const {getStatus=[]} = res.map
        this.list = [...this.list, ...getStatus]
      }).finally(() => {
        this.loading = false
        this.finished = true
      })
    },
    // 新增
    handleAdd() {
      this.$router.push({path: "/labor-supervision-handle", query: {pageType: "add"}})
    },
    // 撤回 type：撤诉1 撤回2
    handleToRevoke(data, type) {      
      const {id, aae391} = data 
      console.log(data, "value111")
      this.$router.push({path: "/labor-supervision-revoke", query: {id, aae391, type}})
    },
    // 材料上传
    handleUpload(data) {
      const {abz200} = data
      console.log(abz200, "abz200 handleUpload")
      this.$router.push({path: "/labor-supervision-upload", query: {abz200: abz200}})
    },
    // 查看详情
    handleDetails(data) {
      console.log(data, "查看详情")
      const {id, abz200, aae391} = data
      this.$router.push({path: "/labor-supervision-handle", query: {pageType: "detail", id, abz200, aae391}})
    },
    // 结案查看
    handleViewCase(data) {
      console.log(data, "查看详情")
      const {id, aae391} = data
      this.$router.push({path: "/labor-supervision-case", query: {id, aae391}})
    },
    
    // 评价
    handleEvaluate(item) {
      this.isShowEvaluatePicker = true
      this.evaluateParams = item
    },
    // 提交评价
    handleSubmitEvaluate() {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定评价？",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        this.confirmEvaluate()
      })      
    },
    // 确定评价
    confirmEvaluate() {
      const {evaluateRadio: aae416, evaluateReason: aba006, evaluateParams: {id, aae391}} = this
      if (!aae416) {
        this.$toast("请选择满意程度！")
        return
      }         
      if (!aba006) {
        this.$toast("请输入评价理由")
        return
      }  

      const params = {
        serviceName: "jftjsq_tsComment",
        id,
        aae391,
        aae416,
        aba006
      }
      commonApi.proxyApi(params).then((res) => {
        this.$toast("已评价！")
        this.resetEvaluateData() // 重置评价数据
        this.findBc01ByPage() //重新获取数据
      })
    },
    // 重置评价数据
    resetEvaluateData() {
      this.isShowEvaluatePicker = false,
      this.evaluateParams = {}, //评价所需参数
      this.evaluateRadio = "", //满意程度
      this.evaluateReason = "" //评价理由
    }
  }
}
</script>

<style lang="less" scoped>
.labor-supervision-record {
  .info-container {
    background: @background_gray_color;  
    min-height: calc(100vh - 44px);
    /deep/.info-box {
      .y-title .content .more {
        position: relative;
        &::before {
          content: ' ';
          width: 8px;
          height: 8px;
          border-radius: 4px;
          background: @main_color;
          position: absolute;
          top: 50%;
          left: -25%;
          transform: translate(0,-50%);
        }
      }
      .van-cell-group .van-cell {
        .van-cell__title {
          flex: auto;
          width: 30%;
        }
        .van-cell__value {
          flex: auto;
          width: 70%;
        }
      }
    }
    /deep/.handle-cell .van-cell__value {
      color: @main_color !important;
    }    
  } 
  .evaluate-popup {
    width: 80% !important;
    border-radius: 8px;
    .evaluate-box {
      .evaluate-title {
        text-align: center;
        font-size: 16px;
        margin: 16px 0;
      }
      .evaluate-button {
        padding: 12px 0;
        .van-button--primary {
          margin-left: 8px;
        }
      }
      /deep/.van-radio-group {
        padding: 4px 16px;
        font-size: 14px;
      }
      /deep/.van-field-textarea{
        font-size: 14px;
      }
    }
  }
}
 
</style>