<!--
 * @Description: 添加盒子
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="add-shared">
    <labor-arbitrate-title :title="title" @handlePackUp="handlePackUp" :showPackUp="showPackUp"></labor-arbitrate-title>

    <div v-show="isOpen" class="list-container">
      <slot name="content"></slot>      
    </div>

    <div v-if="showAddBtn" :class="['add-btn', isOpen ? 'add-btn-open' : '']" @click="handleAdd">
      <div class="text-box">+添加{{addTitle}}</div>
    </div>
    
  </div>
</template>

<script>
import LaborArbitrateTitle from "./labor-arbitrate-title"

export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    addTitle: {
      type: String,
      default: ""
    },
    showPackUp: {
      type: Boolean,
      default: false
    },
    showAddBtn: {
      type: Boolean,
      default: true
    }
  },
  components: {
    LaborArbitrateTitle    
  },
  data() {
    return {
      isOpen: false
    }
  },
  watch: {
    showPackUp(val) {
      this.isOpen = val
    }
  },
  methods: {
    // 收起展开
    handlePackUp(isOpen) {
      this.isOpen = isOpen
    },
    // 添加
    handleAdd() {
      this.$emit("handleAdd")
    }
  }
}
</script>
<style lang="less" scoped>
.add-shared {
  .list-container {
    background: #F6F6F6;
    padding: 16px;
    ::v-deep .info-box {
      margin: 0;
      padding: 0;
      margin-bottom: 16px;
      .btn-box {
        padding: 0 14px;
      }      
    } 
    
  } 
  .add-btn {
    background: #fff;
    padding: 16px;
    &-open {
      background: #F6F6F6;
    }
    .text-box {
      border: 1px solid blue;
      height: 44px;
      border-radius: 22px;
      border: 1px dashed rgba(189,26,45,0.5);
      text-align: center;
      font-weight: 400;
      font-size: 16px;
      color: #BD1A2D;
      line-height: 44px;
    }      
  }
  
}
</style>