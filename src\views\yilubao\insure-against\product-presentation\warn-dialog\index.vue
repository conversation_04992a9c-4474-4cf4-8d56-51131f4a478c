<!--
 * @Description: 消费者权益保障提示
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <van-popup v-model="show" z-index="999" get-container="body" overlay-class="custom-popup" round>
    <template>
      <div class="dialog-wrapper" @click.stop>
        <div class="dialog-header"></div>
        <div class="content">
          <div class="text">
            <p class="overstriking">客户须知：</p>
            <p>尊敬的客户，此产品由太平洋保险代理有限公司提供销售服务，由中国太平洋财产保险股份有限公司厦门分公司首席承保，中国人民财产保险股份有限公司厦门市分公司、中国人寿财产保险股份有限公司厦门市分公司、中国人寿保险股份有限公司厦门市分公司、平安养老保险股份有限公司厦门分公司联合承保。</p>
            <p>您即将进入投保流程，请仔细阅读《太平洋保险代理有限公司客户告知书》<span class="active" style="position: relative; top:0; left:0; z-index: 999;" @click="handleClick">https://www.cpicagency.com.cn/notify</span>，以及后续购买页面中的产品条款、投保须知等内容。</p>
            <p class="overstriking">消费者权益保障提示：</p>
            <p>为保障您的权益，您在销售页面的操作将会被记录并加密存储。您本次输入的姓名、证件号、手机号等信息将仅用于办理投保信息会进行实名登记，请注意输入信息的真实性。</p>
            <p class="overstriking">温馨提示：</p>
            <p>本产品保障涉及以死亡为给付条件，若被保险人与投保人不一致时，投保人应当征求被保险人同意投保并确认保险金额的内容。父母为其未成年子女投保的除外。</p>
            <p>敬请知悉。</p>
          </div>
        </div>
        <div class="ylb-btn bottom-btn">
          <van-button round block @click="handleClose" :class="{'btn-disabled': time}">
            知道了
            <template v-if="time">
              {{ `${time}s` }}
            </template>
          </van-button>
        </div>
      </div>
  </template>
</van-popup>
</template>

<script>

export default {
  name: "warn-dialog",
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: "标题"
    }
  },
  data() {
    return {
      time: 5,
      timer: null
    }
  },
  computed: {
    show: {
      get() {
        return this.visible
      },
      set() {
        this.$emit("update:visible", false)
      }
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    init() {
      this.time = 5
      clearInterval(this.timer)
      this.timer = setInterval(() => {
        this.time = --this.time
        if (this.time === 0) {
          clearInterval(this.timer)
        }
      }, 1000)
    },
    handleClick() {
      location.href = "https://www.cpicagency.com.cn/notify"
    },
    handleClose() {
      if (this.time) {
        return
      }
      this.show = false
      this.$router.push("/yilubao/process")
    }
  }

}
</script>

<style lang="less" scoped>
.active {
  color: @ylb_color;
}
.dialog-wrapper {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 17px 20px;
  width: 90vw;
  .btn-disabled {
    opacity: 0.5;
  }
  
  .bottom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 41px;
    margin-top: 20px;
    .van-button {
      flex: 1;
      background: @ylb_color;
      color: #fff;
      height: 40px;
    }
  }
}
.dialog-header {
  position: absolute;
  left: 0;
  top: -26px;
  height: 192px;
  width: 100%;
  background: url('~@pic/yilubao/home/<USER>') no-repeat;
  background-size: cover;
}
.content {
  .title {
    margin-top: 50px;
    font-size: 18px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    color: #333333;
    line-height: 25px;
    text-align: center;
  }
  .text {
    margin-top: 50px;
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #333333;
    overflow: auto;
    min-height: 200px;
    max-height: 50vh;
    overflow: auto;
    padding-right: 4px;
    p {
      display: flex;
      flex-wrap: wrap;
    }
    .overstriking {
      font-weight: 700;
    }
  }
}
</style>