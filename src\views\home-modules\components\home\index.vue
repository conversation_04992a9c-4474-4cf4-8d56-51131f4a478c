<!--
 * @Description: 首页
 * @Author: wujh
 * @date: 2024/5/13 17:20
 * @LastEditors: 吕志伟 <EMAIL>
-->
<template>
  <div class="page-home" ref="homePage">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <div class="title">
        <!-- <img :src="require('@pic/home/<USER>/<EMAIL>')" alt=""> -->
      </div>
      <div class="menu">
        <div
          v-for="(item, key) in menuList"
          :key="key"
          class="menu-item"
          @click="menuClickFn(item)"
        >
          <img :src="item.image">
          <p>{{ item.name }}</p>
        </div>

      </div>
      <div class="banner-box">
        <van-swipe :autoplay="3000">
          <van-swipe-item v-for="(item, index) in banners" :key="index">
            <img v-lazy="item.logo" class="banner-img" @click="openPageByNew(item)" />
          </van-swipe-item>
        </van-swipe>
      </div>

      <div class="seek-box">
        <span class="seek-title">资讯中心</span>
        <span class="more" @click="showMoreFn">查看更多</span>
      </div>

      <div class="tabs-box">
        <van-tabs v-model="selectd">
          <van-tab
            v-for="(item, key) in bannerLabel"
            :key="key"
            :title="item.label"
            :name="item.key"
          >
            <div class="tabs-item" v-for="(item, key) in news[selectd]" :key="key + selectd" @click="openPageByNew(item)">
              <template v-if="selectd === 'dxal'">
                <div class="item-many">
                  <div class="item-top text-ellipsis-2 ellipsis-2">{{ item.title }}</div>
                  <div class="item-bottom">
                    <p class="item-tip">{{ item.source }}</p>
                    <p class="item-info flex-c-b">
                      <span>{{ item.publishDate }}</span>
                      <span>浏览量:{{item.llcs00}}</span>
                    </p>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="item-left">
                  <div class="item-top text-ellipsis-2 ellipsis-2">{{ item.title }}</div>
                  <div class="item-bottom">
                    <p class="item-tip">{{ item.source }}</p>
                    <p class="item-date">{{ item.publishDate }}</p>
                  </div>
                </div>
                <div class="item-right">
                  <img :src="item.logo" alt="">
                </div>
              </template>

            </div>
          </van-tab>
        </van-tabs>
      </div>
    </van-pull-refresh>

    <VueDragResize
      class="ylz-szr"
      :class="ylzSzrActive"
      v-if="isInitSzrIcon"
      :parentLimitation="true"
      :isActive="false"
      :z="1"
      :w="60"
      :h="111"
      :parentH="parentH"
      :x="vueDragResizeX"
      :y="parentH-160"
      @dragging="dragging"
      @dragstop="dragstop"
      @clicked="onClick"
      :sticks="[]"
    >
      <img :src="require('@pic/home/<USER>/szr.png')" width="60"/>
    </VueDragResize>
  </div>
</template>

<script>
import {commonApi} from "@api"

import home from "@pic/home/<USER>/home.png"
import homeActive from "@pic/home/<USER>/home-active.png"
import person from "@pic/home/<USER>/person.png"
import personActive from "@pic/home/<USER>/person-active.png"
import {isValidUrl} from "@/utils/str-util"

const SITE_ID = "502746213777477" //网站id(默认值:502746213777477)
const ANCESTORS = "502747731423301" //父节点id(默认值:502747731423301)
const CATALOG_LIST = ["twzx", "twzxapp", "Floatingwindow"]
import VueDragResize from "vue-drag-resize"
import { ZHRS_LOGIN_URL } from "@/assets/data/url-config"
export default {
  name: "home",
  data(){
    return {
      refreshing: false,
      active: 0,
      iconList: [
        {
          name: "服务大厅",
          inactive: home,
          active: homeActive
        },
        {
          name: "个人中心",
          inactive: person,
          active: personActive
        }
      ],
      menuList: [
        {
          name: "我要备案",
          image: require("@pic/home/<USER>/7.png"),
          url: "/info-filings"
        },
        {
          name: "我要维权",
          image: require("@pic/home/<USER>/3.png"),
          url: "/labor-protect"
        },
        {
          name: "益鹭保投保",
          image: require("@pic/home/<USER>"),
          url: "/yilubao"
        },
        {
          name: "暖新地图",
          image: require("@pic/home/<USER>/9.png"),
          url: "/trade-union-service"
        },
        {
          name: "技能提升",
          image: require("@pic/home/<USER>/4.png"),
          url: "/skill-train"
        },
        {
          name: "求职招聘",
          image: require("@pic/home/<USER>/2.png"),
          url: "/post-recruit"
        },
        {
          name: "骑手服务",
          image: require("@pic/home/<USER>/11.png"),
          url: "/rider"
        },
        // {
        //   name: "数字人",
        //   image: require("@pic/home/<USER>/szr.png"),
        //   url: "/digital-human"
        // },
        {
          name: "全部",
          url: "/service",
          image: require("@pic/home/<USER>/8.png")
        }
      ],

      banners: [],
      selectd: "",
      bannerLabel: [],
      news: {},
      parentW: "100%",
      parentH: 3000,
      isDragging: false,
      timer: null,
      isInitSzrIcon: false,
      ylzSzrActive: "",
      vueDragResizeX: 0
    }
  },
  components: {
    VueDragResize
  },
  beforeDestroy() {
    clearTimeout(this.timer)
  },
  created() {
    this.getCatalogFn() // 获取栏目列表
    this.fetchTypicalCaseData() // 查询典型案例列表
  },
  mounted() {
    const el = document.querySelector(".page-home")
    const computedStyle = window.getComputedStyle(el)
    const paddingLeft = +(computedStyle.paddingLeft?.replace("px", ""))

    this.parentW = document.body.clientWidth
    this.parentH = document.body.clientHeight
    this.vueDragResizeX = this.parentW - paddingLeft - 44
    this.isInitSzrIcon = true
  },
  methods: {
    onRefresh(){
      // this.getCatalogFn()
    },
    handleChange(activeVal) {
      this.scrollToTop(0, 0, "auto") //滚动到顶部
      if (activeVal === this.iconList.length - 1) {
        return
      }

      const components = ["InsureAgainst", "InsureView", "ClaimsCheck"]
      this.componentName = components[activeVal]
    },
    handleClickTab() {
      if (this.active === this.iconList.length - 1) {
        this.$toast("功能建设中,敬请期待！")
        return
      }
    },
    // 获取栏目列表
    async getCatalogFn(){
      const params = {
        serviceName: "getCatalogTreeNode",
        siteId: SITE_ID,
        ancestors: ANCESTORS //父节点id(默认值:502747731423301)
      }
      const res = await commonApi.proxyApi(params)

      const result = res?.data || [] // 获取通知公告信息
      const resultList = result[0]?.children
      this.bannerLabel = []
      const spreadList = [] // 接口promise列表
      const bannerList = [] // tab列表

      resultList.forEach(item => {
        const params = {
          serviceName: "getContentList",
          siteId: SITE_ID, //网站id(默认值:502746213777477)
          catalogId: item.catalogId, //栏目id
          "pipe": "PC", //发布渠道
          "pageNumber": "1", //页码
          "pageSize": "10" //每页数量
        }

        const oneApi = commonApi.proxyApi(params).then(res => { //获取栏目内容列表
          const contentList = res?.data?.contentList

          if (item.alias === "twzxapp"){ //轮播
            this.banners = contentList
          }

          if (!CATALOG_LIST.includes(item.alias)) { //资讯 tab列表
            bannerList.push({
              ...item,
              label: item.name,
              key: item.alias
            })
          }

          this.news[item.alias] = contentList //资讯 内容
        }).catch(err => {
          this.refreshing = false
        })

        spreadList.push(oneApi)
      })
      Promise.all(spreadList).then(() => {
        this.bannerLabel = bannerList.sort((prev, next) => {
          return prev.sortFlag - next.sortFlag
        })

        // 典型案例 单独新增
        const dxalItem = {
          label: "典型案例",
          key: "dxal"
        }
        this.bannerLabel.push(dxalItem)
      })

      this.selectd = "tzgg" // 默认选中 -- 通知公告
      this.refreshing = false
    },
    // 查询典型案例列表
    async fetchTypicalCaseData() {
      const params={
        serviceName: "xytDxal_findBc07ByPage",
        source: " 002",
        page: 1,
        size: 500
      }
      const res = await commonApi.proxyApi(params)
      const {rows=[]} = res?.map?.data || {}
      console.log(res, "查询典型案例列表")
      const contentList = rows.map(item => {
        return {
          ...item,
          showType: "1",
          catalogName: "典型案例",
          bcz007: item.bcz007,
          title: item.btxx01,
          publishDate: item.fbsj00,
          source: "厦门新就业形态"
        }
      })
      this.news["dxal"] = contentList
    },

    // 展示更多
    showMoreFn(){
      this.$router.push("/information-center")
    },
    openPageByNew(item){
      if (item.linkFlag === "Y"){
        if (isValidUrl(item.redirectUrl)){
          window.location.href = item.redirectUrl
        }
      } else {
        console.log(item, "item00000000000000")
        const { catalogName, bcz007 } = item
        const specialList = ["典型案例"] // 跳转典型案例 特殊处理
        const query = specialList.includes(catalogName) ? { showType: "1", bcz007} : {
          showType: "0",
          catalogId: item.catalogId,
          contentId: item.contentId,
          contentType: item.contentType
        } //showType 0富文本展示 1查询字段展示

        this.$router.push({
          path: "/information-center/detail",
          query
        })
      }
    },
    async menuClickFn(item){
      if (!item.url){ //未开放业务
        this.$toast("功能建设中，敬请期待!")
        return
      }

      if (isValidUrl(item.url)){ //外部业务
        window.open(item.url)
        return
      }

      if (item.url === "/palmar-distributor") { //掌上分销 校验是否是分销业务员
        const res = await commonApi.proxyApi({serviceName: "xytPerson_getDa05ByCurrentUser"})
        const {isSalesman} = res.map.data || {}
        if (isSalesman === "0") {
          this.$dialog.alert({
            title: "提示",
            message: "非承保公司业务人员，暂无权限!",
            theme: "round-button"
          })
          return
        }
      }

      this.$router.push(item.url)
    },
    dragging(newRect){
      this.isDragging = true
      if (newRect.left <= 0) {
        this.ylzSzrActive = "ylz-szr-left"
      } else if (newRect.left + 60 < this.parentW) {
        this.ylzSzrActive = "ylz-szr-vertical"
      } else {
        this.ylzSzrActive = ""
      }
    },
    dragstop(newRect){
      this.isDragging = false
    },
    onClick(){
      this.timer = setTimeout(() => {
        if (this.isDragging){
          return
        }
        window.location.href = ZHRS_LOGIN_URL + "virtual-human"
      }, 200)
    },
    onActivated(){
    }
  }
}
</script>

<style lang="less" scoped>
.page-home {
  min-height: 100vh;
  background-image: url("~@pic/home/<USER>/<EMAIL>");
  background-repeat: no-repeat;
  background-size: contain;
  padding: 20px 16px 80px;
  .title{
    height: 180px;
    // & > img {
    //   width: 100%;
    // }
  }
  .menu{
    display: flex;
    flex-flow: row;
    flex-wrap: wrap;
    width: 100%;
    min-height: 180px;
    background-color: #fff;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    .menu-item{
      display: flex;
      flex-flow: column;
      min-width: 25%;
      justify-content: center;
      align-items: center;
      margin-top: 12px;
      & > img{
        width: 44px;
        height: 44px;
        object-fit: contain;
      }
      & > p{
        height: 20px;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        font-style: normal;
        padding-top: 8px;
      }
    }
  }

  .banner-box {
    width: 100%;
    padding: 16px 0 0;
    .van-swipe-item{
      text-align: center;
    }
    .banner-img {
      max-width: 344px;
      max-height: 110px;
      object-fit: contain;
    }
  }
  .seek-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    .seek-title {
      font-size: 20px;
      font-weight: bold;
      color: @main_text_color;
      line-height: 25px;
    }
    .more {
      font-size: 14px;
      font-weight: bold;
      color: @five_text_color;
      line-height: 20px;
    }
  }
  .tabs-box {
    ::v-deep .van-tab {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }
    ::v-deep .van-tabs__content {
      padding: 0 0 0 14px;
    }
    .tabs-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      &:not(:last-child) {
        border-bottom: 1px solid #EEEEEE;
      }
      .item-left {
        .item-top {
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
        }
        .item-bottom {
          margin-top: 20px;
          .item-tip {
            font-size: 14px;
            font-weight: 400;
            color: @main_color;
            line-height: 16px;
          }
          .item-date {
            font-size: 12px;
            font-weight: 400;
            color: @five_text_color;
            line-height: 18px;
            margin-top: 6px;
          }
        }
      }
      .item-right {
        width: 108px;
        margin-left: 18px;
        border-radius: 4px;
        & > img {
          width: 100%;
          max-width: 180px;
          object-fit: contain;
          border-radius: 4px;
        }
      }
      .item-many {
        width: 100%;
        .item-top {
          font-size: 14px;
          font-weight: bold;
          color: @main_text_color;
          line-height: 20px;
        }
        .item-bottom {
          margin-top: 20px;
          .item-tip {
            font-size: 14px;
            font-weight: 400;
            color: @main_color;
            line-height: 16px;
          }
          .item-info {
            font-size: 12px;
            font-weight: 400;
            color: @five_text_color;
            line-height: 18px;
            margin-top: 6px;
          }
        }
      }
    }
  }
  .ylz-szr{
    position: fixed;
    z-index: 9999999 !important;
    transform: rotate(-45deg);
    transform-origin: top left;
    &.vdr.active:before{
      outline: none;
    }
  }
  .ylz-szr-left {
    transform: rotate(45deg);
    transform-origin: top right;
  }
  .ylz-szr-vertical {
    transform: rotate(0deg);
  }
}
</style>
