<!--
 * @Description: 添加被申请人
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div>
    <y-tips
      v-if="formData.aac001 === '002'"
      :showTitle="false"
      :tipsList="tipsList"
    ></y-tips>

    <van-form
      ref="baseForm"
      class="base-form"
      label-width="130"
      :disabled="pageType === 'details'"
      @failed="onFailed"
      @submit="handleSave"
    >
      <y-select-dict
        v-model="formData.aac001"
        label="被申请人类型"
        :rules="formRules.aac001"
        dict-type="AAC001"
        :disabled="pageType === 'details'"
        :filterabled="false"
        is-link
      />

      <!-- 用人单位 -->
      <van-cell-group v-show="formData.aac001 === '002'">
        <!-- 单位名称搜索 -->
        <y-search-unit v-model="formData"></y-search-unit>

        <y-select-dict
          v-model="formData.aae011"
          label="单位类型"
          :required="true"
          :rules="formRules.aae011"
          dict-type="AAE011"
          :filterabled="false"
          :disabled="pageType === 'details'"
          is-link
        />
        <van-field
          v-model="formData.aab013"
          name="aab013"
          label="法定代表人"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aab013"
        />
        <van-field
          class="label-width"
          v-model="formData.aae015"
          name="aae015"
          label="统一社会信用代码"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aae015"
        />
        <van-field
          v-model="formData.aae008"
          name="aae008"
          label="联系电话"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aae008"
        />
        <van-field
          v-model="formData.aae010"
          name="aae010"
          label="住所"
          placeholder="请输入"
          type="textarea"
          input-align="left"
          :autosize="{ maxHeight: 70, minHeight: 50 }"
          maxlength="100"  
          :required="true"
          :rules="formRules.aae010"
        />
        <van-field
          v-model="formData.aae012"
          name="aae012"
          label="注册地址"
          placeholder="请输入"
          type="textarea"
          input-align="left"
          :autosize="{ maxHeight: 70, minHeight: 50 }"
          maxlength="100"  
          :required="true"
          :rules="formRules.aae012"
        />
        <van-field class="alone-name" label="企业信息" />
        <div class="uploader-container uploader-container-alone">
          <van-uploader
            v-model="fileList"
            :disabled="pageType === 'details'"
            :deletable="pageType !== 'details'"
            :preview-full-image="true"
            @oversize="handleOversize"
            accept = '.jpg,.jpeg,.png'
            max-size="3 * 1024 * 1024"
            max-count="1"
            :after-read="afterRead"
          />
        </div>
      </van-cell-group>

      <!-- 自然人 -->
      <van-cell-group v-show="formData.aac001 === '001'" inset class="border-bottom-wide">
        <van-field
          v-model="formData.aac003"
          name="aac003"
          label="姓名"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aac003"
        />
        <y-select-dict
          v-model="formData.ccg981"
          label="证件类型"
          :required="true"
          :rules="formRules.ccg981"
          dict-type="CCG981_ZC"
          :filterabled="false"
          :disabled="pageType === 'details'"
          is-link
        />
        <van-field
          v-model="formData.aac002"
          name="aac002"
          label="证件号码"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aac002"
        />
        <y-select-dict
          v-model="formData.aac005"
          label="民族"
          :required="true"
          :rules="formRules.aac005"
          dict-type="AAC005"
          :disabled="pageType === 'details'"
          is-link
        />
        <y-select-dict
          v-model="formData.aac004"
          label="性别"
          :required="true"
          :rules="formRules.aac004"
          dict-type="AAC004"
          :filterabled="false"
          :disabled="pageType === 'details'"
          is-link
        />
        <van-field
          v-model="formData.aac006"
          name="aac006"
          label="出生日期"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aac006"
          :readonly="true"
          @click="handleSelectDate('aac006')"
        />
        <van-field
          v-model="formData.aae005"
          name="aae005"
          label="联系电话"
          placeholder="请输入"
          :required="true"
          :rules="formRules.aae005"
        />
        <van-field
          v-model="formData.aae006"
          name="aae006"
          label="住址(身份证)"
          placeholder="请输入"
          type="textarea"
          input-align="left"
          :autosize="{ maxHeight: 70, minHeight: 50 }"
          maxlength="100"  
          :required="true"
          :rules="formRules.aae006"
        />
        <van-field
          class="label-width"
          v-model="formData.aae007"
          name="aae007"
          label="法律文书送达地址"
          type="textarea"
          input-align="left"
          :autosize="{ maxHeight: 70, minHeight: 50 }"
          maxlength="100"  
          placeholder="请输入"
          :required="true"
          :rules="formRules.aae007"
        />
      </van-cell-group>

      <div class="button-box-more" v-if="pageType !== 'details'">
        <van-button round block type="primary" native-type="submit">
          保存
        </van-button>
      </div>
    </van-form>

    <!-- 日期选择弹出层 -->
    <van-popup v-model="pickerShow" round position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        title="选择日期"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="handleConfirmPicker"
        @cancel="pickerShow = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { validateIdCard, checkMobile, validContact } from "@utils/check"
import isEmpty from "lodash/isEmpty"
import { commonApi } from "@/api"
import {removeBase64Header, addBase64Header} from "@/utils/fileUtil"

export default {
  name: "add-applied-people",
  data() {
    return {
      // 提示信息
      tipsList: [
        "*企业相关信息，可登录“国家企业信用信息公开系统”（网址：http://www.gsxt.gov.cn/index.html)进行查询，并可将查询到的页面作为企业营业执照进行上传。"
      ],

      // 表单信息
      formData: {
        aac001: "002", //被申请人类型

        // 自然人
        aac003: "", //姓名
        ccg981: "", //证件类型
        aac002: "", //身份证号码
        aac005: "", //民族
        aac004: "", //性别
        aac006: "", //出生日期
        aae005: "", //联系电话
        aae006: "", //住址（身份证）
        aae007: "", //法律文书送达地址

        // 用人单位
        aab004: "", //单位名称
        aae011: "", //单位类型
        aab013: "", //法定代表人
        aae015: "", //统一社会信用代码
        aae008: "", //联系电话
        aae010: "", //住所
        aae012: "", //注册地址
        aae016Base64: "" //企业信息
      },
      formRules: {},
      formRules1: {
        // 自然人
        aac003: [{ required: true, message: "请输入姓名" }],
        ccg981: [{ required: true, message: "请选择证件类型" }],
        aac002: [
          { required: true, message: "请输入身份证号码" },
          {
            validator: validateIdCard,
            message: "请输入正确身份证号码",
            trigger: "onBlur"
          }
        ],
        aac005: [{ required: true, message: "请选择民族" }],
        aac004: [{ required: true, message: "请选择性别" }],
        aac006: [{ required: true, message: "请选择出生日期" }],
        aae005: [
          { required: true, message: "请输入联系电话" },
          {
            validator: checkMobile,
            message: "请输入正确的手机号",
            trigger: "onBlur"
          }
        ],
        aae006: [{ required: true, message: "请输入住址（身份证）" }],
        aae007: [{ required: true, message: "请输入法律文书送达地址" }]
      },
      formRules2: {
        // 用人单位
        aab004: [{ required: true, message: "请点击搜索单位名称" }],
        aae011: [{ required: true, message: "请选择" }],
        aab013: [{ required: true, message: "请输入法定代表人" }],
        aae015: [{ required: true, message: "请输入统一社会信用代码" }],
        aae010: [{ required: true, message: "请输入住所" }],
        aae012: [{ required: true, message: "请输入注册地址" }],
        aae008: [
          { required: true, message: "请输入联系电话" },
          {
            validator: validContact,
            message: "请输入手机号或固定电话(请去除'-'符号)",
            trigger: "onBlur"
          }
        ]
      },

      // 日期选择
      pickerShow: false,
      currentDate: new Date(),
      pickerType: "",
      minDate: new Date(new Date().getFullYear() - 100, 0, 1),
      maxDate: new Date(),

      // 企业信息
      fileList: []
    }
  },
  computed: {
    zcy000() {
      //仲裁委员会
      return this.$route.query.zcy000
    },
    pageType() {
      return this.$route.query.pageType
    },
    bczbsq() {
      //被申请人信息表主键
      return this.$route.query?.bczbsq || ""
    }
  },
  watch: {
    "formData.aac001": {
      handler(val) {
        if (this.pageType === "add") {
          this.initData()
          this.formData.aac001 = val
          this.formRules = val === "002" ? this.formRules2 : this.formRules1
          console.log(this.formRules, "this.formRules")
        }        
      },
      immediate: true
    },
    "formData.ccg981": {
      handler(val) {
        if (this.formData.aac001 === "002") {
          return
        }
        
        const requireIdCard = { required: true, message: "请输入" }
        const checkIdCard = {
          validator: validateIdCard,
          message: "请输入正确身份证号码",
          trigger: "onBlur"
        }
        
        this.formRules.aac002 = val === "001" ? [requireIdCard, checkIdCard] : [requireIdCard]   
      },
      immediate: true
    }
  },
  created() {
    // 检查 URL 查询参数中是否有 formData
    const formDataStr = this.$route.query.formData
    if (formDataStr) {
      try {
        const parsedFormData = JSON.parse(formDataStr)
        // 将解析后的数据合并到表单数据中
        this.formData = { ...this.formData, ...parsedFormData }
      } catch (error) {
        console.error("解析 formData 失败:", error)
      }
    }
    
    if (this.pageType !== "add") {
      this.getBc05BsqrById() // 查询详情
    }

    this.handleSearchUnit() // 查询单位名称
  },
  methods: {
    initData() {
      Object.assign(this.$data, this.$options.data())
    },
    // 查询详情
    getBc05BsqrById() {
      const { bczbsq } = this
      commonApi
        .proxyApi({
          serviceName: "xytBc05Bsqr_getBc05BsqrById",
          bczbsq: bczbsq
        })
        .then((res) => {
          console.log("被申请人 查询详情", res)
          const { data } = res.map
          for (const key in data) {
            if (data[key]) {
              this.formData[key] = data[key]
            }
          }
          const { aae016Base64 } = this.formData
          this.fileList = aae016Base64
            ? [
              {
                content: addBase64Header(aae016Base64, "image/png")
              }
            ]
            : []
        })
    },

    onFailed(val) {
      console.log(val, "val")
      this.$toast("请完善表单信息！")
    },
    handleNext() {},

    // 选择日期
    handleSelectDate(type) {
      if (this.pageType === "details") {
        return
      }
      this.pickerShow = true
      this.pickerType = type
    },
    handleConfirmPicker(val) {
      this.formData[this.pickerType] = this.dayFormatFn(val, "date")
      this.pickerShow = false
      // this.currentDate = new Date()
    },

    // 查询单位信息
    handleSearchUnit() {
      const params = {
        serviceName: "xytCommon_findAb01NewByName",
        aab004: this.unitName
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "res")
        const { rows } = res.map.data
        this.unitList = rows.map((item) => {
          return {
            text: item.aab004,
            value: item.aab001
          }
        })
        console.log(this.unitList, "this.unitList")
      })
    },

    // 文件读取完成后的回调函数
    afterRead(data) {
      console.log(data, "data")
      console.log(this.fileList, "fileList")
    },
    // 保存
    handleSave() {
      const { aac001 } = this.formData
      if (aac001 === "002" && isEmpty(this.fileList)) {
        this.$dialog.alert({
          title: "提示",
          message: "请先上传企业信息！",
          theme: "round-button"
        })
        return
      }
      this.formData.aae016Base64 =
        aac001 === "002" ? removeBase64Header(this.fileList[0].content) : ""

      this.$dialog
        .confirm({
          title: "提示",
          message: "您是否确定保存",
          showCancelButton: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        })
        .then(() => {
          const { zcy000 } = this
          commonApi
            .proxyApi({
              serviceName: "xytBc05Bsqr_saveOrUpdateBc05Bsqr",
              ...this.formData,
              aac002: this.formData.aac002.toUpperCase(),
              zcy000
            })
            .then((res) => {
              console.log("保存", res)
              this.$dialog
                .alert({
                  title: "提示",
                  message: "保存成功！",
                  theme: "round-button"
                })
                .finally(() => {
                  this.$router.go(-1)
                })
            })
            .catch((err) => {
              console.error(err)
            })
        })
    },
    // 限制上传文件大小
    handleOversize() {
      this.$dialog.alert({
        title: "提示",
        message: "请上传小于3M的图片！",
        theme: "round-button"
      })      
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.alone-name {
  &::after {
    border-bottom: none !important;
  }
  .van-cell__value {
    display: none;
  }
}
/deep/.unit-name-field {
  .van-cell__value {
    .van-field__control {
      text-align: left;
    }
    .van-field__button {
      min-width: 64px;
      & > span {
        font-size: 14px;
        color: @main_color;
      }
    }
  }
}
.uploader-container {
  padding: 8px 16px;
}
.button-box-more {
  background: #fff;
}
</style>
