<!--
 * @Description: 主题
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="theme-page ylb-container">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model="loading"
        :finished="finished"
        :finished-text="list.length > 0 ? '没有更多了' : ''"
        @load="onLoad"
      >
        <template v-if="list.length > 0">
          <div class="card-box flex-s-c" v-for="(item,index) in list" :key="index">
            <img src="@pic/yilubao/palmar-distributor/<EMAIL>" alt="">

            <div class="content-box">
              <div class="content-title flex-c-s">
                <span class="title xz-ellipsis-2">{{item.fxzt00}}</span>
              </div>
              <div class="content-text">{{item.wanr00}}</div>
              <div class="content-img">
                <img :src="addBase64Header(item.zttp00, 'image/png')" alt="">
              </div>
            </div>

            <div class="share-btn" @click="handleClickShare(item)">
              <img src="@pic/yilubao/palmar-distributor/<EMAIL>" alt="">
            </div>
          </div>
        </template>

        <y-empty v-else tips="暂无主题列表哦~"></y-empty>
      </van-list>
    </van-pull-refresh>

    <!-- 分销二维码 -->
    <div v-show="false" ref="qrCodeUrl"></div>  
    
    <van-popup class="theme-popup" v-model="showPopup" position="bottom" :round="false">
      <div class="card-box card-box-popup">
        <img src="@pic/yilubao/palmar-distributor/<EMAIL>" alt="">
        <div class="content-box">
          <div class="content-title flex-c-s">
            <span class="title xz-ellipsis-2">{{selectInfo.fxzt00}}</span>
          </div>
          <div class="content-text">{{selectInfo.wanr00}}</div>
          <div class="content-img">
            <img :src="fileBase64" alt="">            
          </div>
          <span class="tips">*长按可保存主题图片</span>
        </div>
      </div>     

      <div class="bottom-box">
        <div class="phone-box flex-c-b">
          <span>是否显示手机号</span>
          <van-switch v-model="checkedPhone" @change="changeChecked" size="24px" />
        </div>
        <div class="btn-box flex-c-c">
          <van-button @click="handleClose">关 闭</van-button>
          <!-- <van-button class="save-btn" @click="downloadQrCode">保 存</van-button> -->
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { commonApi } from "@/api"
import {addBase64Header} from "@/utils/fileUtil"
import { generatorQrcode } from "@/utils/qrcode"

export default {
  name: "theme-page",
  props: {
    baseInfo: {
      type: Object,
      require: true
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      searchParams: {
        page: 0,
        size: 3
      },
      refreshing: false, //下拉刷新

      showPopup: false, //弹窗
      selectInfo: {},
      checkedPhone: false,

      qrcode: null,
      fileBase64: "",
      simpleFileBase64: "",
      wholeFileBase64: ""
    }
  },
  async created() {
    await this.$nextTick()
    const serviceName = process.env.VUE_APP_PUBLIC_PATH
    const {rygh00} = this.baseInfo
    const contentText = `${window.location.origin + serviceName}3EDCWSX/yilubao?rygh=${rygh00}` //智慧人社规范 #号使用3EDCWSX替代    
    this.qrcode = generatorQrcode(contentText, "", this.$refs.qrCodeUrl)
  },
  methods: {
    addBase64Header,
    onLoad() {
      this.searchParams.page++
      this.findDa10ByPage()
    },

    findDa10ByPage() {
      const {daz007} = this.baseInfo
      const params = {
        serviceName: "xytDa10_findDa10ByPage",
        daz007,
        ...this.searchParams
      }
      commonApi.proxyApi(params).then((res) => {
        if (this.refreshing) { // 清空列表数据
          this.list = []
          this.refreshing = false
        }

        const {rows=[], total=0} = res.map.data
        this.list = [...this.list, ...rows]

        this.loading = false

        if (this.list.length === total) {
          this.finished = true
        }
      }).catch(() => {
        this.loading = false
        this.finished = true
      })
    },

    // 下拉刷新
    onRefresh() {
      // 清空列表数据
      this.finished = false

      // 重新加载数据
      this.loading = true
      this.searchParams.page = 0
      this.onLoad()
    },

    // 分享
    async handleClickShare(data) {
      const {zttp00} = data
      this.fileBase64 = addBase64Header(zttp00)
      this.selectInfo = data
      this.showPopup = true

      await this.$nextTick()
      this.qrBase64 = this.$refs.qrCodeUrl.children[1].toDataURL("image/png", 0.5)
      this.mergeImages()
    },

    // 合成二维码主题图片
    mergeImages() {
      const canvas = document.createElement("canvas")
      const ctx = canvas.getContext("2d")
      const img = new Image()
      const qrImg = new Image()
 
      img.onload = () => {
        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0) // 绘制图片
 
        qrImg.onload = () => {
          const x = canvas.width - qrImg.width - 74 // 插入二维码的位置为图片的右下角，且二维码大小为qrImg.width x qrImg.height
          const y = canvas.height - qrImg.height - 92
          ctx.drawImage(qrImg, x, y, qrImg.width, qrImg.height) // 绘制二维码

          this.simpleFileBase64 = canvas.toDataURL() // 导出合成后的base64图片
          this.fileBase64 = this.simpleFileBase64
          this.mergePhone()
        }
        qrImg.src = this.qrBase64        
      }
      img.src = this.fileBase64
    },
    // 合成手机号
    mergePhone() {
      const canvas = document.createElement("canvas")
      const ctx = canvas.getContext("2d")
      const img = new Image()
 
      img.onload = () => {
        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0) // 绘制图片

        // 设置字体样式
        ctx.font = "22px Arial"
        ctx.fillStyle = "#565951" // 字体颜色
        ctx.textAlign = "right"
        ctx.className = "phone-block"

        // 计算文本的位置
        const {aae005} = this.baseInfo
        const text = `手机号码: ${aae005}` // 要绘制的手机号码
        // const textWidth = ctx.measureText(text).width
        // const x = canvas.width / 2 + textWidth / 2
        const y = canvas.height - 20 // 30像素离底部
        // 绘制文本
        ctx.fillText(text, 306, y)
        this.wholeFileBase64 = canvas.toDataURL() // 导出合成后的base64图片
      }
      img.src = this.fileBase64          
    }, 
    // 是否显示手机号
    changeChecked(val) {
      this.fileBase64 = val ? this.wholeFileBase64 : this.simpleFileBase64
    },
    // 下载二维码
    downloadQrCode() {
      const a = document.createElement("a")
      a.href = this.fileBase64
      const {aac003, rygh00} = this.baseInfo
      a.download = aac003 + "_" + rygh00 + ".png"
      a.click()
    },
    // 关闭
    handleClose() {
      this.showPopup = false
      this.checkedPhone = false
    }
  }
}
</script>
<style lang="less" scoped>
.theme-page {
  background: @main_bg_color;
  padding: 16px 16px 90px;
  min-height: calc(100vh - 90px);
  .card-box {
    padding: 14px 34px 14px 14px;
    margin-bottom: 16px;
    position: relative;
    & > img {
      width: 38px;
      height: 38px;
    }
    .content-box {
      flex: 1;
      margin-left: 8px;      
      color: @main_text_color;
      padding: 0;
      .content-title {
        font-weight: bold;
        font-size: 16px;        
        height: 42px;
        width: 100%;
        .title {
          line-height: 20px;
        }
      }
      .content-text {
        margin-top: 14px;
        font-size: 14px;
        line-height: 20px;        
      }
      .content-img {
        margin-top: 14px;
        & > img {
          width: 100%;
        }
      }
      .tips {
        display: inline-block;
        width: 249px;
        font-size: 12px;
        color: @warn_color;
        line-height: 17px;
        text-align: center;
      }
    }
    .share-btn {
      position: absolute;
      top: 20px;
      right: 12px;
      width: 24px;
      height: 20px;
      & > img {
        width: 100%;
        height: 100%;
      }
    }
    &-popup {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      flex-wrap: wrap;
      padding: 14px 0 14px 14px;
      .content-box {
        .content-img {
          width: 249px;
          height: 443px;
          position: relative;
          & > img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .theme-popup {
    height: 100vh;
    border-radius: 0;
    background: #F5F7FA;
    padding: 16px 16px 120px;
    .bottom-box {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 112px;
      background: #FFFFFF;
      box-shadow: 0px -1px 8px 0px rgba(186,186,186,0.32);
      padding: 12px 18px;
      .phone-box {
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        /deep/.van-switch {
          width: 48px;
          height: 24px;
          .van-switch__node {
            width: 24px;
            height: 24px;
          }
        }
        /deep/.van-switch--on {
          background-color: @ylb_color;
          .van-switch__node{
            transform: translateX(24px);
          }
        }
      }
      .btn-box {
        padding: 12px 0 0;
        .van-button {
          width: 164px;
          height: 44px;
          font-size: 16px;
          font-weight: 500;
          line-height: 44px;
          border-radius: 22px;          
          &--info {
            border-radius: 22px;
            color: @main_color;
          }          
        }
        .save-btn {
          background-color: @ylb_color;
          color: #FFFFFF;
        }
      }
    }
  }
}

</style>