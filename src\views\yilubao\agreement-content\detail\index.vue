<!--
 * @Description: 保险条例
 * @Version: 0.1
 * @Autor: T
-->
<template>
  <div class="page-wrapper">
    <div class="wrapper-box" v-for="(item, index) in list" :key="index">
      <!-- 折叠方式 -->
      <template v-if="current==='1' || item.type === 'collapse'">
        <van-collapse v-model="activeNames" :border="false" accordion>
          <van-collapse-item :title="item.title" :name="index" title-class="custom-item">
            <div class="detail" v-for="(subItem, subIndex) in item.contents" :key="`${index}-${subIndex}`">
              <template v-if="item.type === 'collapse'">{{ subItem }}</template>
              <y-pdf v-else :eid="`${pdf}${index}-${index}`" :base64Pdf="subItem" :key="`${index}-${subIndex}`" />
            </div>
          </van-collapse-item>
        </van-collapse>
      </template>
      <!-- 普通展开方式 -->
      <template v-else>
        <y-title :content="item.title" :background-color="ylb_color"  />
        <div class="detail" v-for="(subItem, subIndex) in item.contents" :key="`${index}-${subIndex}`">
          <!-- {{ subItem }} -->
          <p v-html="subItem"></p>
        </div>
      </template>
    </div>
    
    <div v-if="$attrs.btnVisible" class="botton-btn ylb-btn" ref="bottomBtn">
        <van-button round block type="primary" @click="goBack">返回</van-button>
        <van-button round block type="primary" :disabled="!!time" @click="handleFinish">
          已阅读完毕
          <template v-if="time">
            {{ `${time}s` }}
          </template>
        </van-button>
    </div>
  </div>
</template>

<script>
import {agreemeData, agreemeData1, agreemeData2, agreemeData3, agreemeData4} from "./data"

import {ylb_color} from "@/styles/theme/theme-params.less"
import YPdf from "@/components/plugins/y-pdf"
export default {
  name: "agreement-detail",
  components: {
    YPdf
  },
  props: {
    current: {
      type: [String, Number],
      default: "0"
    }
  },
  data() {
    return {
      ylb_color,
      list: [],
      activeIndex: 0,
      readList: [], // 已阅读系类
      activeNames: [],
      time: 5,
      timer: null
    }
  },
  watch: {
    current: {
      immediate: true,
      handler(val) {
        this.setAgreementData()

      }
    }
  },
  created() {
    this.setAgreementData()
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    /**
     * @description: 返回上个条例
     * @param {*}
     * @return {*}
     * @author: T
     */    
    goBack() {
      let index = this.current
      --index
      if (index > -1) {
        this.$emit("update:current", `${index}`)
      } else {
        this.$router.go(-1)
      }
    },
    /**
     * @description: 阅读校验
     * @param {*}
     * @return {*}
     * @author: T
     */    
    handleAcitve(index) {
      this.activeIndex = index
    },
    handleFinish() {
      if (this.time) {
        return
      }
      
      this.time = 5
      const { text, id } = this.$attrs["tab-info"]
      this.$toast(`${text}阅读完毕`)
      
      const readList = [...this.readList]
      console.log(readList, "readList")
      if (!readList.includes(id)) {
        readList.push(id)
        this.readList = readList
      }
      console.log(readList, "readList-end")
      
      this.$emit("readFinish", `${+id + 1}`)

      if (this.readList.length === 4) {
        this.$router.push({
          path: "/yilubao/order",
          query: {
            ...this.$route.query
          }
        })
      }
      
    },
    setAgreementData() {
      const agreementMap = {
        0: agreemeData,
        1: agreemeData1,
        2: agreemeData2,
        3: agreemeData3,
        4: agreemeData4
      }
      this.setCount()
      this.list = agreementMap[this.current] || []
    },
    setCount() {
      this.time = 5
      clearInterval(this.timer)
      this.timer = setInterval(() => {
        this.time = --this.time
        if (this.time === 0) {
          clearInterval(this.timer)
          
        }
      }, 1000)
    }
  }
}
</script>

<style lang="less" scoped>
.page-wrapper {
  background-color: #F5F6F6;
  padding: 17px 16px;
  height: 100%;
  .wrapper-box {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px 16px;
    margin-bottom: 12px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #333333;
    .detail, .dropdown-menu {
      font-size: 14px;
      line-height: 20px;
    }
    
  }
 
  .botton-btn {
    position: fixed;
    left: 0;
    bottom: 0;
    padding: 8px 16px 16px;
    width: 100%;
    background: #fff;
    
    box-shadow: 0px -1px 8px 0px rgba(186,186,186,0.32);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    
    .van-button {
      flex: 1;
      &:last-child {
        margin-left: 12px;
      }
    }
    
    .escape-clause {
      padding-left: 8px;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #333333;
      margin-bottom: 16px;
      line-height: 1.6;
      .link {
        color: @ylb_color;
      }
    }
  /deep/.van-checkbox__icon--checked .van-icon {
    color: #fff;
    background-color: @ylb_color;
    border-color: @ylb_color;
  }
  }
  
}
::v-deep.van-collapse {
    position: relative;
    top: 0;
    left: 0;
    .custom-item {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      span {
        flex: 1;
        width: 200px;
        white-space: normal;
        word-break: break-all;
      }
    }
    .van-icon-arrow {
      margin-left: 0;
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-40%);
    }
  }
</style>