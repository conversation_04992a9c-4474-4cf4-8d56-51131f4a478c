<!--
 * @Description: 新就业形态劳动者技能证书信息
 * @Version: 0.1
 * @Autor: hwx
-->

<template>
  <div class="skill-record-query">
    <div class="record-box" v-if="list.length > 0">
      <div class="record-item" v-for="(item,index) in list" :key="index">
        <div class="item-header flex-c-s">
          <p>{{item.xmlx00}}</p>
        </div>

        <van-cell-group class="show-cell-group">
          <van-cell title="培训年份" :value="item.nf0000" />
          <van-cell title="培训工种" :value="item.gzmc00" />
          <van-cell title="培训等级" :value="item.dj0000" />
          <van-cell title="证书编号" :value="item.zsbh00" />
          <van-cell title="发证时间" :value="item.fzrq00" />
          <van-cell title="培训企业" :value="item.aab004" />
        </van-cell-group>
      </div>
    </div>

    <y-empty v-else tips="暂无培训记录"></y-empty>

  </div>
</template>

<script>
import { commonApi } from "@/api"
export default {
  name: "skill-record-query",
  data() {
    return {
      list: []
    }
  },
  computed: {
    userInfo() {
      const {xm0000: aac003, zjhm00: aac002} = this.$sessionUtil.getItem("userInfo")
      return {aac003, aac002}
    }
  },
  created() {
    this.getXytpxqk()
  },
  methods: {
    getXytpxqk(){
      const {aac002} = this.userInfo
      commonApi.proxyApi({
        serviceName: "xytJnpx_getXytpxqk",
        aac002
      }).then((res) => {
        console.log("培训记录", res)
        this.list = res.map?.data || []
      })
    }
  }
}
</script>
<style lang="less" scoped>
.skill-record-query {
  padding: 16px;
  background: #F6F6F6;
  min-height: 100vh;
  .record-box {
    .record-item {
      margin-bottom: 16px;
      .item-header {
        width: 100%;
        height: 48px;
        background-image: url("~@/assets/imgs/skill-record-query/<EMAIL>");
        background-size: 100% 100%;
        padding-left: 16px;
        & > p {
          font-weight: 500;
          font-size: 16px;
          color: #FFFFFF;
          line-height: 16px;
        }
      }

    }
  }
}
</style>