<!--
 * @Description: 劳动监察--新增
 * @Version: 0.1
 * @Autor: hwx
-->
<template>
  <div class="labor-supervision-handle common-container">
    <!-- 步骤条 -->
    <y-society-steps class="top-header" :handleList="handleList" :active="active"></y-society-steps>
   
    <!-- 特务类型 -->
    <van-cell v-show="active === 0" class="business-cell" title="业务类型" value="请选择" v-model="formData.oprTypeName" @click="handleClickBusinessType" is-link/>

    <!-- 说明 -->
    <div class="explain-box flex-c-c">
      <div class="explain-button flex-c-c" @click="handleExplain">
        <img :src="require('@/assets/imgs/labor-protect/<EMAIL>')" alt="">
        <span>{{ formData.oprTypeName }}说明</span>
      </div>
    </div>

    <!-- 申请信息 -->
    <apply-info ref="applyInfo" v-show="active === 0" :businessType="formData.oprType" @handleNext="handleNext" @updateFormData="updateFormData" @handleSave="handleSave"></apply-info>

    <!-- 提交材料 -->
    <submit-material ref="submitMaterial" v-show="active === 1" :active="active" :businessType="formData.oprType" @handleNext="handleNext" @handleSubmit="handleSubmit" @handleSave="handleSave"></submit-material>

    <!-- 业务类型选择弹窗 -->
    <van-popup v-model="showBusinessType" position="bottom">
      <van-picker          
        title="业务类型"
        show-toolbar
        :columns="columns"
        @confirm="confirmBusinessType"
        @cancel="showBusinessType = false"
      />
    </van-popup>

    <!-- 说明弹窗 -->
    <van-popup v-if="showExplain" class="explain-popup" v-model="showExplain" width="100%" position="center">
      <div class="iframe-box">        
        <iframe id="iframe" class="my-iframe" :src="iframeUrl" frameborder="0"></iframe>
        <div class="flex-c-c">
          <van-button plain type="info" @click="showExplain = false" native-type="button" size="mini">
          关 闭
        </van-button>
        </div>
      </div>
    </van-popup>
    
  </div>
</template>

<script>
import ApplyInfo from "./cpns/apply-info"
import SubmitMaterial from "./cpns/submit-material"

import {commonApi} from "@/api"

const EXPLAIN_TYPE = {
  "xztj": "xztj",
  "else": "tous",
  "juBao": "jbao"
}

const BUSINESS_TYPE = {
  "1": "投诉",
  "2": "举报",
  "3": "行政调解"
}

export default {
  name: "labor-supervision-handle",
  components: {
    ApplyInfo,
    SubmitMaterial
  },
  data() {
    return {
      active: 0,  
      handleList: [
        "申请信息",
        "提交材料"
      ],

      // 业务类型
      showBusinessType: false,
      columns: [
        { text: "行政调解", value: "xztj" },
        { text: "投诉", value: "else" },
        { text: "举报", value: "juBao" }
      ],

      // 表单
      formData: {
        oprType: "xztj", //业务类型 值
        oprTypeName: "行政调解" //业务类型 默认行政调解
      },

      showExplain: false, //说明弹窗
      iframeUrl: "" //说明弹窗地址
    }
  },
  watch: {
    active() {
      this.scrollToTop(0, 0, "auto")
    }
  },
  computed: {
    userInfo() {
      return this.$sessionUtil.getItem("userInfo")
    },
    pageType() {
      return this.$route.query.pageType || ""
    },
    businessType() {
      return this.$route.query.aae391
    },
    primaryKey() {
      return this.$route.query.id || ""
    }
  },
  created() {    
    // 编辑和查看详情 查询已保存信息
    if (this.pageType === "add") {
      return
    }

    const {text: oprTypeName, value: oprType} = this.columns.find(item => item.text === BUSINESS_TYPE[this.businessType]) //业务类型
    this.formData = {oprTypeName, oprType}
    this.getBaseInfo()
  },
  mounted() {    
    if (window.history && window.history.pushState) {
      if (window.history.length>1){
        const state = {
          key: Math.random() * new Date().getTime()
        }
        window.history.pushState(state, null, document.URL)
      }
      
      //给window添加一个监听事件popstate，拦截返回键，并执行方法 backFn
      window.addEventListener("popstate", this.backFn, false)
    }
  },
  methods: {
    // 点击业务类型
    handleClickBusinessType() {
      if (this.pageType === "detail") { //详情页面禁用
        return
      }
      this.showBusinessType = true
    },
    // 选择业务类型
    confirmBusinessType(data) {
      const {text, value} = data
      this.formData.oprTypeName = text
      this.formData.oprType = value

      this.showBusinessType = false 
      console.log(this.formData, "this.formData111")     
    },

    // 更新表单数据
    updateFormData(data) {
      this.formData = { ...this.formData, ...data }
    },

    // 拦截返回键
    backFn() {
      // 未完成提交 改变active状态
      if (this.active === 1) {        
        this.active--
        const state = {
          key: Math.random() * new Date().getTime()
        }
        window.history.pushState(state, null, document.URL)        
        return
      }

      // 第一步或提交成功状态 返回上一页面
      this.$router.go(-1)      
    },

    // 下一步
    handleNext(active) {
      this.active = active
      this.scrollToTop(0, 0, "auto")
    },

    // 保存
    handleSave(type) {
      this.$dialog.confirm({
        title: "提示",
        message: "您确定保存信息为待提交状态",
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消"
      }).then(() => {
        const abz200 = this.$refs.submitMaterial.materialNum
        const qmzp00Base64 = this.$refs.signConfirm.signature
        const mapObj = {
          "info": {...this.formData},
          "material": {...this.formData, abz200},
          "sign": {...this.formData, abz200, qmzp00Base64}
        }

        const params = mapObj[type]
        delete params.createTime
        delete params.updateTime
        params.serviceName = "xytBc01_saveOrUpdateBc01"
        commonApi.proxyApi(params).then((res) => {
          this.$toast("保存成功！")
          setTimeout(() => {
            this.$router.go(-2)
          }, 300)
        })
      }).catch(() => { })   
    },

    // 提交
    async handleSubmit(materialInfo) {
      const baseForm = this.$refs.applyInfo.$refs.baseFormInfo.formData
      const {projectname, aab013, aae406} = baseForm
      const abb922 = this.dayFormatFn(new Date(), "YYYYMMDD") //投诉日期
      const params = {
        serviceName: "jftjsq_saveInfo",        
        ...baseForm,
        ...this.formData,
        ...materialInfo,

        projectname_ts: projectname, //projectname和projectname_ts与site_project都传 建筑工程项目 
        site_project: projectname,
        aac008: aab013, //单位联系人
        abb282: baseForm.aac152, //现通信地址 接口会校验该字段必填 约定这样传
        abb922, //投诉日期
        dqdwdz: aae406, //被申请单位现实际地址|实际经营地址
        abz200: materialInfo.dzd999, //电子文档id
        abb285: "18", //新就业形态来源 
        abb289: "1" //数据来源
      }            
      const res = await commonApi.proxyApi(params) //提交接口

      const message = res.map.msg || "提交成功！"
      this.$dialog.confirm({
        title: "提示",
        message,
        showCancelButton: false,
        confirmButtonText: "确认"
      }).then(() => {
        this.$router.go(-2)
      })      
    },

    // 详情页面返回
    handleRouterBack() {
      this.$router.go(-1)
    },

    // 详情页面查询案件信息
    getBaseInfo() {
      const {id, aae391} = this.$route.query
      const params = {
        serviceName: "jftjsq_enterCheck",
        id,
        aae391
      }
      commonApi.proxyApi(params).then((res) => {
        console.log(res, "案件信息")
        const {objMap} = res.map
        for (const key in objMap) {
          objMap[key] === null && (objMap[key] = "")
        }

        // 基本信息
        this.formData = {...this.formData, ...objMap}
        const {aac004, projectname, site_project, abb304, aab013, aac008, abb282} = objMap
        objMap.aac004 = aac004 === "0" ? "2" : "1" //表不一样 字典需转化
        objMap.projectname = projectname || site_project //建筑工程项目
        objMap.sfnm = abb304 //是否实名
        objMap.aab013 = aab013 || aac008 //单位联系人
        objMap.aac152 = abb282 //现通讯地址
        
        this.$refs.applyInfo.$refs.baseFormInfo.formData = {...objMap}
        this.$refs.applyInfo.$refs.baseFormInfo.formRules = {}

        // 材料信息
        this.$refs.submitMaterial.formData = {...objMap}
        this.$refs.submitMaterial.getArchivesPhoneData(objMap.dzd999) //获取材料
      })
    },
    // 查看说明
    handleExplain() {
      const type = EXPLAIN_TYPE[this.formData.oprType]
      this.iframeUrl = `${process.env.VUE_APP_EXPLAIN_URL + type}`
      this.showExplain = true
    }
  },
  destroyed(){
    //销毁监听
    window.removeEventListener("popstate", this.backFn, false)
  }
}
</script>

<style lang="less" scoped>
.labor-supervision-handle {
  .business-cell .van-cell__value {
    color: @main_text_color;
  }
  .explain-box {    
    .explain-button {
      width: 320px;
      height: 40px;
      background: #F8E8EA;
      border-radius: 20px;
      margin: 14px 0;
      &>img {
        width: 18px;
        height: 20px;
      }
      &>span {
        font-size: 14px;
        color: @main_color;
        line-height: 40px;
        margin-left: 12px;
        font-weight: bold;
      }
    }
  }
  .explain-popup {
    border-radius: 4px;
    width: 96vw;
    height: 95vh;
    .iframe-box {
      & > iframe {
        width: 96vw;
        height: calc(95vh - 40px);
        margin-bottom: 4px;
      }
      .van-button {
        padding: 0 8px;
      }
    }
  }
  
}

</style>